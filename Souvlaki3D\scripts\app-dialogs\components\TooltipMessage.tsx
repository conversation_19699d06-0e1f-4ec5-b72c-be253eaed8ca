import { dom } from "@immugio/jsx-and-html-utils";
import { translate } from "../services/Translator";

interface Props {
    message: string;
    style?: Partial<CSSStyleDeclaration>;
}

export type TooltipMessage = HTMLElement & {
    show: () => void;
    hide: () => void;
    message: string;
}

export function TooltipMessage({ message, style }: Props): TooltipMessage {
    let visible = false;

    const tooltip: TooltipMessage = (
        <div style={style} className="tooltip-message">
            {translate(message)}
        </div>
    );

    tooltip.show = () => {
        tooltip.style.display = "block";
        visible = true;
    };

    tooltip.hide = () => {
        tooltip.style.display = "none";
        visible = false;
    };

    Object.defineProperty(tooltip, "message", {
        get: () => tooltip.innerHTML,
        set: (value: string) => tooltip.innerHTML = translate(value)
    });

    document.body.addEventListener("mousemove", (e: MouseEvent) => {
        if (visible) {
            tooltip.style.left = `${e.clientX}px`;
            tooltip.style.top = `${e.clientY + 25}px`;
        }
    });

    return tooltip;
}