﻿import { Size } from "../../models/geometry/Size";
import { SizedMaterial } from "@immugio/controls";
import { Color, Mesh, MeshStandardMaterial, MeshStandardMaterialParameters, Object3D, TextureLoader } from "three";
import { settings } from "../../configurations/setup_global";

export function getMaterial(key: string, cacheGroup: string = ""): SizedMaterial<MeshStandardMaterial> {
    return key ? MaterialUtils.getMaterial(key, true, cacheGroup) : null;
}

const textureLoader = new TextureLoader();

export class MaterialUtils {

    private static materials: { [key: string]: MeshStandardMaterial } = {};

    public static getMaterial(key: string | number, useCache: boolean = true, cacheGroup: string = ""): SizedMaterial<MeshStandardMaterial> {
        if (!useCache) {
            return this.createMaterial(key);
        }

        const cacheKey = `_${key}_${cacheGroup}`;

        if (!this.materials.hasOwnProperty(cacheKey)) {
            this.materials[cacheKey] = this.createMaterial(key);
        }

        return this.materials[cacheKey];
    }

    private static createMaterial(key: string | number): SizedMaterial<MeshStandardMaterial> {
        const params: MeshStandardMaterialParameters = {
            name: key.toString(),
        };

        const data = settings.textures[key];

        if (data) {
            if (data.texture) params.map = textureLoader.load(data.texture);
            if (data.transparent !== undefined) params.transparent = data.transparent;
            if (data.opacity !== undefined) params.opacity = data.opacity;
            if (data.color !== undefined) params.color = data.color;
            if (data.depthTest !== undefined) params.depthTest = data.depthTest;
            if (data.metalness !== undefined) params.metalness = data.metalness;
            if (data.roughness !== undefined) params.roughness = data.roughness;
            if (data.side !== undefined) params.side = data.side;

        } else {
            params.color = key;
        }

        const material: SizedMaterial<MeshStandardMaterial> = new MeshStandardMaterial(params);

        if (data) {
            if (data.physicalWidth && data.physicalHeight)
                material.userData = new Size(data.physicalWidth, data.physicalHeight);

            if (data.wrapX !== undefined && material.map)
                material.map.wrapS = data.wrapX as number;

            if (data.wrapY !== undefined && material.map)
                material.map.wrapT = data.wrapY as number;

            if (data.repeat !== undefined) {
                material.map.repeat.set(data.repeat, data.repeat);
            }
        }

        material.textureSize = new Size(data?.physicalWidth || 1, data?.physicalHeight || 1);

        return material;
    }

    public static updateMaterial(object: Object3D, materialKey: string, color: string | number): void {
        const material = this.getMaterial(materialKey);
        object.traverse(o => {
            if (o instanceof Mesh && o.material instanceof MeshStandardMaterial) {
                o.material.name = materialKey + color;
                o.material.color = new Color(color);
                o.material.side = material.side;
                o.material.metalness = material.metalness;
                o.material.roughness = material.roughness;
            }
        });
    }
}