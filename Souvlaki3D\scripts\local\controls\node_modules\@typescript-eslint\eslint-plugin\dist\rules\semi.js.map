{"version": 3, "file": "semi.js", "sourceRoot": "", "sources": ["../../src/rules/semi.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,8EAI+C;AAC/C,iEAA6C;AAC7C,8CAAgC;AAKhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,+CAA+C;YAC5D,QAAQ,EAAE,kBAAkB;YAC5B,oCAAoC;YACpC,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,MAAM;QACf,MAAM,EAAE,cAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ,EAAE,cAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE;QACd,QAAQ;QACR;YACE,sBAAsB,EAAE,KAAK;YAC7B,gCAAgC,EAAE,KAAK;SACxC;KACF;IACD,MAAM,CAAC,OAAO;QACZ,MAAM,KAAK,GAAG,cAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,iBAAiB,GAAG,KAAK,CAAC,mBAE/B,CAAC;QAEF;;;;;;;UAOE;QACF,MAAM,YAAY,GAAG;YACnB,mCAAc,CAAC,aAAa;YAC5B,mCAAc,CAAC,uBAAuB;YACtC,mCAAc,CAAC,0BAA0B;YACzC,mCAAc,CAAC,iBAAiB;YAChC,mCAAc,CAAC,kBAAkB;YACjC,mCAAc,CAAC,yBAAyB;YACxC,mCAAc,CAAC,sBAAsB;SACtC,CAAC,MAAM,CAAwB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAC5C,GAAG,CAAC,IAAc,CAAC,GAAG,iBAAiB,CAAC;YACxC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,qDACK,KAAK,GACL,YAAY,KACf,wBAAwB,CAAC,IAAI;gBAC3B,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,mCAAc,CAAC,sBAAsB,EAAE;oBACnE,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;iBACtC;YACH,CAAC,IACD;IACJ,CAAC;CACF,CAAC,CAAC"}