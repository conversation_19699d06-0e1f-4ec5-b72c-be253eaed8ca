import { MessageHub } from "../../../infrastructure/MessageHub";
import { Events } from "../../../models/infrastructure/Events";
import { IToolbarActionOptions } from "./IToolbarActionOptions";

export abstract class BaseActionController {

    public element: HTMLElement;

    protected constructor(protected options: IToolbarActionOptions) {
    }

    public defaultInitialize(): void {
        this.element = document.querySelector(this.options.selector);
        if (this.element instanceof HTMLElement) {
            this.element.addEventListener("click", () => this.execute());
            MessageHub.subscribe([Events.pushState, Events.itemSelected, Events.drawingInteraction, Events.setState, Events.updateViewMode], () => this.update());

        } else {
            console.error(`ToolbarAction: element with selector "${this.options.selector}" not found`);
        }

        if (this.options.shortcut) {
            document.addEventListener("keyup", (e) => {
                if (e.code === this.options.shortcut && (e.ctrl<PERSON><PERSON> || e.meta<PERSON>ey)) {
                    this.execute();
                }
            });
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    public execute(): void {
    }

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    public update(): void {
    }
}