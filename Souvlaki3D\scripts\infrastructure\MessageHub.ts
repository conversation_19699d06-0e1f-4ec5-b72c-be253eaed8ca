export class MessageHub {

    private static subscribers: { [channel: string]: ((message?) => void)[] } = {};

    public static subscribe<T>(channels: string | string [], subscriber: (message?: T) => void, start?: boolean): void {
        (Array.isArray(channels) ? channels : [channels]).forEach(channel => {
            if (!this.subscribers[channel]) {
                this.subscribers[channel] = [];
            }

            start ? this.subscribers[channel].unshift(subscriber)
                : this.subscribers[channel].push(subscriber);
        });
    }

    public static broadcast<T>(channel: string, message?: T): void {
        console.log("Event: " + channel);

        if (!this.subscribers[channel]) {
            return;
        }

        this.subscribers[channel].forEach(x => x(message));
    }

    public subscribe<T>(channel: string | string [], subscriber: (message?: T) => void, start?: boolean): void {
        MessageHub.subscribe(channel, subscriber, start);
    }

    public broadcast<T>(channel: string, message?: T): void {
        MessageHub.broadcast(channel, message);
    }
}