import { IApplicationState } from "../../models/state/IApplicationState";
import { BaseController } from "./BaseController";
import { Scene } from "../../models/scene/Scene";
import { IPoint2D } from "../../models/geometry/IPoint2D";
import { IInteractionOptions } from "../../models/positioning/IInteractionOptions";
import { Interactive3D } from "../../models/scene/Interactive3D";
import { MessageHub } from "../../infrastructure/MessageHub";
import { Events } from "../../models/infrastructure/Events";
import { SelectionBox } from "../../utils/SelectionBox";
import { ITapInfo } from "../../models/menu/ITapInfo";
import { Object3D, Renderer, Scene as ThreeScene } from "three";

export class SelectController extends BaseController {

    public static $inject = ["scene", "MessageHub", "StateService"];

    private selectionBox: SelectionBox;

    private startPoint: IPoint2D;
    private pointTopLeft: IPoint2D;
    private pointBottomRight: IPoint2D;

    private labelPoint: IPoint2D;

    private element: HTMLDivElement;
    private renderer: Renderer;
    private isDown: boolean;

    constructor(private scene: Scene, private messageHub: MessageHub, private state: IApplicationState) {
        super();

        this.selectionBox = new SelectionBox(scene.camera, scene.control.object3D as ThreeScene);
        this.initialize();

        document.addEventListener("mousedown", (event) => {
            if (this.state.view.rectangleSelect) {
                this.isDown = true;
                this.selectBoxShow(event); // Show html box

                const c = this.coordsFromEvent(event); // Reset select engine
                this.selectionBox.startPoint.set(c.x, c.y, 0.5);
                this.selectionBox.endPoint.set(c.x, c.y, 0.5);

                messageHub.broadcast<ITapInfo>(Events.groupSelection, null); // Let the app know
            }
        });

        document.addEventListener("mousemove", (event) => {
            if (this.isDown) {
                this.selectBoxUpdate(event); // Update html box
                this.select(event); // Update select engine and let the app know what is selected
            }
        });

        document.addEventListener("mouseup", (event) => {
            if (this.isDown) {
                this.selectBoxHide(); // Hide html box
                this.select(event); // Update final selecting and let the app know
                this.state.view.rectangleSelect = false;
                this.messageHub.broadcast(Events.updateToolbar);
                this.isDown = false;
            }
        });
    }

    private select(event: MouseEvent): void {
        const c = this.coordsFromEvent(event);
        this.selectionBox.endPoint.set(c.x, c.y, 0.5);

        const selected = this.selectionBox.select();
        const controls = this.findControls(selected);

        this.messageHub.broadcast<ITapInfo>(Events.groupSelection, { ...this.labelPoint, group: controls });
    }

    private coordsFromEvent(event: MouseEvent): IPoint2D {
        const scene = this.scene;

        const eltx = event.pageX - scene.offset.x; // Translate page coords to element coords
        const elty = event.pageY - scene.offset.y;

        const x = (eltx / scene.renderer.domElement.clientWidth) * 2 - 1; // Translate client coords into viewport x,y
        const y = - (elty / scene.renderer.domElement.clientHeight) * 2 + 1;

        return { x, y };
    }

    private findControls(objects: Object3D[]): IInteractionOptions[] {
        return objects
            .map(x => this.findInteraction(x))
            .reduce((acc, cur) => {
                if (cur && acc.every(e => e.movable.id !== cur.movable.id)) {
                    acc.push(cur);
                }
                return acc;
            }, new Array<IInteractionOptions>());
    }

    private findInteraction(object3D: Object3D): IInteractionOptions {
        const interaction = (object3D as Interactive3D).interaction;

        return (interaction && interaction.movable)
            ? interaction
            : object3D.parent
                ? this.findInteraction(object3D.parent)
                : undefined;
    }

    private initialize(): void {
        this.element = document.createElement("div");
        this.element.classList.add("select-box");
        this.element.style.pointerEvents = "none";

        this.renderer = this.scene.renderer;

        this.startPoint = { x: 0, y: 0 };
        this.pointTopLeft = { x: 0, y: 0 };
        this.pointBottomRight = { x: 0, y: 0 };
        this.labelPoint = { x: 0, y: 0 };
    }

    private selectBoxShow(event: MouseEvent) {
        this.renderer.domElement.parentElement.appendChild(this.element);

        this.element.style.left = event.clientX + "px";
        this.element.style.top = event.clientY + "px";
        this.element.style.width = "0px";
        this.element.style.height = "0px";

        this.startPoint.x = event.clientX;
        this.startPoint.y = event.clientY;
    }

    private selectBoxUpdate(event: MouseEvent) {
        this.pointBottomRight.x = Math.max(this.startPoint.x, event.clientX);
        this.pointBottomRight.y = Math.max(this.startPoint.y, event.clientY);
        this.pointTopLeft.x = Math.min(this.startPoint.x, event.clientX);
        this.pointTopLeft.y = Math.min(this.startPoint.y, event.clientY);

        this.labelPoint.x = (this.pointTopLeft.x + this.pointBottomRight.x) / 2;
        const height = this.pointBottomRight.y - this.pointTopLeft.y;
        if (height > 200) {
            this.labelPoint.y = this.pointTopLeft.y + height / 2;
        } else {
            this.labelPoint.y = this.pointTopLeft.y;
        }

        this.element.style.left = this.pointTopLeft.x + "px";
        this.element.style.top = this.pointTopLeft.y + "px";
        this.element.style.width = (this.pointBottomRight.x - this.pointTopLeft.x) + "px";
        this.element.style.height = (this.pointBottomRight.y - this.pointTopLeft.y) + "px";
    }

    private selectBoxHide() {
        this.element.parentElement.removeChild(this.element);
    }
}