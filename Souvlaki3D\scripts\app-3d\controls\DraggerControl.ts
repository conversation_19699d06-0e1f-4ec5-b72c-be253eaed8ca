﻿import { Scene } from "../../models/scene/Scene";
import { IPoint2D } from "../../models/geometry/IPoint2D";
import { IApplicationState } from "../../models/state/IApplicationState";
import { Axis, Direction, InteractionType, PlacementMode } from "../../models/common/Enums";
import { IntersectionResult } from "../../models/positioning/IntersectionResult";
import { IPoint3D } from "../../models/geometry/IPoint3D";
import { ISnapPoint } from "../../models/positioning/ISnapPoint";
import { DesignState } from "../../models/state/DesignState";
import { MovableItem } from "../../models/state/MovableItem";
import { Zone } from "../../models/positioning/Zone";
import { cleanRotation, PositioningUtils } from "../../logic/helpers/PositioningUtils";
import { CollisionUtils } from "../../logic/helpers/CollisionUtils";
import { MovablesUtils } from "../../logic/helpers/MovablesUtils";
import { PolygonUtils } from "../../logic/helpers/PolygonUtils";
import { MathUtils } from "../../logic/helpers/MathUtils";
import { CanvasUtils } from "../utils/CanvasUtils";
import { MovableItemControl } from "./MovableItemControl";
import { SnapControl } from "../utils/SnapControl";
import { CanvasControl } from "./CanvasControl";
import { Box3, BufferAttribute, DoubleSide, Intersection, Mesh, MeshBasicMaterial, MeshStandardMaterial, PlaneGeometry, SphereGeometry, Vector3 } from "three";
import { getMovablePoints } from "../../logic/helpers/positioning/getMovablePoints";
import { Mesh3D } from "../graphics/Mesh3D";
import { MessageHub } from "../../infrastructure/MessageHub";
import { Events } from "../../models/infrastructure/Events";
import { SnapShift } from "../../models/positioning/SnapShift";

interface IDraggerControlOptions {
    positionPlaneColor: string;
    dragPlaneColor: string;
    snapDistance: number;
    wireframe: boolean;
    enabledSnapPointsColor: string;
    disabledSnapPointsColor: string;
}

export class DraggerControl extends CanvasControl {

    public static $inject = ["scene", "movableControls"];

    private positionPlanes: Mesh[] = [];
    private dragPlanes: Mesh[] = [];
    private planes: Mesh[] = [];

    private draggedObject: CanvasControl;
    private selectedControl: CanvasControl;
    private grouped: CanvasControl[];

    private floorOffset: IPoint2D;
    private initialRotation: IPoint3D;

    private snapPoints: ISnapPoint[];

    private indicator: Mesh;

    private taken: CanvasControl[];
    private room: Box3;

    constructor(private scene: Scene, private movableControls: MovableItemControl[], private options: IDraggerControlOptions) {
        super();

        this.indicator = DraggerControl.getIndicator("yellow");
        //this.object3D.add(this.indicator);

        MessageHub.subscribe(Events.requestDragEnd, () => this.endDrag());
    }

    private showSnapPoints(snaps: IPoint3D[], color: string, collides: boolean): void {
        for (const p of snaps || []) {
            const control = new SnapControl(p, DraggerControl.getIndicator(color));
            this.object3D.add(control.object3D);

            if (collides) {
                control.updateBoundingBox(50);
                this.taken.push(control);
            }
        }
    }

    public update(s: IApplicationState): void {
        if (s.view.activeItem) {

            if (!this.selectedControl) {

                switch (s.view.activeItem.interactionType) {

                    case InteractionType.Track:
                    case InteractionType.Attachment:
                    case InteractionType.DraggingItem:
                    case InteractionType.InsertingItem:
                        if (s.view.zones) {
                            this.selectedControl = this.movableControls.find(c => c.interaction.movable.id === s.view.activeItem.movable.id);
                            this.selectedControl.update(s);
                            if (s.view.activeItem.children) {
                                this.initialRotation = { x: s.view.activeItem.movable.rotationX, y: s.view.activeItem.movable.rotationY, z: s.view.activeItem.movable.rotationZ };
                                const ids = s.view.activeItem.children.map(x => x.movable.id);
                                this.grouped = this.movableControls.filter(x => ids.includes(x.interaction.movable.id));
                                CanvasUtils.mergeControls(this.selectedControl, this.grouped);
                            }
                            this.movableControls.forEach(x => {
                                x.mouseOut();
                                x.updateWordVertices();
                            });

                            // 0.5 mm tolerance in the room size is to allow for inaccuracies in data - some snap points are miss-aligned which can cause
                            // SMD items to collide with walls. When pushed back by wall collision detection, the snap is broken.
                            this.room = CollisionUtils.fromRoom(s.design, this.selectedControl.interaction.movable, 0.5);

                            const excluded = [...(this.grouped || []), this.selectedControl];
                            this.taken = this.movableControls.filter(x => !excluded.includes(x));
                            this.taken.forEach(x => x.updateBoundingBox());

                            this.beginDrag(s, this.selectedControl);
                        }
                        break;

                    case InteractionType.ResizeBuilding:
                    case InteractionType.ResizeHeight:
                        this.selectedControl = s.view.activeItem.control;
                        this.beginDrag(s, this.selectedControl);
                        break;
                }
            }

            this.drag(s.design, s.view.rawMousePosition);
        }

        if (!s.view.activeItem) {

            this.selectedControl = undefined;
            this.endDrag();
        }
    }

    private beginDrag(s: IApplicationState, control: CanvasControl): void {
        this.floorOffset = s.design.offset();
        this.object3D.position.set(-s.view.offset.x, 0, -s.view.offset.y);

        this.snapPoints = s.view.snaps;

        this.draggedObject = control;
        this.interaction = control.interaction;

        if (this.interaction.axis) {
            this.setupAxisPlane();
        } else {
            this.setupZonesPlanes(s.view.zones);
        }

        // Needs to be updated before dragging starts, otherwise the intersection will be initially in the wrong place
        this.object3D.updateMatrixWorld(true);

        this.setClickOffset(s);

        // Minimize dragging start delay
        setTimeout(() => {
            this.showSnapPoints(this.snapPoints, this.options.enabledSnapPointsColor, false);
            this.showSnapPoints(s.view.forbidden, this.options.disabledSnapPointsColor, true);
        }, 100);
    }

    private setClickOffset(s: IApplicationState) {
        if (this.interaction.interactionType !== InteractionType.DraggingItem) {
            return;
        }

        const planes = this.dragPlanes.filter(x => x.userData.placement === this.interaction.movable.placementMode && x.userData.rotationY === this.interaction.movable.rotationY);
        const intersect = CanvasUtils.intersectObjects(this.scene, s.view.rawMousePosition.x, s.view.rawMousePosition.y, planes)[0];

        if (intersect) {
            const zone = intersect.object.userData;
            if (zone instanceof Zone) {
                zone.clickOffset = new Vector3().subVectors(this.draggedObject.object3D.position, intersect.point);
                const plane = this.positionPlanes.find(x => x.userData === zone);
                if (zone.placement & (PlacementMode.Ceiling | PlacementMode.Walls)) {
                    plane.position.copy(zone.positionPlane.position);
                    plane.position.sub(zone.clickOffset);
                    plane.updateMatrixWorld(true);
                }
            }
        }
    }

    private setupAxisPlane(): void {
        const positionPlane = new Mesh(new PlaneGeometry(100000, 100000), new MeshBasicMaterial({ opacity: 0, transparent: true, wireframe: this.options.wireframe, visible: false }));

        if (this.interaction.axis === Axis.Y) { // Dragging things up and down
            positionPlane.rotation.x = 0;
            positionPlane.position.z = this.draggedObject.object3D.position.z;
            positionPlane.position.y = 0;

        } else { // Dragging things left, right, front, back
            positionPlane.rotation.x = -Math.PI / 2;
            positionPlane.position.y = this.draggedObject.object3D.position.y;
            positionPlane.position.z = 0;
        }

        this.positionPlanes.push(positionPlane);
        this.planes.push(positionPlane);

        this.object3D.add(positionPlane);
    }

    private setupZonesPlanes(zones: Zone[]): void {
        for (let i = 0; i < zones.length; i++) {
            const zone = zones[i];

            const position = zone.positionPlane;
            const positionPlane = new Mesh3D(
                new PlaneGeometry(position.width, position.height, 10, 10).rotateX(Math.PI / 2),
                new MeshBasicMaterial({ wireframe: this.options.wireframe, side: DoubleSide, color: this.options.positionPlaneColor, opacity: .5, transparent: true, visible: false })
            ).setRenderOrder(-1);

            positionPlane.position.copy(position.position);
            positionPlane.userData = zone;
            CanvasUtils.applyRotation(positionPlane, position);

            this.positionPlanes.push(positionPlane);
            this.object3D.add(positionPlane);

            const drag = zone.dragPlane;
            const dragPlane = new Mesh(
                new PlaneGeometry(drag.width, drag.height, 10, 10).rotateX(Math.PI / 2),
                new MeshBasicMaterial({ wireframe: this.options.wireframe, side: DoubleSide, color: this.options.dragPlaneColor, transparent: true, opacity: 0, visible: false })
            );

            dragPlane.position.copy(drag.position);
            dragPlane.userData = zone;
            CanvasUtils.applyRotation(dragPlane, drag);

            this.dragPlanes.push(dragPlane);
            this.object3D.add(dragPlane);

            this.planes.push(positionPlane, dragPlane);
        }
    }

    private drag(d: DesignState, p: IPoint2D): void {
        if (!this.draggedObject || !p) return;

        const m = this.draggedObject.interaction.movable;

        const r = this.getIntersection(p);
        if (!r) return;

        this.draggedObject.object3D.position.copy(r.snappedIntersectionPoint);

        if (m.placementMode !== PlacementMode.MinMaxAxes) {
            Object.assign(m, r.movable);
            m.placementMode = r.zone.placement;
            m.x = r.snappedIntersectionPoint.x + this.floorOffset.x;
            m.z = r.snappedIntersectionPoint.z + this.floorOffset.y;
            m.y = r.snappedIntersectionPoint.y;

            this.draggedObject.object3D.rotation.set(0, 0, 0);
            CanvasUtils.applyRotation(this.draggedObject.object3D, m);

            if (m.placementMode & (PlacementMode.Ceiling | PlacementMode.Horizontal)) {
                m.ceilingDistance = d.height - d.roofWidth - m.height / 2 - m.y;
                m.floorDistance = undefined;
            }

            this.collision(r.snapShifts.length > 0);
        }
    }

    private getIntersection(p: IPoint2D): IntersectionResult {
        const intersections = this.getPlaneIntersections(p.x, p.y);
        if (!intersections || !intersections.length) return;

        const results: IntersectionResult[] = [];

        for (let i = 0; i < intersections.length; i++) {
            const intersection = intersections[i];
            const zone = intersection.object.userData as Zone;

            const movable = this.draggedObject.interaction.movable.clone();
            movable.rotationY = zone.rotationY;

            const intersectionPoint = intersection.point.clone();
            if (zone.clickOffset) {
                intersectionPoint.add(zone.clickOffset);
            }

            results.push({
                zone: zone,
                snappedIntersectionPoint: intersectionPoint,
                intersectionPoint: intersectionPoint.clone(),
                movable: movable,
                snapShifts: this.getNewPosition(zone, movable, intersectionPoint),
                matchesCurrentRotation: this.interaction.movable.rotationY === movable.rotationY,
                distance: 0
            });
        }

        const maxSnapShifts = Math.max(...results.map(x => x.snapShifts.length));
        const snapped = results.filter(x => x.snapShifts.length === maxSnapShifts);
        let r = snapped[0] || results[0];

        const size = Math.max(this.interaction.movable.width, this.interaction.movable.depth, this.interaction.movable.height);

        if (snapped.length > 1 && size < 150) { // If more snaps are available use the one with snap points closes to mouse cursor - this enables angles/curves to rotate as they are being dragged

            for (const sn of snapped) {
                sn.movable.x = r.snappedIntersectionPoint.x;
                sn.movable.z = r.snappedIntersectionPoint.z;
                sn.movable.y = r.snappedIntersectionPoint.y;

                const absPoints = getMovablePoints(sn.movable, sn.movable.snaps, sn.movable);

                const distances = absPoints.map(pp => PolygonUtils.pointsDistance(pp, sn.intersectionPoint));
                sn.distance = distances.reduce((acc, cur) => acc + cur, 0);
            }

            snapped.sort((a, b) => a.distance - b.distance);
            const first = snapped[0];
            if (first.matchesCurrentRotation || snapped.every(x => !x.matchesCurrentRotation)) {
                r = first;

            } else {
                const fistMatching = snapped.find(x => x.matchesCurrentRotation);
                if (first.distance + 20 < fistMatching.distance) {
                    r = first;
                }
            }
        }

        return r;
    }

    private collision(isSnapped: boolean): void {
        this.draggedObject.updateWordVertices();
        this.draggedObject.updateBoundingBox(-.0001);
        this.draggedObject.offset = new Vector3();

        if (!this.room.containsBox(this.draggedObject.box)) {
            CollisionUtils.moveInside(this.room, this.draggedObject);
            this.draggedObject.updateBoundingBox(-.0001);
        }

        if (CollisionUtils.collides(this.taken, this.draggedObject, isSnapped)) {
            const move = CollisionUtils.move(this.taken, this.room, this.draggedObject);
            const m = this.draggedObject.interaction.movable;

            m.x += move.x;
            m.y += move.y;
            m.z += move.z;

            this.draggedObject.object3D.position.add(move);
        }
    }

    private getNewPosition(zone: Zone, m: MovableItem, intersectionPoint: Vector3): SnapShift[] {
        if (zone.placement & PlacementMode.Walls) { // Drop zones based dragging
            switch (zone.exterior) {
                case Direction.Right:
                    m.rotationZ = 270;
                    m.rotationX = 0;
                    break;

                case Direction.Down:
                    m.rotationZ = 0;
                    m.rotationX = 90;
                    break;

                case Direction.Up:
                    m.rotationZ = 0;
                    m.rotationX = 270;
                    break;

                case Direction.Left:
                    m.rotationZ = 90;
                    m.rotationX = 0;
                    break;
            }

            m.y = intersectionPoint.y;
            m.elevation = zone.index;
            return this.snap(intersectionPoint, m);

        } else if (zone.placement & PlacementMode.ExternalWalls) { // Drop zones based dragging along walls
            const dragLines = MathUtils.convertXyToPlainArray(zone.dragLines);
            const ap = PositioningUtils.positionItemOnDropLines([dragLines], intersectionPoint.x + this.floorOffset.x, intersectionPoint.z + this.floorOffset.y, m.width, m.depth, true);
            m.elevation = ap.edge;
            intersectionPoint.x = ap.x - this.floorOffset.x;
            intersectionPoint.z = ap.y - this.floorOffset.y;
            intersectionPoint.y = zone.fixedHeight;
            m.rotationY = ap.rotation;
            m.rotationX = 0;
            m.rotationZ = 0;
            return this.snap(intersectionPoint, m);

        } else if (zone.placement & PlacementMode.Attachment) { // Drop zones based dragging along walls
            m.rotationY = zone.emitter.rotationY;
            m.rotationX = zone.emitter.rotationX;
            m.rotationZ = zone.emitter.rotationZ;
            m.parentId = zone.emitter.id;

            m.parentPartId = MovablesUtils.isMultiPart(zone.emitter)
                ? zone.emitter.parts[zone.index]
                : undefined;

            const ap = PositioningUtils.closestPointOn3DPoly(zone.dragLines3D, this.offsetPoint(intersectionPoint));
            const attachmentCenter = PolygonUtils.midPoint3D(m.currentAttachmentPoints[0], m.currentAttachmentPoints.at(-1));
            const rotatedAc = PolygonUtils.rotatePoint(attachmentCenter, m);
            ap.x -= rotatedAc.x;
            ap.y -= rotatedAc.y;
            ap.z -= rotatedAc.z;

            intersectionPoint.x = ap.x - this.floorOffset.x;
            intersectionPoint.z = ap.z - this.floorOffset.y;
            intersectionPoint.y = ap.y;

            return [];

        } else if (zone.placement & PlacementMode.Corners) {
            const point = zone.dragLines[0];
            // Move to corner
            intersectionPoint.x = point.x - this.floorOffset.x;
            intersectionPoint.z = point.y - this.floorOffset.y;
            m.rotationX = 360 - zone.index * 90;
            m.rotationY = 0;
            m.rotationZ = 90;

            const s = this.snap(intersectionPoint, m);

            // Move to corner again after snap
            intersectionPoint.x = point.x - this.floorOffset.x;
            intersectionPoint.z = point.y - this.floorOffset.y;
            return s;

        } else if (zone.placement & PlacementMode.Ceiling) { // Dragging withing boundaries
            m.rotationY = zone.rotationY;
            m.rotationX = 0;
            m.rotationZ = 0;
            m.y = intersectionPoint.y;
            m.elevation = 0;
            return this.snap(intersectionPoint, m);

        } else if (zone.placement & PlacementMode.Horizontal) { // Dragging withing boundaries
            m.rotationY = zone.rotationY;
            m.rotationX = 0;
            m.rotationZ = 0;
            m.y = intersectionPoint.y;
            m.elevation = 0;
            return this.snap(intersectionPoint, m);

        } else if (this.interaction) { // Min to max on single axis dragging

            const previous = new Vector3().copy(this.draggedObject.object3D.position);

            if (this.interaction.axis === Axis.Z) {
                intersectionPoint.x = previous.x;
                intersectionPoint.y = previous.y;
                if (intersectionPoint.z > this.interaction.max) intersectionPoint.z = this.interaction.max;
                if (intersectionPoint.z < this.interaction.min) intersectionPoint.z = this.interaction.min;
            }

            else if (this.interaction.axis === Axis.X) {
                intersectionPoint.z = previous.z;
                intersectionPoint.y = previous.y;
                if (intersectionPoint.x > this.interaction.max) intersectionPoint.x = this.interaction.max;
                if (intersectionPoint.x < this.interaction.min) intersectionPoint.x = this.interaction.min;
            }

            else if (this.interaction.axis === Axis.Y) {
                intersectionPoint.z = previous.z;
                intersectionPoint.x = previous.x;
                if (intersectionPoint.y > this.interaction.max) intersectionPoint.y = this.interaction.max;
                if (intersectionPoint.y < this.interaction.min) intersectionPoint.y = this.interaction.min;
            }
        }

        return [];
    }

    private snap(intersection: Vector3, m: MovableItem): SnapShift[] {
        const movingPoints = getMovablePoints(m, m.snaps, this.offsetPoint(intersection), true);

        const snapShifts = PositioningUtils.snapPointsDetails(this.snapPoints, movingPoints, m.behavior.snapDistance ?? this.options.snapDistance);

        if (snapShifts.length) {
            intersection.sub(snapShifts[0].shiftToSnap);
        }

        return snapShifts;
    }

    private endDrag(): void {
        if (!this.draggedObject) return;
        const parent = this.interaction.movable;

        if (this.grouped) {
            CanvasUtils.splitControls(this.grouped, this.scene.control);
            const rotationDiff = { x: parent.rotationX - this.initialRotation.x, y: parent.rotationY - this.initialRotation.y, z: parent.rotationZ - this.initialRotation.z };

            for (const child of this.grouped) {
                const m = child.interaction.movable;

                m.x = child.object3D.position.x + this.floorOffset.x;
                m.y = child.object3D.position.y;
                m.z = child.object3D.position.z + this.floorOffset.y;

                m.rotationX = cleanRotation(m.rotationX + rotationDiff.x);
                m.rotationY = cleanRotation(m.rotationY + rotationDiff.y);
                m.rotationZ = cleanRotation(m.rotationZ + rotationDiff.z);

                if ((parent.placementMode & (PlacementMode.Ceiling | PlacementMode.Walls)) && m.placementMode ^ PlacementMode.Attachment && m.placementMode ^ PlacementMode.Horizontal) {
                    m.placementMode = parent.placementMode;
                    m.elevation = parent.elevation;
                }

                child.mouseOut();
            }
        } else {
            const b = parent.behavior;
            if (b && b.singlePlacementOptions && b.placementOptions && b.singlePlacementOptions.includes(parent.placementMode)) {
                parent.placementMode = b.placementOptions[0];
            }
        }

        this.positionPlanes = [];
        this.dragPlanes =[];
        this.planes = [];

        this.draggedObject = undefined;
        this.snapPoints = undefined;
        this.grouped = undefined;

        this.clearChildren();
    }

    private getPlaneIntersections(x: number, y: number): Intersection[] {
        let intersects = CanvasUtils.intersectObjects(this.scene, x, y, this.planes);

        const hasHorizontal = intersects.some(i => i.object.userData.placement === PlacementMode.Horizontal);
        if (hasHorizontal) {
            intersects = intersects.filter(i => i.object.userData.placement !== PlacementMode.Ceiling);
        }

        // If there are two intersections with the same distance, prefer the one that matches the current item's rotation
        const m = this.interaction.movable;
        intersects.sort((a, b) => {
            if (Math.abs(a.distance - b.distance) > 500) { // Distance has priority
                return a.distance - b.distance;
            }

            const zoneA = a.object.userData as Zone;
            const zoneB = b.object.userData as Zone;

            if (zoneA.rotationY === zoneB.rotationY) {

                if (zoneA.placement === m.placementMode) { // If it's the same, prefer current placement
                    return -1;
                }
                if (zoneB.placement === m.placementMode) { // If it's the same, prefer current placement
                    return 1;
                }
            }

            if (zoneA.rotationY === m.rotationY) { // If it's the same, prefer current angle
                return -1;
            }
            if (zoneB.rotationY === m.rotationY) { // If it's the same, prefer current angle
                return 1;
            }

            return a.distance - b.distance;
        });

        // Hide all position planes
        for (const p of this.positionPlanes) {
            (p.material as MeshBasicMaterial).visible = false;
        }

        // First, try to find an intersection with position planes (smaller, only in areas where the item can be placed)
        for (let i = 0; i < intersects.length; i++) {
            const obj = intersects[i].object as Mesh;
            if (this.positionPlanes.includes(obj)) {

                this.indicator.position.copy(intersects[i].point);

                // Show the active position plane
                (obj.material as MeshBasicMaterial).visible = true;

                for (const p of this.dragPlanes) {
                    p.visible = false;
                }

                const dragPlane = this.dragPlanes[this.positionPlanes.indexOf(obj)];
                if (dragPlane) dragPlane.visible = true;

                return intersects.filter(se => this.positionPlanes.includes(se.object as Mesh));
            }
        }

        // Fallback, try to find at least a drag plane
        for (let i = 0; i < intersects.length; i++) {
            const obj = intersects[i].object as Mesh;
            if (this.dragPlanes.includes(obj)) {

                if (obj.visible) {

                    const int = intersects[i];
                    const origin = int.point;
                    const mesh = int.object as Mesh;
                    const index = this.dragPlanes.indexOf(mesh);
                    const dragPlane = this.positionPlanes[index];

                    if (dragPlane) {
                        dragPlane.updateMatrixWorld(true);

                        const verticesArray = (dragPlane.geometry.getAttribute("position") as BufferAttribute).array;
                        const vertices: Vector3[] = [];
                        for (let i = 0; i < verticesArray.length; i += 3) {
                            const v = new Vector3(verticesArray[i], verticesArray[i + 1], verticesArray[i + 2]);
                            vertices.push(dragPlane.localToWorld(v));
                        }

                        const vertiPoints = vertices.map(p => (
                            {
                                p: p,
                                dist: p.distanceTo(origin)
                            })
                        );
                        vertiPoints.sort((a, b) => a.dist - b.dist);
                        this.indicator.position.copy(vertiPoints[0].p);

                        int.point.copy(vertiPoints[0].p);

                        return [int];
                    }
                }
            }
        }

        return undefined;
    }

    private offsetPoint(p: IPoint3D): IPoint3D {
        return { x: p.x + this.floorOffset.x, y: p.y, z: p.z + this.floorOffset.y };
    }

    private static geo = new SphereGeometry(15, 25, 32, 32);

    private static getIndicator(color: string): Mesh {
        return new Mesh(
            DraggerControl.geo,
            new MeshStandardMaterial({ color, metalness: 0.8, roughness: 0.2 })
        );
    }
}