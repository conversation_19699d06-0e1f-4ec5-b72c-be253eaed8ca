﻿import { CanvasControl } from "./CanvasControl";
import { IResizeCallback } from "./IResizeCallback";
import { MovableGraphics } from "../graphics/MovableGraphics";
import { IInteractionOptions } from "../../models/positioning/IInteractionOptions";
import { MathUtils } from "../../logic/helpers/MathUtils";
import { Axis } from "../../models/common/Enums";
import { IApplicationState } from "../../models/state/IApplicationState";
import { Vector3 } from "three";
import { MessageHub } from "../../infrastructure/MessageHub";
import { Events } from "../../models/infrastructure/Events";

export class ResizeControl extends CanvasControl {

    private controlPosition: Vector3;

    constructor(graphics: MovableGraphics) {
        super();

        const arrow = graphics.createGraphics(null, null, null, null, null);
        this.object3D.add(arrow);

        this.cachedHighlight = graphics.createHighlight(null, null, null, null, this.highlightMaterial);

        this.overCursor = "pointer";
    }

    public init(options: IInteractionOptions): void {
        this.interaction = options;

        if (options.interiorSide) { // Arrow on plan - rotate to be in 90-degree angle with the related elevation
            const angle = MathUtils.getRotationByDirection(MathUtils.flipDirection(options.interiorSide));
            this.object3D.rotation.y = Math.PI / 180 * (angle === 90 || angle === 270 ? -angle : angle);
        }

        if (options.axis === Axis.Y) { // Vertical arrow
            this.object3D.rotation.x = Math.PI / 2;
        }

        this.controlPosition = new Vector3();

        this.object3D.position.set(this.interaction.x, this.interaction.movable.y, this.interaction.y);
        this.controlPosition.copy(this.object3D.position);

        this.attachControlToObject3D();
    }

    public update(s: IApplicationState): void {
        this.object3D.visible = s.view.showBuildingResize && (!s.view.activeItem || s.view.activeItem === this.interaction);

        if (this.interaction.axis === Axis.Y) { // Vertical arrow
            this.object3D.rotation.z = (Math.PI * 2) - (s.view.theta * (Math.PI / 180));
        }

        const diff = MathUtils.getVectorDiff(this.object3D.position, this.controlPosition, this.interaction.step);

        if (Math.abs(diff.x) > 0 || Math.abs(diff.y) > 0 || Math.abs(diff.z) > 0) {
            this.controlPosition.x += diff.x;
            this.controlPosition.y += diff.y;
            this.controlPosition.z += diff.z;

            this.object3D.position.copy(this.controlPosition);

            MessageHub.broadcast<IResizeCallback>(Events.dragResizing, {diff, interaction: this.interaction});
        }
    }
}