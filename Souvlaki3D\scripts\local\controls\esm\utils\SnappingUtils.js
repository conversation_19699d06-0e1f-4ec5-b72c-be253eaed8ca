export function snapDirection(referencePoint, movingPoint, maxDistance) {
    let snapped = false;
    if (Math.abs(referencePoint.x - movingPoint.x) < maxDistance) {
        movingPoint.x = referencePoint.x;
        snapped = true;
    }
    if (Math.abs(referencePoint.z - movingPoint.z) < maxDistance) {
        movingPoint.z = referencePoint.z;
        snapped = true;
    }
    return snapped;
}
export function snapDirections(referencePoints, movingPoint, maxDistance, exclude) {
    let snapped = false;
    for (const referencePoint of referencePoints) {
        if (exclude?.distanceTo(referencePoint) < 1) {
            continue;
        }
        if (snapDirection(referencePoint, movingPoint, maxDistance)) {
            snapped = true;
        }
    }
    return snapped;
}
export function snapToGrid(movingPoint, snapGridSpacing, snapToGridMaxDistance) {
    let snapped = false;
    if (movingPoint.x % snapGridSpacing < snapToGridMaxDistance) {
        movingPoint.x = Math.round(movingPoint.x / snapGridSpacing) * snapGridSpacing;
        snapped = true;
    }
    if (movingPoint.z % snapGridSpacing < snapToGridMaxDistance) {
        movingPoint.z = Math.round(movingPoint.z / snapGridSpacing) * snapGridSpacing;
        snapped = true;
    }
    return snapped;
}
export function snapWalls(movingPoint, snapPoints, snapToWallEndpointMaxDistance, exclude) {
    for (const snapPoint of snapPoints) {
        if (exclude?.distanceTo(snapPoint) < 1) {
            continue;
        }
        if (snapPoint.distanceTo(movingPoint) < snapToWallEndpointMaxDistance) {
            movingPoint.copy(snapPoint);
            return true;
        }
    }
    return false;
}
