import { Line2D, Vec2, Vec3 } from "@immugio/three-math-extensions";
import { MathUtils } from "three";
export class Wall {
    wallCenterPoints;
    startHeight;
    endHeight;
    bottomY;
    thickness;
    leftMaterial;
    rightMaterial;
    revealsMaterial;
    interactions;
    cutoutItems;
    isTransparent;
    textureOffset;
    id;
    constructor(wallCenterPoints, startHeight, endHeight, bottomY = 0, thickness, 
    /** Left side material (when looking from the start to end)  */
    leftMaterial, 
    /** Right side material (when looking from the start to end)  */
    rightMaterial, revealsMaterial, interactions, cutoutItems = [], isTransparent = false, textureOffset = null, id = MathUtils.generateUUID()) {
        this.wallCenterPoints = wallCenterPoints;
        this.startHeight = startHeight;
        this.endHeight = endHeight;
        this.bottomY = bottomY;
        this.thickness = thickness;
        this.leftMaterial = leftMaterial;
        this.rightMaterial = rightMaterial;
        this.revealsMaterial = revealsMaterial;
        this.interactions = interactions;
        this.cutoutItems = cutoutItems;
        this.isTransparent = isTransparent;
        this.textureOffset = textureOffset;
        this.id = id;
    }
    static fromPoints(startPoint, endPoint, startHeight, endHeight, bottomY, thickness, id) {
        return new Wall([startPoint, endPoint], startHeight, endHeight, bottomY, thickness, null, null, null, null, null, false, null, id);
    }
    get startPointOnPlan() {
        return this.wallCenterPoints[0];
    }
    get endPointOnPlan() {
        return this.wallCenterPoints.at(-1);
    }
    get wallLineOnPlan() {
        return Line2D.fromPoints(this.startPointOnPlan, this.endPointOnPlan);
    }
    get startPointInSpace() {
        return new Vec3(this.startPointOnPlan.x, this.startHeight, this.startPointOnPlan.y);
    }
    get endPointInSpace() {
        return new Vec3(this.endPointOnPlan.x, this.endHeight, this.endPointOnPlan.y);
    }
    getContour(slopes, previous, next) {
        const { leftLine, rightLine } = this.getOnPlanContour(previous, next);
        const leftTranslation = this.wallLineOnPlan.direction.normalize().rotateAround(new Vec2(), -Math.PI / 2).multiplyScalar(this.thickness / 2);
        const rightTranslation = leftTranslation.clone().negate();
        const leftPoints2d = leftLine.endpoints;
        const rightPoints2d = rightLine.endpoints;
        this.wallCenterPoints.slice(1, -1).forEach(p => {
            leftPoints2d.splice(-1, 0, p.clone().add(leftTranslation));
            rightPoints2d.splice(-1, 0, p.clone().add(rightTranslation));
        });
        return {
            left: {
                top: slopes.projectPointsOnPlane(leftPoints2d),
                bottom: leftLine.endpoints.map(p => new Vec3(p.x, this.bottomY, p.y)),
            },
            right: {
                top: slopes.projectPointsOnPlane(rightPoints2d),
                bottom: rightLine.endpoints.map(p => new Vec3(p.x, this.bottomY, p.y)),
            }
        };
    }
    clipNeighbour(leftLine, rightLine, otherWall) {
        const { leftLine: otherLeftLine, rightLine: otherRightLine } = otherWall.getOnPlanContour();
        leftLine = this.clipLine(leftLine, otherLeftLine);
        rightLine = this.clipLine(rightLine, otherRightLine);
        return { leftLine, rightLine };
    }
    clipLine(line, otherLine) {
        const clips = line.splitAtOrExtendToIntersection(otherLine);
        if (clips) {
            return clips.at(-1);
        }
        return line;
    }
    getOnPlanContour(previous, next) {
        let leftLine = this.wallLineOnPlan.clone().translateLeft(this.thickness / 2);
        let rightLine = this.wallLineOnPlan.clone().translateRight(this.thickness / 2);
        if (previous) {
            ({ leftLine, rightLine } = this.clipNeighbour(leftLine, rightLine, previous));
        }
        if (next) {
            ({ leftLine, rightLine } = this.clipNeighbour(leftLine, rightLine, next));
        }
        return { leftLine, rightLine };
    }
    getInteractionAtIndex(index) {
        return Array.isArray(this.interactions) ? (this.interactions[index] || this.interactions[0]) : this.interactions;
    }
    reverseDirection() {
        this.wallCenterPoints.reverse();
        return this;
    }
    get isInteractive() {
        return !this.isTransparent && !!this.interactions;
    }
    equals(other) {
        return (this.id === other.id &&
            this.startPointOnPlan.equals(other.startPointOnPlan) &&
            this.endPointOnPlan.equals(other.endPointOnPlan) &&
            this.startHeight === other.startHeight &&
            this.endHeight === other.endHeight &&
            this.bottomY === other.bottomY &&
            this.thickness === other.thickness &&
            this.leftMaterial === other.leftMaterial &&
            this.rightMaterial === other.rightMaterial &&
            this.revealsMaterial === other.revealsMaterial &&
            this.isTransparent === other.isTransparent);
    }
    clone() {
        return new Wall(this.wallCenterPoints.map(p => p.clone()), this.startHeight, this.endHeight, this.bottomY, this.thickness, this.leftMaterial, this.rightMaterial, this.revealsMaterial, this.interactions, this.cutoutItems, this.isTransparent, this.textureOffset, this.id);
    }
}
