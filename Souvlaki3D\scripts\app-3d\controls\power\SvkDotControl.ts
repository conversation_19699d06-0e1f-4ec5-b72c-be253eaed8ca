import { IApplicationSettings } from "../../../models/infrastructure/IApplicationSettings";
import { MovableItem } from "../../../models/state/MovableItem";
import { DotState } from "../../../models/common/Enums";
import { Scene } from "../../../models/scene/Scene";
import { Object3D } from "three";
import { TaDotControl } from "./TaDotControl";

export class SvkDotControl extends TaDotControl {

    constructor(movable: MovableItem, index: number, collidingAttachments: MovableItem[], settings: IApplicationSettings, scene: Scene, public interactive = true) {
        super(movable, index, collidingAttachments, settings, scene);
        this.setCursor(interactive ? DotState.Default : DotState.NonInteractive);
    }

    protected setCursor(dotState: DotState): void {
        switch(dotState) {
            case DotState.NonInteractive: 
                this.overCursor = "not-allowed";
                break;
            default:
                super.setCursor(dotState);
                break;
        }
    }

    public getHighlight(): Object3D {
        if(this.interactive) {
            return super.getHighlight();
        }

        return null;
    }

    protected getCurrentState(selectedPowerSupply: { movable: MovableItem, index: number }, movableItems: MovableItem[]): DotState {
        const state = super.getCurrentState(selectedPowerSupply, movableItems)

        if(state == DotState.Connected && !this.interactive) {
            return DotState.NonInteractive;
        }

        return state;
    }
}