import { CanvasControl } from "./CanvasControl";
import { IWallEditControlOptions, SceneContext, WallEditControl, WallSet } from "@immugio/controls";
import { Scene } from "../../models/scene/Scene";
import { MessageHub } from "../../infrastructure/MessageHub";
import { ContextActions, Events } from "../../models/infrastructure/Events";
import { shiftWallsToZero } from "../../logic/helpers/building/shiftWallsToZero";
import { pushState } from "../../state/actions/pushState";
import { setWallHeight } from "../../logic/helpers/building/setWallHeight";
import { ContextMenu } from "../../app-dialogs/components/ContextMenu";
import { ContextItem } from "../../models/menu/ContextItem";
import { dom } from "@immugio/jsx-and-html-utils";
import { Wall } from "@immugio/controls";
import { StateService } from "../../state/StateService";
import { IWall } from "../../models/state/DesignState";
import { Vec2, Vec3 } from "@immugio/three-math-extensions";

function wallDataToWallSet(wallData: IWall[]): WallSet {
    const walls = wallData.map(w => Wall.fromPoints(new Vec2(w.start.x, w.start.y), new Vec2(w.end.x, w.end.y), w.start.z, w.end.z, 10, w.thickness, w.id));
    return new WallSet(walls);
}

function wallSetToWallData(wallSet: WallSet): IWall[] {
    return wallSet.walls.map(w => ({
        id: w.id,
        start: new Vec3(w.wallCenterPoints[0].x, w.wallCenterPoints[0].y, w.startHeight),
        end: new Vec3(w.wallCenterPoints[1].x, w.wallCenterPoints[1].y, w.endHeight),
        thickness: w.thickness,
        isExternal: true,
        level: 0,
    }));
}

export class WallEditControlWrapper extends CanvasControl {

    public static $inject = ["scene", "StateService", "MessageHub"];
    private control: WallEditControl;
    private contextmenu: ContextMenu;

    constructor(private scene: Scene, private state: StateService, private messageHub: MessageHub) {
        super();
        messageHub.subscribe(Events.toggleWallControl, (show: boolean) => this.toggleWallControl(show));
        messageHub.subscribe(Events.drawingInteraction, () => this.contextmenu?.remove());
    }

    private toggleWallControl(show: boolean) {
        this.contextmenu?.remove();

        if (show && !this.control) {
            const controlOptions: IWallEditControlOptions = {
                defaultWallThickness: this.state.design.wallThickness,
                defaultWallHeight: Math.max(...this.state.design.walls.map(w => [w.start.y, w.end.y]).flat()),
                disableOrbitControls: (disable?: boolean) => {
                    this.state.view.activeItem = disable ? {} : null;
                },
                onWallClicked: (wallSet, wall, e) => {
                    this?.contextmenu?.remove();
                    const action = new ContextItem("Delete Wall", ContextActions.DeleteMovable);
                    this.contextmenu = <ContextMenu actions={[action]} onClick={() => this.removeWall(wall)} position={{ x: e.clientX, y: e.clientY }} />;
                },
                onStep: (walls) => {
                    const design = this.state.design.clone();
                    design.walls = setWallHeight(shiftWallsToZero(wallSetToWallData(walls)), design.height);
                    pushState(design);
                    this.state.saveHistory();
                },
            };

            const controlScene = new SceneContext(
                this.scene.control.object3D,
                this.scene.camera,
                this.scene.renderer
            );

            this.control = new WallEditControl(controlOptions, controlScene, wallDataToWallSet(this.state.design.walls));
        }

        if (!show && this.control) {
            const design = this.state.design.clone();
            design.walls = setWallHeight(shiftWallsToZero(wallSetToWallData(this.control.getWallData())), design.height);

            this.control.dispose();
            this.control = null;

            pushState(design);
        }
    }

    private removeWall(wall: Wall) {
        this.toggleWallControl(false);
        const design = this.state.design.clone();
        design.walls = design.walls.filter(w => w.id !== wall.id);
        pushState(design);
        this.toggleWallControl(true);
    }
}