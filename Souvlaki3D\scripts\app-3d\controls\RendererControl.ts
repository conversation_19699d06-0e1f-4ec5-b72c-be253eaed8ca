﻿import { CanvasControl } from "./CanvasControl";
import { Scene } from "../../models/scene/Scene";
import { IApplicationState } from "../../models/state/IApplicationState";
import { Scene as ThreeScene } from "three";
import { settings } from "../../configurations/setup_global";

export class RendererControl extends CanvasControl {

    public static $inject = ["scene"];

    constructor(private scene: Scene) {
        super();
    }

    public update(s: IApplicationState): void {
        if (s.diff.backgroundColorChanged) {
            this.scene.renderer.setClearColor(s.view.backgroundColor, settings.view.backgroundAlpha);
        }

        this.scene.renderer.render(this.scene.control.object3D as ThreeScene, this.scene.camera);
    }
}