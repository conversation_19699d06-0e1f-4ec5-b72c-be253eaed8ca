import { translate } from "./Translator";

export class Modal {
    public static confirm(title: string, message: string, callback?: (r: boolean) => void, okButtonText?: string, cancelButtonText?: string, okShowsPreviousModal?: boolean): void {
        yada.confirm(translate(title), translate(message), callback, okButtonText, cancelButtonText, okShowsPreviousModal);
    }

    public static showInfoModal(title: string, message: string, redirectUrl?: string): void {
        yada.showInfoModal(translate(title), translate(message), redirectUrl);
    }

    public static showErrorModal(title: string, message: string): void {
        yada.showErrorModal(translate(title), translate(message));
    }
}