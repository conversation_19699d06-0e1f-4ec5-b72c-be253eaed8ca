import { BaseActionController } from "./BaseActionController";
import { StateService } from "../../../state/StateService";
import { IToolbarActionOptions } from "./IToolbarActionOptions";
import { pushView } from "../../../state/actions/pushView";

export class RulerController extends BaseActionController {

    public static $inject = ["StateService"];

    constructor(private state: StateService, options: IToolbarActionOptions) {
        super(options);
        this.defaultInitialize();
    }

    public execute(): void {
        const ruler = !this.state.view.ruler;
        pushView({ruler});
    }
}