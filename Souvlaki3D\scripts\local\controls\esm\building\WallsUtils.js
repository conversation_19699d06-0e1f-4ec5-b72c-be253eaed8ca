export class WallsUtils {
    static moveWallEndpoint(walls, wall, endpoint, move) {
        const otherWalls = this.getOtherWalls(walls, wall);
        const endpointsOnOtherWalls = otherWalls.map(w => [w.start, w.end]).flat();
        const matchingEndpoints = endpointsOnOtherWalls.filter(ep => ep.isNear(endpoint, 1));
        [...matchingEndpoints, endpoint].forEach(ep => ep.add(move));
    }
    static getOtherWalls(walls, wall) {
        return walls.filter(w => w.id !== wall.id);
    }
    static moveWall(walls, wall, move) {
        this.moveWallEndpoint(walls, wall, wall.start, move);
        this.moveWallEndpoint(walls, wall, wall.end, move);
    }
}
