import { CanvasUtils } from "../app-3d/utils/CanvasUtils";
import { StateService } from "../state/StateService";
import { MovablesLogic } from "../logic/MovablesLogic";
import { Activator } from "./Activator";
import { Core } from "../app-3d/Core";
import { DebugUtils } from "../app-3d/utils/DebugUtils";
import { Scene } from "../models/scene/Scene";
import { settings } from "../configurations/setup_global";
import { ConfigurationProvider } from "./ConfigurationProvider";
import { IController } from "../app-dialogs/controllers/BaseController";

ConfigurationProvider.initialize(settings);
const container = document.getElementById(settings.app.containerId);

Activator.register("settings", settings);

const scene = new Scene();
Activator.register("scene", scene);
CanvasUtils.createScene(container, null, null, "canvas-3d", true, scene, Activator.get<StateService>("StateService").view.environment);

const controllers = (settings.app.controllers || []).map(x => typeof x === "string" ? Activator.get<IController>(x) : Activator.get<IController>(x.type, x.options));

DebugUtils.initialize(Activator.get<Scene>("scene"), Activator.get("StateService"));
new Core(Activator.get<MovablesLogic>("MovablesLogic"), Activator.get("BuildingLogic"), Activator.get("DimensionsLogic"), Activator.get("ZonesLogic"), Activator.get("LabelsLogic"), Activator.get("AlertService"), Activator.get("StateService"), controllers, Activator.get("MessageHub"), Activator.get("scene"));