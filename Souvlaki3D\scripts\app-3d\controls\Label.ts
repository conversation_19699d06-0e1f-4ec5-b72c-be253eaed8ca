import { TextUtils } from "../utils/TextUtils";
import { Mesh, MeshBasicMaterial, Vector3 } from "three";

const textMat = new MeshBasicMaterial({ color: "black", depthTest: false });
const _target = new Vector3();

export function Label(text: string, textSize: number = 50): Mesh {
    const label = TextUtils.getText(text, textSize, textMat, 1, true);
    label.geometry.computeBoundingBox();
    const size = label.geometry.boundingBox.getSize(_target);
    label.position.x -= size.x / 2;
    label.position.y -= size.y * 2;
    label.renderOrder = 999;
    return label;
}