import { BaseController } from "../BaseController";
import { MessageHub } from "../../../infrastructure/MessageHub";
import { StateService } from "../../../state/StateService";
import { Events } from "../../../models/infrastructure/Events";
import { IInteractionOptions } from "../../../models/positioning/IInteractionOptions";
import { MovableItem } from "../../../models/state/MovableItem";
import { PowerLogic } from "../../../logic/PowerLogic";
import { IPowerOptions } from "../../../models/state/IPowerOptions";
import { ITapInfo } from "../../../models/menu/ITapInfo";
import { MovablesLogic } from "../../../logic/MovablesLogic";
import { PositioningUtils } from "../../../logic/helpers/PositioningUtils";
import { IPowerInteractionOptions } from "../../../models/events/IPowerInteractionOptions";
import { Modal } from "../../services/Modal";

export class PowerController extends BaseController {

    public static $inject = ["PowerLogic", "MovablesLogic", "MessageHub", "StateService"];

    private dialog: HTMLElement;
    private container: HTMLElement;
    private template: string;

    constructor(private powerLogic: PowerLogic, private movablesLogic: MovablesLogic, private messageHub: MessageHub, private state: StateService) {
        super();
        this.initialize();
    }

    private async initialize(): Promise<void> {
        this.dialog = document.getElementById("powerinfo");
        this.container = this.dialog.querySelector("div");
        this.template = this.container.innerHTML;
        this.dialog.querySelector("button").addEventListener("click", () => this.reset());
        this.messageHub.subscribe<IInteractionOptions>(Events.optionsSelected, (info) => this.onSupplySelected(info));
        this.messageHub.subscribe<ITapInfo>(Events.itemSelected, info => this.onItemSelected(info));
        this.messageHub.subscribe<IPowerInteractionOptions>(Events.powerSourceAdded, o => this.powerSourceAdded(o));
        this.messageHub.subscribe(Events.powerSourcesNeedUpdate, () => this.setColors());
    }

    private reset(): void {
        const m = this.state.view.selectedPowerSupply.movable;
        m.data.powerSource = [];
        this.state.view.selectedPowerSupply = null;
        this.powerLogic.setColors(this.state.design, null, null);
        this.powerLogic.updatePowerIds(this.state.design.movableItems);
        this.togglePowerDg(null, null);
    }

    private onItemSelected(info: ITapInfo): void {
        const toConnect = info.interaction.movable;
        const main = this.state.view.selectedPowerSupply?.movable;
        const powerSourceIndex = this.state.view.selectedPowerSupply?.index;

        if (!toConnect || !main) {
            return;
        }

        toConnect.selected = false;

        const toggled = this.powerLogic.toggleItem(this.state.design, main, powerSourceIndex, toConnect, info.interaction.partId);
        if (toggled) {
            this.powerLogic.setColors(this.state.design, main, powerSourceIndex);
            this.togglePowerDg(main, powerSourceIndex);

        } else if (toggled === null) { // Can't connect due to insufficient power
            const snaps = PositioningUtils.getMovableSnaps(this.state.design.movableItems);
            const allowed = this.powerLogic.getAllowedPoints(toConnect, snaps);

            if (allowed.points.length > 0) { // Zero points typically means that the element is not allowed to have its own power sources
                const index = toConnect.snaps.indexOf(allowed.points[0]);
                const connected = (this.movablesLogic.getConnectedItems(this.state.design.movableItems, toConnect) || []).map(x => x.movable);
                this.confirmSource(toConnect, index, connected);
            }
        }
    }

    private onSupplySelected(info: IInteractionOptions): void {
        const m = info.movable, d = this.state.design;
        const powerSource = m.data.powerSource.find(x => x.sideIndex === info.lineIndex);

        if (!powerSource && this.powerLogic.isPowered(d, m, info.partId)) {
            // Item is connected to another power source already
            return;
        }

        if (powerSource) { // Item is a power source with params set
            this.selectGroup(m, powerSource.sideIndex);
            this.state.view.selectedPowerSupply = { movable: m, index: powerSource.sideIndex };

        } else { // New item to become power source, params to be selected
            const isFirst = d.movableItems.every(x => !x.data.powerSource.length);
            const connected = (this.movablesLogic.getConnectedItems(d.movableItems, m) || []).map(x => x.movable);

            if (isFirst) {
                console.log("TODO: openPowerSelect", m, info.lineIndex, connected);

            } else {
                this.confirmSource(m, info.lineIndex, connected);
            }
        }
    }

    private confirmSource(m: MovableItem, index: number, connected: MovableItem[]): void {
        Modal.confirm(null, "Do you want to add another power supply?", r => {
            if (r) {
                console.log("TODO: openPowerSelect", m, index, connected);
            }
        }, "Ok", "Cancel");
    }

    private powerSourceAdded(o: IPowerInteractionOptions): void {
        this.powerLogic.addPowerSource(this.state.design, o.selection, o.movable, o.index);
        this.state.view.selectedPowerSupply = { movable: o.movable, index: o.index };
        this.selectGroup(o.movable, o.index);
    }

    private selectGroup(m: MovableItem, index: number): void {
        this.togglePowerDg(m, index);
        this.powerLogic.setColors(this.state.design, m, index);
    }

    private togglePowerDg(m: MovableItem, index: number) {
        if (m) {
            const ps = m.data.powerSource.find(x => x.sideIndex === index);
            if (ps) {
                const currentLoad = this.powerLogic.groupConsumption(this.state.design.movableItems, m, index);
                const left = ps.effective - currentLoad;
                const id = (ps.powerSourceId > 9 ? "" : "0") + ps.powerSourceId.toString();
                const canAdd = this.powerLogic.hasPowerForNeighbour(this.state.design, m, index);

                this.container.innerHTML = this.template
                    .replace(/\${number}/g, id)
                    .replace(/\${tags}/g, this.getTags(ps))
                    .replace(/\${warn}/g, canAdd ? "" : "error-color")
                    .replace(/\${left}/g, left.toString());
                this.dialog.style.display = "";
            }

        } else {
            this.dialog.style.display = "none";
        }
    }

    private getTags(ps: IPowerOptions): string {
        const tags = [
            `${ps.nominal} W`,
            ps.installation,
            ps.color,
            ps.appControlled && (ps.appControlled == "true" ? "App-controlled" : "Normal"),
            ps.typology && (ps.typology == "true" ? "Dimmable" : "Undimmable")
        ]
            .filter(x => x);

        return tags.map(x =>
            `<span class="prop-label" title="${x}">${x}</span>`
        ).join("");
    }

    private setColors(): void {
        this.powerLogic.setPowerFrom(this.state.design.movableItems);
        this.powerLogic.setColors(this.state.design, null, null);
    }
}