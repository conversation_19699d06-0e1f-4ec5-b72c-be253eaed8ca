import { IApplicationSettings } from "../../../models/infrastructure/IApplicationSettings";
import { IApplicationState } from "../../../models/state/IApplicationState";
import { MovableItem } from "../../../models/state/MovableItem";
import { CanvasControl } from "../CanvasControl";
import { Scene } from "../../../models/scene/Scene";
import { AvailableActions } from "../../../models/common/Enums";
import { isLinear } from "../../../logic/helpers/PositioningUtils";
import { HoyDotControl } from "./HoyDotControl";
import { ISnapPoint } from "../../../models/positioning/ISnapPoint";
import { canHaveFeedPoints, isHoyControlTypeSelected } from "../../../logic/helpers/PowerUtils";
import { IMovableSnap } from "../../../models/positioning/IMovableSnap";
import { isNear } from "../../../logic/helpers/IsNear";
import { getConnectedGroups } from "../../../logic/helpers/positioning/getConnectedGroups";

export class HoyPowerControl extends CanvasControl {

    public static $inject = ["settings", "scene"]

    constructor(private settings: IApplicationSettings, private scene: Scene) {
        super();
    }

    private movables: MovableItem[] = [];

    public update(s: IApplicationState): void {
        if (!s.view.isActionAvailable(AvailableActions.Power) || !isHoyControlTypeSelected(s.design.movableItems)) {
            if (this.children.length > 0) {
                this.movables = [];
                this.clearChildren();
            }

            return;
        }

        this.object3D.position.x = -s.view.offset.x;
        this.object3D.position.z = -s.view.offset.y;

        const movables = s.design.movableItems.filter(x => !x.data.attachment);
        if (this.changed(movables)) {
            this.clearChildren();
            this.movables = movables;
            this.setControls(movables);
        }

        super.update(s);
    }

    private setControls(movables: MovableItem[]): void {
        const groups = getConnectedGroups(movables);
        for (const group of groups) {
            if (canHaveFeedPoints(group)) {
                this.setControlsGroup(group);
            }
        }
    }

    private setControlsGroup(snaps: IMovableSnap[]): void {
        const powerPointLocations: {
            absoluteSnap: ISnapPoint;
            movablesAtSnap: { item: MovableItem, index: number }[]
        }[] = [];

        for (const snap of snaps) {
            for (let i = 0; i < snap.absoluteSnaps.length; i++) {
                const snapPoint = snap.absoluteSnaps[i];
                const movableWithIndex = { item: snap.movable, index: i };

                let ppLocation = powerPointLocations.find(ta => isNear(ta.absoluteSnap, snapPoint, .5));

                if (!ppLocation) {
                    ppLocation = {
                        absoluteSnap: snapPoint,
                        movablesAtSnap: []
                    };
                    powerPointLocations.push(ppLocation);

                }

                if (isLinear(snap.movable)) {
                    ppLocation.movablesAtSnap.push(movableWithIndex);
                } else {
                    ppLocation.movablesAtSnap.unshift(movableWithIndex);
                }
            }
        }

        // Remove duplicate corners
        const unique = powerPointLocations.reduce((acc, cur) => {
            if (isLinear(cur.movablesAtSnap[0].item)) {
                return acc.concat(cur);
            }

            if (!acc.some(i => i.movablesAtSnap[0].item === cur.movablesAtSnap[0].item)) {
                return acc.concat(cur);
            }

            return acc;
        }, []);


        for (const point of unique) {
            const dot = new HoyDotControl(point.movablesAtSnap[0].item, point.movablesAtSnap[0].index, point.absoluteSnap, this.settings, this.scene);
            this.addChild(dot);
        }
    }

    private changed(movables: MovableItem[]): boolean {
        if (this.movables.length !== movables.length) {
            return true;
        }

        for (let i = 0; i < movables.length; i++) {
            const m = movables[i];
            const c = this.movables[i];
            if (m !== c) {
                return true;
            }
        }

        return false;
    }
}