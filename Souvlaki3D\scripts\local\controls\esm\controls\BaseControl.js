import { MeshBasicMaterial, SphereGeometry } from "three";
import { Mesh3D } from "../objects/Mesh3D";
import { Interaction } from "../objects/Interaction";
import { Highlight } from "../graphics/Highlight";
import { Line3D, Vec3 } from "@immugio/three-math-extensions";
import { Container3D } from "../objects/Container3D";
export class BaseControl {
    props;
    get position() {
        return this.root.position;
    }
    root;
    interaction;
    highlight;
    onHover(isHovered) {
        if (!this.highlight) {
            this.highlight = Highlight.create(this.root);
            this.highlight.visible = false;
            this.root.add(this.highlight);
        }
        this.highlight.visible = isHovered;
    }
    onDrag(info) {
    }
    onMouseDown(e) {
        this.props.onMouseDown?.(this, e);
    }
    onMouseUp(e) {
        this.props.onMouseUp?.(this, e);
    }
    constructor(props = {}) {
        this.props = props;
        const { content, interaction } = props;
        this.interaction = interaction || new Interaction();
        this.root = new Container3D().setInteraction(this.interaction);
        if (content) {
            this.root.add(content);
        }
        if (interaction) {
            this.interaction = interaction;
            this.root.setInteraction(interaction);
        }
        this.interaction.control = this;
    }
}
export class CornerControl extends BaseControl {
    wallCornerPoint;
    initialPosition;
    dragCallback;
    constructor(wallCornerPoint, initialPosition, dragCallback) {
        super();
        this.wallCornerPoint = wallCornerPoint;
        this.initialPosition = initialPosition;
        this.dragCallback = dragCallback;
        this.interaction = new Interaction();
        this.interaction.dragger = "HorizontalPlaneDragger";
        this.interaction.options = {
            center: wallCornerPoint.cornerPoint,
        };
        this.interaction.control = this;
        this.root = new Mesh3D(new SphereGeometry(100, 32, 32), new MeshBasicMaterial({ color: 0xff0000, transparent: true, opacity: 0.5 }))
            .setInteraction(this.interaction)
            .setPosition(initialPosition.x, initialPosition.y, initialPosition.z);
    }
    onDrag(info) {
        this.dragCallback(this);
    }
}
export class CenterControl extends BaseControl {
    wallCenterPoint;
    initialPosition;
    dragCallback;
    constructor(wallCenterPoint, initialPosition, dragCallback) {
        super();
        this.wallCenterPoint = wallCenterPoint;
        this.initialPosition = initialPosition;
        this.dragCallback = dragCallback;
        const wallLine2d = wallCenterPoint.wall.wallLineOnPlan;
        wallLine2d.rotate(Math.PI / 2);
        const wallLine = new Line3D(new Vec3(wallLine2d.start.x, 0, wallLine2d.start.y), new Vec3(wallLine2d.end.x, 0, wallLine2d.end.y));
        wallLine.setLength(20000); // TODO: This value is arbitrary, we might need to add options to set this via parameters
        this.interaction = new Interaction();
        this.interaction.dragger = "LineDragger";
        this.interaction.options = {
            line: wallLine,
        };
        this.interaction.control = this;
        this.root = new Mesh3D(new SphereGeometry(100, 32, 32), new MeshBasicMaterial({ color: 0x0000ff, transparent: true, opacity: 0.5 }))
            .setInteraction(this.interaction)
            .setPosition(initialPosition.x, initialPosition.y, initialPosition.z);
    }
    onDrag(info) {
        this.dragCallback(this);
    }
}
