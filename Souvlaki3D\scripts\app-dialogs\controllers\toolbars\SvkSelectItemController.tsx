import { DefaultSelectItemController } from "./DefaultSelectItemController";
import { StateService } from "../../../state/StateService";

export class SvkSelectItemController extends DefaultSelectItemController {

    public static $inject = ["StateService"];

    constructor(state: StateService) {
        super(state);
    }

    protected canSelectItem(): boolean {
        return location.hash !== "#power";
    }
}