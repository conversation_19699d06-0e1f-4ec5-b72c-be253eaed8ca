import { Mesh } from "three";
export class Mesh3D extends Mesh {
    interaction;
    setPosition(x = undefined, y = undefined, z = undefined) {
        this.position.set(x || 0, y || 0, z || 0);
        return this;
    }
    setPositionY(y) {
        this.position.setY(y);
        return this;
    }
    setRotation(x = undefined, y = undefined, z = undefined) {
        this.rotation.set(x || 0, y || 0, z || 0);
        return this;
    }
    lookAtPoint(point) {
        this.lookAt(point);
        return this;
    }
    setRenderOrder(order) {
        this.renderOrder = order;
        return this;
    }
    setInteraction(interaction) {
        this.interaction = interaction;
        return this;
    }
}
