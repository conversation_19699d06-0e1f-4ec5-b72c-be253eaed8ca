import RequireContext = __WebpackModuleApi.RequireContext;
import { CtorInfo } from "../models/infrastructure/CtorInfo";

export class Activator {

    private static constructors: { [key: string]: any } = {}; // Holds references to classes that are dynamically instantiated based on configuration.json etc
    private static instances: { [key: string]: any } = {}; // Holds dependencies that can be injected via constructors

    /** Return existing instance if exists, otherwise create a new and keep it for next request */
    public static get<T>(classType: string, ...args: unknown[]): T {
        if (this.instances[classType]) {
            return this.instances[classType];
        }

        const instance = this.getUnique<T>(classType, ...args);

        this.instances[classType] = instance;

        return instance;
    }

    /** Always create a new instance of the requested type */
    public static getUnique<T>(classType: string, ...args: unknown[]): T {
        const ctor = this.constructors[classType];
        if (!ctor) {
            console.warn(`Module "${classType}" is missing in the build`);
        }

        const dependencies: unknown[] = this.getDependencies(ctor).map(x => this.get(x));
        dependencies.push(...args);

        if (dependencies.length !== ctor.length) {
            console.warn(`Incorrect number of constructor ags for type ${classType}`);
        }

        return new ctor(...dependencies);
    }

    /** Register instance */
    public static register(classType: string, instance: unknown): void {
        this.instances[classType] = instance;
    }

    /** Register constructor */
    public static registerType(key: string, constructor: unknown): void {
        this.constructors[key] = constructor;
    }

    private static getDependencies(ctor: CtorInfo): string[] {
        if (Array.isArray(ctor.$inject)) {
            return ctor.$inject;
        }

        return ctor.$inject && "dependencies" in ctor.$inject ? ctor.$inject.dependencies : [];
    }

    public static importAll(r: RequireContext, isInstance: boolean): void {
        const cache = isInstance ? this.instances : this.constructors;

        r.keys().forEach((key: string) => {
            const module = r(key);
            for (const exportedMemberKey in module) {
                if (module.hasOwnProperty(exportedMemberKey)) {
                    const member = module[exportedMemberKey];
                    cache[this.getInjectKey(member, exportedMemberKey)] = member;
                }
            }
        });
    }

    /** In same cases dependencies will be resolved by interface name, in such cases the name needs to be specified in the class $inject settings. Otherwise, the default class name will be used */
    private static getInjectKey(ctor: CtorInfo, defaultKey: string): string {
        return ctor.$inject && "key" in ctor.$inject ? ctor.$inject.key : defaultKey;
    }
}

Activator.importAll(require.context("../app-3d", true, /\.tsx?$/), false);
Activator.importAll(require.context("../logic", true, /\.tsx?$/), false);
Activator.importAll(require.context("../app-dialogs", true, /\.tsx?$/), false);
Activator.importAll(require.context("../configurations", true, /\.tsx?$/), true);
Activator.importAll(require.context("../validators", true, /\.tsx?$/), false);
Activator.importAll(require.context("../state", true, /\.tsx?$/), false);
Activator.importAll(require.context("../infrastructure", true, /\.tsx?$/), false);