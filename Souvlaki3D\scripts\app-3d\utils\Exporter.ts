import { GLTFExporter } from "three/examples/jsm/exporters/GLTFExporter";
import { OBJExporter } from "three/examples/jsm/exporters/OBJExporter";
import { PLYExporter } from "three/examples/jsm/exporters/PLYExporter";
import { ColladaExporter } from "three/examples/jsm/exporters/ColladaExporter";
import { STLExporter } from "three/examples/jsm/exporters/STLExporter";
import { Camera, Mesh, Object3D } from "three";

export enum Formats {
    gltf = "gltf", // GL Transmission Format https://threejs.org/docs/index.html#examples/exporters/GLTFExporter
    glb = "glb", // a binary form of glTF that includes textures instead of referencing them as external images
    obj = "obj", // Alias Mesh
    ply = "ply", // Stanford Triangle Mesh https://threejs.org/docs/index.html#examples/exporters/PLYExporter
    stl = "stl", // Stl
    dae = "dae", // Collada, https://threejs.org/docs/index.html#examples/exporters/ColladaExporter
}

export class Exporter {

    public static async convert(scene: Object3D, filename: string, format: Formats, callback: (data: Blob, filename: string) => void): Promise<void> {
        const object3D = this.cleanScene(scene);
        let data: string | ArrayBuffer = null;

        filename = `${filename}.${format}`;
        callback = callback || this.download;

        switch (format) {
            case Formats.glb: {
                const gltfExporter = new GLTFExporter();
                data = await gltfExporter.parseAsync(object3D, { binary: true }) as ArrayBuffer;
                break;
            }

            case Formats.gltf: {
                const gltfExporter = new GLTFExporter();
                data = JSON.stringify(await gltfExporter.parseAsync(object3D, { binary: false }));
                break;
            }

            case Formats.obj:
                data = new OBJExporter().parse(object3D);
                break;

            case Formats.ply:
                data = new PLYExporter().parse(object3D, undefined, undefined);
                break;

            case Formats.dae:
                data = new ColladaExporter().parse(object3D, undefined, undefined).data;
                break;

            case Formats.stl:
                data = new STLExporter().parse(object3D, { binary: false });
                break;

            default:
                console.log(`"${format} is not supported"`);
        }

        if (data) {
            this.saveFile(data, filename, callback);
        }
    }

    private static saveFile(data: string | ArrayBuffer, filename: string, callback: (data: Blob, filename: string) => void): void {
        const blob = typeof data === "string"
            ? new Blob([data], { type: "text/plain" })
            : new Blob([data], { type: "application/octet-stream" });

        callback(blob, filename);
    }

    private static download(blob: Blob, filename: string): void {
        const link = document.createElement("a");
        link.style.display = "none";
        document.body.appendChild(link);

        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
    }

    private static cleanScene(object3D: Object3D): Object3D {
        const object = object3D.clone(true);
        const remove: Object3D[] = [];
        object.traverse(e => {
            const isTransparent = e instanceof Mesh ? e.material?.opacity === 0 && e.material.transparent : false;
            if ((!e.visible || isTransparent) && e.parent) {
                remove.push(e);
            }
            if (e instanceof Camera) {
                remove.push(e);
            }
        });

        for (const iterator of remove) {
            iterator.parent.remove(iterator);
        }

        return object;
    }
}