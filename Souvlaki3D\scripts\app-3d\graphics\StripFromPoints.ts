import { MovableItem } from "../../models/state/MovableItem";
import { CatmullRomCurve3, Color, DoubleSide, MeshBasicMaterial, Object3D, TubeGeometry, Vector3 } from "three";
import { CanvasUtils } from "../utils/CanvasUtils";
import { Container3D } from "./Container3D";
import { IInteractionOptions } from "../../models/positioning/IInteractionOptions";
import { Mesh3D } from "./Mesh3D";
import { Vec3 } from "@immugio/three-math-extensions";

export function StripFromPoints(m: MovableItem, points: Vector3[], color: string | Color, radius: number, interaction?: IInteractionOptions, opacity = 0.3): Object3D {
    if (points.length === 0) {
        return new Object3D();
    }

    const distinct: Vector3[] = [];
    for (const point of points) {
        const last = distinct.at(-1);
        if (!last || !last.equals(point)) {
            distinct.push(point);
        }
    }

    const vectors = distinct.map(p => new Vec3(
            p.x - m.x,
            p.y - m.y,
            p.z - m.z
        )
    );

    const curve = new CatmullRomCurve3(vectors);
    const geo = new TubeGeometry(curve, 64, radius, 64, false);
    const mat = new MeshBasicMaterial({ color: color, side: DoubleSide, transparent: true, opacity});
    const mesh = new Mesh3D(geo, mat).setRenderOrder(-1);

    CanvasUtils.applyReverseRotation(mesh, { rotationX: m.rotationX, rotationY: m.rotationY, rotationZ: m.rotationZ });
    return new Container3D(mesh).addInteraction(interaction);
}