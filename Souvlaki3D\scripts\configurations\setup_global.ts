import { IApplicationSettings } from "../models/infrastructure/IApplicationSettings";
import { AvailableActions, Display, ShowDimensions, UnitType } from "../models/common/Enums";
import { DoubleSide } from "three";

export const settings: IApplicationSettings = {
    app: {
        startUpBehaviors: null,
        runtimeBehaviors: null,
        controllers: null,
        containerId: null,
        dragRootId: null,
        configuratorId: null,
        configuratorCode: null,
    },

    view: {
        scaleRatio: 1.25,
        theta: 22,
        phi: 30,
        zoomExtends: false,
        displayMode: Display.PerspectiveView,
        showBuildingResize: true,
        showDimensions: ShowDimensions.Vertical | ShowDimensions.Horizontal,
        showLabels: false,
        unitType: UnitType.Metric,
        showPlane: false,
        backgroundColor: "#FFFFFF",
        backgroundColorExport: "#FFFFFF",
        backgroundAlpha: 1,
        ambientLightColor: "#4D4D4C",
        ambientLightIntensity: 1,
        ambientLightIntensityNight: 0.1,
        directionalLightColor: "#FFFFFF",
        directionalLightIntensity: 0.9,
        directionalLightIntensityNight: 0.1,
        availableActions: {
            [AvailableActions.Insert]: {},
            [AvailableActions.Rotate]: {},
            [AvailableActions.Move]: {},
            [AvailableActions.Resize]: {},
            [AvailableActions.Properties]: {},
            [AvailableActions.Power]: {},
            [AvailableActions.Delete]: {},
        },
        maxSuspension: 4000
    },

    interaction: {
        zoomStep: 1000,
        minDistPerspective: 2000,
        maxDistPerspective: 30000,
        minZoomOrtho: 0.1,
        maxZoomOrtho: 4,
        maxUndoSteps: 40
    },

    dimensions: {
        color: "#333333",
        fontSize: 60,
        lineOffset: 160,
        minDistanceToShow: 300,
        minDistanceToAddUnits: 700,
        minDistanceToAddArrows: 300,
        textMargin: 0,
        dimTypes: []
    },

    building: {
        defaultItemColor: "#ffffff",
        defaultItemEdgesColor: "#000000",
    },

    sizes: {
        baseHeight: 10,
        wallThickness: 100,
        partitionThicknessMm: 120,
        totalDefaultHeight: 2500,

        resizeFloorplanAlgorithm: "ResizeFpSingleWall",
        resizeFloorplanArrowSize: 1000,
        resizeFloorplanArrowDistance: 800,
        resizeFloorplanArrowMargin: 1300,
        resizeFpGraphics: "ResizeFpInGraphics"
    },

    roomSizeOptions: {
        minWidth: 2000,
        maxWidth: 16000,
        minDepth: 2000,
        maxDepth: 16000,
        minHeight: 2000,
        maxHeight: 12000,

        resizeStep: 100,
        resizeStepBuildingX: 100,
        resizeStepBuildingY: 100,
        resizeStepHeight: 100,
    },

    buildingTemplates: [
        {
            range: "Room",
            template: "roomRect",
            floor: [
                { x: 0   , y: 0   , z: 3000 },
                { x: 6000, y: 0   , z: 3000 },
                { x: 6000, y: 4000, z: 3000 },
                { x: 0   , y: 4000, z: 3000 },
                { x: 0   , y: 0   , z: 3000 }
            ],
            intFinishes: ["#f4f2ed"],
            extFinishes: ["#f4f2ed"],
            baseFinish: "#f4f2ed",
            height: 3000,
            baseHeight: 100,
            roofWidth: 50,
            wallThickness: 50,
            movables: []
        },
        {
            range: "Room",
            template: "roomL",
            floor: [
                { x: 0   , y: 0   , z: 3000 },
                { x: 3000, y: 0   , z: 3000 },
                { x: 3000, y: 2500, z: 3000 },
                { x: 7000, y: 2500, z: 3000 },
                { x: 7000, y: 5000, z: 3000 },
                { x: 0   , y: 5000, z: 3000 },
                { x: 0   , y: 0   , z: 3000 }
            ],
            intFinishes: ["#FFFFFF"],
            extFinishes: ["#D0D0D0"],
            baseFinish: "#696969",
            height: 3000,
            baseHeight: 100,
            roofWidth: 50,
            wallThickness: 50,
            movables: []
        },
        {
            range: "Room",
            template: "roomT",
            floor: [
                { x: 0   , y: 2500, z: 3000 },
                { x: 2500, y: 2500, z: 3000 },
                { x: 2500, y: 0   , z: 3000 },
                { x: 4500, y: 0   , z: 3000 },
                { x: 4500, y: 2500, z: 3000 },
                { x: 7000, y: 2500, z: 3000 },
                { x: 7000, y: 5000, z: 3000 },
                { x: 0   , y: 5000, z: 3000 },
                { x: 0   , y: 2500, z: 3000 }
            ],
            intFinishes: ["#FFFFFF"],
            extFinishes: ["#D0D0D0"],
            baseFinish: "#696969",
            height: 3000,
            baseHeight: 100,
            roofWidth: 50,
            wallThickness: 50,
            movables: []
        }
    ],

    visualStates: [
    ],

    movables: [
        {
            type: "Group", width: 0, height: 0, depth: 0,  typology: null, shape: null, placementMode: null, snaps: null, radiusPoints: null, holderPoints: null, childrenTypes: null, attachmentPoints: null,
            skin: { graphics3D: "EmptyGraphics", data: { finishes: ["highlight"] } }
        }
    ],

    textures: {
        invisible: { transparent: true, opacity: 0 },
        initial: { color: "#0000FF" },
        highlight: { color: "#f44141", opacity: 0.3 },
        dropzone: { color: "#caf441", opacity: 0.2, transparent: true },
        invalid: { color: "#f45c42" },
        detached: { color: "yellow" },
        selected: { color: "#008080" },
        powerActive: { color: "green" },
        powerActiveOuter: { color: "#ffffff", transparent: true, opacity: 0.5 },
        powerDefault: { color: "#ffffff" },
        powerDisabled: { color: "#ffffff", transparent: true, opacity: 0.5 },
        powerConnected: { color: "#ace35b" },
        powerColliding: { color: "red" },
        overweight: { color: "red", opacity: 0.1, transparent: true, depthTest: false },
        underweight: { color: "green", opacity: 0.1, transparent: true, depthTest: false },

        defaultWallFinish               : { color: "#f4f2ed" },
        defaultWallFinishTransparent    : { color: "#f4f2ed", transparent: true, opacity: 0 },
        defaultWallFinishSemiTransparent: { color: "#f4f2ed", transparent: true, opacity: 0.05 },
        wallsTopFinish                  : { color: "#161719" },

        cable: { color: "#ffffff", metalness: 0.8, roughness: 0.3, side: DoubleSide },
        movable: { color: "#ffffff", metalness: 0.8, roughness: 0.3, side: DoubleSide },
    },

    messages: {
        insertError1     : "Compatible Target Missing",
        insertError2     : "This item can not be inserted into the current drawing",
        nothingToUndo    : "There are no steps to undo",
        nothingToRedo    : "There are no steps to redo",
        cmRotate         : "Rotate",
        brushSelectSource: "Select source item",
        brushSelectTarget: "Select target",
        brushCopied      : "Copied!",
        elementRemoved   : "Element(s) removed",
        rulerStart       : "Click on starting point",
        undoToRevert     : "Click undo to revert",
        cannotResize     : "Cannot resize due to the lack of space",
        cannotFlip       : "Cannot flip due to the ceiling distance",
    },

    subfamilyMap: [],

    powerSettings: {
        sharpingOrDiffused: [],
        maximumCircuitLengthsPerPowerPointOutput: [
            { effectiveOutput: null, maxLengthCm: 1400 }
        ]
    },
};
