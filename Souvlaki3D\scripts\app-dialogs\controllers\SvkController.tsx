import { MessageHub } from "../../infrastructure/MessageHub";
import { Events } from "../../models/infrastructure/Events";
import { IApplicationState } from "../../models/state/IApplicationState";
import { deleteMovables } from "../../state/actions/deleteMovables";
import { toggleFeedPoints } from "../../state/actions/toggleFeedPoints";
import { getRelativeConnections } from "../../logic/helpers/movables/getRelativeConnections";
import { Api } from "../../logic/algorithms/other/Api";
import { IBaseResponse } from "../../models/api/IBaseResponse";
import { Label } from "../../models/state/Label";
import { Formats } from "../../app-3d/utils/Exporter";
import { setSelected } from "../../state/actions/setSelected";
import { attachMovable } from "../../state/actions/attachMovable";
import { getConnectedGroups } from "../../logic/helpers/positioning/getConnectedGroups";
import { setApplicationData } from "../../state/actions/setApplicationData";
import { initializeDictionary } from "../services/Translator";
import { Modal } from "../services/Modal";
import { IPoint2D } from "../../models/geometry/IPoint2D";
import { IInsertingEndedEvent } from "../../models/events/IInsertingEndedEvent";
import { IInteractionOptions } from "../../models/positioning/IInteractionOptions";
import { MovableItem } from "../../models/state/MovableItem";
import { IPowerSourceUpdatedEvent } from "../../models/events/IPowerSourceUpdatedEvent";
import { isDesignPowered } from "../../logic/helpers/power/isDesignPowered";

export interface IServerConfig {
	language: string;
	url_applicationData: string;
	urlShowBom: string;
	urlCalcBom: string;
	urlCsvDownload: string;
	urlXlsDownload: string;
	// Loaded data:
	productConfigurationId: number;
	productConfigurationName: string;
	jsonGlobalValues: string;
	sceneJson: string;
	developmentEnvironment: boolean;
}

export interface IElement {
	kelvin: string[];
	beam: string[];
	jointFlag: boolean;
	attachment: boolean;
	power: number;
	colorIta: string[];
	control: string[];
	modelUrl: string;
	pendantLength: number;
}

export interface ISvkServerData {
	models: { [model: string]: IElement};
	powerSupplyWatts: number[]; // Array with power supply values, higher to lower: [216, 185, 130]
	// gatewayWatts: number; // e.g. 5
	dictionary: {key: string; translation: string}
}

interface IGlobalValues {
	fixingType: string;
	rodSize: string;
	gateway: boolean;	
	multiplier: number;
	structureColor: string;
}

export class SvkController {

	public static $inject = ["StateService", "Api"];

	private serverConfig: IServerConfig = window["config"]
	
	private cableRangeKey = 'pendantLengthMinMaxDef';
	
	private currentStepName : string;

	public static globalValues: IGlobalValues;
	private productConfigurationId = this.serverConfig.productConfigurationId; // Will be set on load
	private productConfigurationName = this.serverConfig.productConfigurationName; // Will be set on load
	// Used to save and restore a select when a property is forced on drag
	// Not used anymore
	// private $propertyFormUnselected: JQuery<HTMLElement>; // Cache of the form when no element is selected
	private cursorPosition: IPoint2D = {x:0, y:0};
	private insertedItem: IInteractionOptions;
	private $propertiesPopup: JQuery;
	private $structureColors: JQuery;
	private $attachmentColors: JQuery;

	private gatewayChecked = false;

	constructor(private state: IApplicationState<ISvkServerData>, private engine: Api) {
		this.globalValues = {
			fixingType: "",
			rodSize: "",
			gateway: false,
			multiplier: 0,
			structureColor: ""
		};
		this.initializeStepsHandling();
		this.initializeExportHandling();
		this.initializeSaveHandling();
		this.initializeElementPropertiesHandling();
		this.initializeGui();

		MessageHub.subscribe(Events.engineReady, () => {
			// Load scene if any
			this.loadScene(this.serverConfig.jsonGlobalValues, this.serverConfig.sceneJson);
			// Load element definitions
			this.loadDatabase(this.serverConfig.url_applicationData, () => {
				// Set correct step if contained in the url
				if (location.hash) {
					if (this.state.design.movableItems.length>0) {
						this.showStepByName(location.hash);
					} else {
						location.hash = "draw"; // On empty config always load the draw step
					}
				}
			});
			// Remove loader
			window.setTimeout(() => {
				$("#loading-indicator").hide();
			}, 2000)
		});

		// Save mouse position
		document.addEventListener("mousemove", (e: MouseEvent) => this.setCursorPosition(e.clientX, e.clientY));
	}

	private get globalValues(): IGlobalValues {
		return SvkController.globalValues;
	}
	private set globalValues(value: IGlobalValues) {
		SvkController.globalValues = value;
	}

	private updateFixingTypeValue() {
		const current = $<HTMLInputElement>(".option.selected input[name='fixingType']")[0].value;

		if(current != this.globalValues.fixingType) {
			this.globalValues.fixingType = current;
			MessageHub.broadcast(Events.fixingTypeChanged, current)
		}
	}
	
	private initializeGui(): void {
		// Show proper step on back/forward
		window.onpopstate = () => setTimeout(() => {
			// Using a zero timeout as suggested here https://developer.mozilla.org/en-US/docs/Web/API/Window/popstate_event
			this.showStepByName(location.hash);
		}, 0);

		// Fixing type selection
		$("#fixingTypeContainer .option").on("click", e => {
			$("#fixingTypeContainer .option.selected").removeClass("selected");
			$(e.target).closest(".option").addClass("selected");
			this.updateFixingTypeValue();
		});
		// Update values at init
		this.updateFixingTypeValue();

		// Handle structure color changes
		this.globalValues.structureColor = $("#structureColors").val() as string;
		$("#structureColors").on("change", e => {
			const selectedColor = $(e.target).val() as string;
			this.globalValues.structureColor = selectedColor;
			this.updateStructureTitle(selectedColor);
			// Set all existing movable colors to the new selected color, but only if they are not accessories
			this.state.design.movableItems.forEach(m => {
				if (!m.data.attachment) {
					m.data.colorIta = selectedColor;
				}
			});
	
		});
		// Set initial structure title
		this.updateStructureTitle($("#structureColors").val() as string);

		// Handler for the pendant cable length input range
		$(`.elementProperties input[type=range][name=pendantLength]`)
			.on('input', (e) => 
				$('.displayCableLength', $(e.target).closest(".elementProperties")).text((e.target as HTMLInputElement).value)
			);

		// Preset the save name
		if (this.serverConfig.productConfigurationName) {
			$("#saveConfigurationForm input[name=saveName]").val(this.serverConfig.productConfigurationName);
		}

		// Update Power UI on changes
		MessageHub.subscribe<IPowerSourceUpdatedEvent>(Events.powerSourceUpdated, m => {this.onPsuUpdate(m)});

		$("#gateway").on('change', e =>{
			MessageHub.broadcast<boolean>(Events.requestGatewayUpdate, (e.target as HTMLInputElement).checked);
			this.updateTotalPowerDraw();
			this.gatewayChecked = (e.target as HTMLInputElement).checked;
		});

		MessageHub.subscribe(Events.gatewayPlacementFailed, () => this.onGatewayPlacementFailure());

		// Handle the double color select
		this.$structureColors = $("#structureColors");
		this.$attachmentColors = $("#attachmentColors").detach();
	}

	private setCursorPosition(x: number, y: number): void{
		this.cursorPosition.x = x;
		this.cursorPosition.y = y;
	}

	/**
	 * Loads data from the backend
	 */
	private loadDatabase(url: string, callback: Function): void {
		// yada.ajax(url, null, jsonResult => setApplicationData<ISvkServerData>(jsonResult));
		yada.ajax(url, null, jsonResult => {
			let allExceptDictionary = {...jsonResult};
			delete allExceptDictionary.dictionary;
			setApplicationData<ISvkServerData>(allExceptDictionary)
			// Map of keyword > value, already translated to the target language. The keyword is actually a phrase.
			initializeDictionary(jsonResult.dictionary);
			callback();
		});
	}

	/**
	 * Changes the looks of the element property select
	 */
	private flashPropertyOk($select: JQuery): void {
		$select.addClass("flash ok");
	}

	/**
	 * Step handling
	 */
	private initializeStepsHandling(): void {
		$(".stepDraw .bottom.structure button").on("click", () => {
			if (this.state.design.movableItems.length==0) {
				Modal.showErrorModal("Invalid configuration", "The configuration is empty");
				return;
			}
			if (this.state.design.movableItems.length==1 && this.state.design.movableItems[0].data.jointFlag) {
				Modal.showErrorModal("Invalid configuration", "Configuration is incomplete");
				return;
			}
			if (!this.isOneGroupOnly()) {
				Modal.showErrorModal("Invalid configuration", "The elements must all be connected to each other without interruption of continuity");
				return;
			}
			this.showStepAddons();
		});
		$(".stepDraw .bottom.addons button:first-child").on("click", () => {
			this.showStepDraw();
		});
		$(".stepDraw .bottom.addons button:last-child").on("click", () => {
			this.showStepPower();
		});
		$(".stepPower .bottom button:first-child").on("click", () => {
			this.pushGatewayOptionState();
			this.showStepAddons();
		});
		$(".stepPower .bottom button:last-child").on("click", () => {
			let multiplier = parseInt($("#multiplier").val() as string, 10);
			if (multiplier<1) {
				// Value is required (client specification)
				$("#multiplier").addClass("error");
				if (this.isNotDeveloment()) {
					Modal.showErrorModal("Multiplier required", "Please specify the number of configurations needed");
					return;
				} else {
					console.log("Multiplier forced to 1 in development")
					multiplier=1; // In development, force 1
				}
			} else {
				$("#multiplier").removeClass("error");
			}
			this.globalValues.multiplier = multiplier;
			// Force choice of feed point
		   if(!isDesignPowered(this.state.design)) {
				Modal.showErrorModal("Power source required", "Please specify the starting power supply");
				return;            
			}						
			// Force save if logged in
			const loggedIn = $("header").hasClass("loggedIn");
			if (loggedIn) {
				this.openSaveModal(true); // true to open the BOM page
			} else {
				this.showStepBom();
			}
		});
		$(".stepBom .bottom button").on("click", () => {
			this.showStepPower();
		});
	}

	/**
	 * Check if the configuration is made of connected elements only: no isolated groups
	 */
	private isOneGroupOnly(): boolean {
		const groups = getConnectedGroups(this.state.design.movableItems);
		return groups.length < 2;
	}

	/**
	 * Adds and removes elements as needed
	 */
	private cleanupConfiguration(): void {
		// - remove any dangling non-terminal joints
		// - add any missing terminal joints
		const terminalJointType = $(".icon[data-terminaljoint]").data("type");
		let graph = getRelativeConnections(this.state.design);
		let jointDeleted = false;
		// When we get here there must be at least one non-joint element and all elements are connected, because of previous checks.
		for (const relativeConnection of graph) {
			const movableItem = relativeConnection.movable;
			const isJoint = movableItem.data.jointFlag;
			const isTerminal = movableItem.type == terminalJointType;
			if (isJoint && !isTerminal && relativeConnection.connected.length<2) {
				deleteMovables([movableItem.id], false);
				jointDeleted=true;
			}
		}
		// Reload the connections after deleting the dangling joints
		if (jointDeleted) {
			graph = getRelativeConnections(this.state.design);
		}
		for (const relativeConnection of graph) {
			const movableItem = relativeConnection.movable;
			const isTerminal = movableItem.type == terminalJointType;
			if (!isTerminal && relativeConnection.connected.length<2 && movableItem.parentId==null) {
				// This element is not connected to both sides.
				// Add terminal joint, one for each empty snaps
				const colorIta = movableItem.data.colorIta;
				const emptySnaps: number[] = [0, 1];
				const availableSnaps: number[] = emptySnaps.filter((snap) => {return !relativeConnection.connected.some((m) => m.movableSnapIndex === snap);});
				for (const snap of availableSnaps) {
					attachMovable(movableItem.id, snap, terminalJointType, {
						colorIta: colorIta,
						jointFlag: true
					});
				}
			}
		}
	}

	private onPsuUpdate(event: IPowerSourceUpdatedEvent): void {
		if(event.powerSupplies.length) {
			$("#powerSupplyTot").text(event.powerSupplies.length);
		} else {
			$("#powerSupplyTot").text('-');
		}
	}

	private pushGatewayOptionState() {
		this.gatewayChecked = $('#gateway').is(':checked');
		$('#gateway').prop('checked', false).trigger("change");
	}

	private popGatewayOptionState() {
		$('#gateway').prop('checked', this.gatewayChecked).trigger("change");
	}

	private onGatewayPlacementFailure() {
		$('#gateway').prop('checked', false).trigger("change");
		Modal.showErrorModal("Invalid configuration", "No free space found to place a gateway");
	}

	private showStepDraw(): void {
		$("#sidebar > .stepDraw .palette.addons").addClass("hidden");
		$("#sidebar > .stepDraw .bottom.addons").addClass("hidden");
		$("#sidebar > .stepDraw .beamGroup").addClass("hidden");
		$("#sidebar > .stepDraw .suspensionCableGroup").addClass("hidden");
		$("#sidebar > .stepDraw .palette.structure").removeClass("hidden");
		$("#sidebar > .stepDraw .bottom.structure").removeClass("hidden");
		$("#sidebar > .stepDraw").removeClass("stepAddons");
		this.currentStepName = "draw";
		location.hash = this.currentStepName;
		$("#attachmentColors").detach();
		$("#colorLabel").after(this.$structureColors);
		// Update structure title with current color
		this.updateStructureTitle($("#structureColors").val() as string);
	}

	/**
	 * Updates the structure title with the selected color
	 */
	private updateStructureTitle(color: string): void {
		const structureSpan = $("#sidebar > .stepDraw .palette.structure h3 span");
		const colorText = $(`#structureColors option[value='${color}']`).text();
		const structureText = structureSpan.text().split(" ")[0]; // Get the word "Structure" in current language
		structureSpan.text(`${structureText} (${colorText})`);
	}

	private showStepAddons(): void {
		this.cleanupConfiguration();
		// The "addon" step is not like the others: it is inside the draw step
		$("#sidebar > .stepDraw .palette.structure").addClass("hidden");
		$("#sidebar > .stepDraw .palette.addons").removeClass("hidden");
		$("#sidebar > .stepDraw .beamGroup").removeClass("hidden");
		$("#sidebar > .stepDraw .suspensionCableGroup").removeClass("hidden");
		$("#sidebar > .stepDraw .bottom.structure").addClass("hidden");
		$("#sidebar > .stepDraw .bottom.addons").removeClass("hidden");
		$("#sidebar > .stepDraw").addClass("stepAddons");
		$("#sidebar > .stepDraw").removeClass("hidden");
		this.currentStepName = "addons";
		location.hash = this.currentStepName;
		toggleFeedPoints(false)
		$("#structureColors").detach();
		$("#colorLabel").after(this.$attachmentColors);

		// Set default beam value only if none is selected
		const $beamSelect = $("#elembeam");
		if (!$beamSelect.val()) {
			const firstOption = $beamSelect.find("option:first");
			if (firstOption.length) {
				$beamSelect.val(firstOption.val());
			}
		}
	}

	private showStepPower(): void {
		this.cleanupConfiguration(); // Needed on reload
		$("#sidebar > .stepDraw").addClass("hidden");
		$("#sidebar > .stepBom").addClass("hidden");
		$("#bom").addClass("hidden");
		$("#sidebar > .stepPower").removeClass("hidden");
		$("#app-3d").removeClass("hidden");
		// $("#toolbar").show();
		$("#toolbar li:not(:has([title='Save']))").show();
		this.currentStepName = "power";
		location.hash = this.currentStepName;
		toggleFeedPoints(true);

		this.updateTotalPowerDraw();

		// Update the labels
		const powerSupplies = this.state.design.movableItems.filter(x => x.data.powerSource?.length );
		this.onPsuUpdate({powerSupplies});

		// Update the gateway checkbox
		const hasDaliDriver = this.state.design.movableItems.find(x => x.data.control == 'Dali' );
		$("#gateway").prop("disabled", !hasDaliDriver);
		
		if(hasDaliDriver) {
			this.popGatewayOptionState();
		} else {
			this.gatewayChecked = false;
		}
	}

	// Calc total power consumption
	private updateTotalPowerDraw() {
		const movables = this.state.design.movableItems;
		
		let totPower: number = 0;
		for (const movable of movables) {
			totPower = totPower + Number(movable.data?.power || 0);
		}

		$("#totalPower").text(totPower.toFixed(2));
	}

	public showStepBom(): void {
		//MessageHub.broadcast(Events.requestPSUPlacement);
		yada.loaderOn();
		$("#sidebar > :not(.stepBom)").addClass("hidden");
		$("#app-3d").addClass("hidden");
		$("#sidebar > .stepBom").removeClass("hidden");
		$("#bom").removeClass("hidden");
		// Do not hide the save button
		// $("#toolbar").hide();
		$("#toolbar li:not(:has([title='Save']))").hide();
		this.currentStepName = "bom";
		location.hash = this.currentStepName;
		toggleFeedPoints(false);
		this.calcBom();
	}

	/**
	 * Call backend with configuration values in order to get the BOM with labels (aka "assemble")
	 */
	private calcBom(): void {
		const graph = getRelativeConnections(this.state.design);
		this.globalValues.rodSize = (document.querySelector("#rodSize") as HTMLInputElement)?.value;
		this.globalValues.gateway = (document.querySelector("#gateway") as HTMLInputElement)?.checked;
		// this.globalValues.productConfigurationId = this.productConfigurationId;
		const formData = new FormData();
		formData.append("jsonConnections", JSON.stringify(graph));
		formData.append("jsonGlobalValues", JSON.stringify(this.globalValues));
		yada.ajax(this.serverConfig.urlCalcBom, formData, r => this.showBom(r), "POST");
	}

	/**
	 * Calls the backend to retrieve the BOM page
	 */
	private showBom(responseText: IBaseResponse & { labels: Partial<Label>[]}): void {
		// This method is called to process the "calcBom" return json.
		// It then calls the "showBom" url.
		if (responseText.errorMessage) {
			Modal.showErrorModal(responseText.errorTitle, responseText.errorMessage);
			return;
		}
		// Send the labels to the engine
		this.engine.setLabels(responseText.labels);
		// Take the screenshots
		this.engine.showLabels(true);
		this.engine.updateScene({ view: { phi: 90, theta: 0, displayMode: "TopView" } });
		let screenshot = this.engine.screenshot(screen.width, screen.height);
		sessionStorage.setItem("TopView", screenshot);
		this.engine.updateScene({ view: { phi: 45, theta: 45, displayMode: "PerspectiveView" } });
		screenshot = this.engine.screenshot(screen.width, screen.height);
		sessionStorage.setItem("PerspectiveView", screenshot);
		this.engine.updateScene({ view: { phi: 0, theta: 0, displayMode: "FrontView" } });
		screenshot = this.engine.screenshot(screen.width, screen.height);
		sessionStorage.setItem("FrontView", screenshot);
		this.engine.updateScene({ view: { phi: 0, theta: 90, displayMode: "SideView" } });
		screenshot = this.engine.screenshot(screen.width, screen.height);
		sessionStorage.setItem("SideView", screenshot);
		this.engine.showLabels(false);
		this.engine.updateScene({ view: { phi: 45, theta: 45, displayMode: "PerspectiveView" } });
		//
		const formData = new FormData();
		formData.append("productConfigurationId", this.productConfigurationId?.toString());
		formData.append("topView", sessionStorage.getItem("TopView"));
		formData.append("perspectiveView", sessionStorage.getItem("PerspectiveView"));
		formData.append("frontView", sessionStorage.getItem("FrontView"));
		formData.append("sideView", sessionStorage.getItem("SideView"));
		// Call the URL that returns the BOM Table HTML
		yada.ajax(this.serverConfig.urlShowBom, formData, this.insertBom.bind(this), "POST");
	}

	private insertBom(responseText: string, responseHtml: string): void {
		// The response is just the HTML of the BOM table
		$("#bom").html(responseHtml);
		// Store the productConfigurationId locally
		this.productConfigurationId = this.serverConfig.productConfigurationId;
		yada.loaderOff();
	}

	/**
	 * Used by back/forward to show the correct step based on the location hash
	 */
	private showStepByName(stepName: string): void {
		stepName = yada.getHashValue(stepName); // Remove # if present
		if (stepName === this.currentStepName) {
			// Need to do this because location.hash="xxx" triggers window.onpopstate that calls this method again (see configurator.html)
			return;
		}
		switch (stepName) {
			case "draw":
				this.showStepDraw();
				break;
			case "addons":
				this.showStepAddons();
				break;
			case "power":
				this.showStepPower();
				break;
			case "bom":
				this.showStepBom();
				break;
			default:
				this.showStepDraw();
		}
	}

	//
	// Export
	//
	private initializeExportHandling(): void {
		$(".stepBom .export a:not(.pdfDownload):not(.csvDownload):not(.xlsDownload)").on("click", e => {
			e.preventDefault();
			const hashedType = $(e.target).attr("href");
			//const hashedType = $(e.target).closest("a").attr("href");
			const format: string = hashedType.split("#")[1];
			this.exportScene(format);
		});
	}

	private exportScene(format: string): void {
		if (format in Formats) {
			const name = this.productConfigurationName==null?"":"_"+this.productConfigurationName;
			const id = this.productConfigurationId==null?"":"_"+this.productConfigurationId;
			const outputName = "souvlaki" + name + id;
			this.engine.convert(outputName, Formats[format]);
			return;
		}
		console.error("Invalid export format: " + format);
	}

	//
	// Selecting elements and changing properties
	//
	private initializeElementPropertiesHandling(): void {
		// Subscribe to element added to scene
		MessageHub.subscribe<IInsertingEndedEvent>(Events.insertingEnded, e => this.onInsertEnded(e));
		// Subscribe to element(s) being selected
		MessageHub.subscribe(Events.drawingInteraction, () => this.checkItemSelected());
	}

	private onInsertEnded(e: IInsertingEndedEvent) {
		if(!e.success) {
			return;
		}
		this.insertedItem = e.item;
	}
	
	/**
	 * Remove any options that are not available for the current object or selection
	 */
	private fixPopupProperties($select: JQuery, commonProperties: IElement, selectedMovables: MovableItem[]): void {
		const propertyName = $select.attr('name'); // "beam"
		const firstValue = selectedMovables[0].data[propertyName];
		const allSameValue = selectedMovables.every(m => m.data[propertyName] == firstValue);
		const propertyValue = allSameValue?firstValue : "";
		if (propertyValue!=null) {
			// Remove options that are not available
			const availableValues = commonProperties[propertyName]; // ["18°", "24°", "36°", "60°"]
			if (availableValues == null || availableValues.length == 0) {
				// This property can't be set: just ignore it.
				$select.prop('disabled', true);
			} else {
				// There is at least one value in common to all the selected movables.
				$("option", $select).each((index: number, option: HTMLOptionElement) => {
					if (!availableValues.includes(option.value)) {
						// Remove options not in common
						$(option).remove();
						// $(option).addClass('hidden');
					}
				});
				$select.val(propertyValue);
				this.flashPropertyOk($select);
			}
		} else {
			if (propertyName=="beam" && selectedMovables.every(m => m.data.attachment === false)) {
				// Hide beam from non-attachments
				$select.addClass('hidden');
			}
			// Property can't be selected
			$select.prop('disabled', true);
		}
	}
	
	/**
	 * Shows the properties popup on element add or element select
	 */
	private showPropertiesPopup(commonProperties: IElement | null = null, selectedMovables: MovableItem[]) {
		const hasAttachment = selectedMovables.some(m => m.data.attachment === true);
		// The popup is a clone of ".propopup", that is itself a thymeleaf copy of the sidebar properties box with some added HTML
		if (this.$propertiesPopup){
			this.$propertiesPopup.remove();
		}

		this.$propertiesPopup = $(".propopup").clone(true);
		this.$propertiesPopup.addClass("cloned");
		$(".propopup").parent().prepend(this.$propertiesPopup);

		// Copy the selects from the sidebar to the popup, except colorIta that is copied from a different select that holds all colors
		$("#sidebar .elementProperties select").each((index: number, select: HTMLSelectElement) => {
			const propertyName = select.name; // "beam"
			const className = select.className; // "structureColor"
			var $clonedSelect = $(select).clone(true);
			if (propertyName=="colorIta") {
				// Need to clone from a different select
				$clonedSelect = $(".propopup:not(.cloned) .allColors").clone(true) as JQuery<HTMLSelectElement>;
			}
			$clonedSelect.removeAttr('id');
			$clonedSelect.removeAttr('disabled');
			$clonedSelect.val(select.value);
			$("select[name=" + propertyName + "]", this.$propertiesPopup).replaceWith($clonedSelect);
			this.fixPopupProperties($clonedSelect, commonProperties, selectedMovables);
		});

		this.$propertiesPopup.removeClass("hidden");

		// Show or hide the bean group depending on the commonProperties beam property
		if (commonProperties && (commonProperties as any).beam && hasAttachment) {
		 	this.$propertiesPopup.find('.beamGroup').removeClass("hidden");
		 }
		// else {
		// 	// I don't think it ever enters here because the beam[] will always have at least one value even when disabled
		// else {
		// 	// I don't think it ever enters here because the beam[] will always have at least one value even when disabled
		// 	this.$propertiesPopup.find('.beamGroup').addClass("hidden");
		// }

		// Only show suspension cable input when pendantLengthMinMaxDef property is present
		if(commonProperties && (commonProperties as any).pendantLengthMinMaxDef) {
			const values = (commonProperties as any).pendantLengthMinMaxDef;

			$(`input[type=range][name=pendantLength]`, this.$propertiesPopup)
			// Commented because this event is registered in initializeGui() now
			//.on('input', (e) => 
			//    $('.displayCableLength', this.$propertiesPopup).text((e.target as HTMLInputElement).value)
			//)
			.attr('min', values[0])
			.attr('max', values[1])
			.val(values[2])
			.trigger('input');

			this.$propertiesPopup.find('.suspensionCableGroup').removeClass("hidden");
		}else{
			this.$propertiesPopup.find('.suspensionCableGroup').addClass("hidden");
		}
		
		// Add drag handler on the title bar
		const $titleBar = $(".title", this.$propertiesPopup);
		this.handlePopupDragging($titleBar);
		  
		// Set initial popup position
		const height = this.$propertiesPopup.outerHeight();
		const width = this.$propertiesPopup.outerWidth();
		this.$propertiesPopup.css({"left": (this.cursorPosition.x - width - 10) + 'px', "top": (this.cursorPosition.y - height/2) + 'px'});
	}
	
	private handlePopupDragging($titleBar: JQuery) {
		$titleBar.on('mousedown', (e) => {
			let startPosX = e.clientX;
	  		let startPosY = e.clientY;
	  		let initialPopupOffset = this.$propertiesPopup.offset();
			
			const onMouseMove = (e: JQuery.Event) => {
			  const dx = e.clientX - startPosX;
			  const dy = e.clientY - startPosY;
			  this.$propertiesPopup.css('left', initialPopupOffset.left + dx + 'px');
			  this.$propertiesPopup.css('top', initialPopupOffset.top + dy + 'px');
			}
			
			const onMouseUp = () => {
			  $(document).off('mousemove', onMouseMove);
			  $(document).off('mouseup', onMouseUp);
			}
			
			$(document).on('mousemove', onMouseMove);
			$(document).on('mouseup', onMouseUp);
		});        
	}
	
	private hidePropertiesPopup() {
		if(!this.$propertiesPopup) return;
		this.$propertiesPopup.remove();
		this.$propertiesPopup = null;
	}
	
	private showSelectionDescription(description: string | null = "") {
		if (this.$propertiesPopup) {
			$(".title", this.$propertiesPopup).text(description);
		}
		// $("#selectedDesc").text(description);
	}

	/**
	 * Called each time there is a drawing interaction, it checks if there is either a newly inserted element
	 * or a selection of elements.
	 */
	private checkItemSelected(): void {
		// When an element is selected, update the properties form with its values.
		// When an element is deselected, restore the original form.
		// Couldn't use Events.itemSelected because I need to catch deselection too.
		// Also change the description in the toolbar.
		// Now it handles multiple selections: only common properties can be changed to common values.
		const selectedMovables = this.state.design.movableItems.filter(x => x.selected);

		// When an element has been added to the scene
		if(this.insertedItem && this.insertedItem.movable){
			selectedMovables.push(this.insertedItem.movable);
			this.insertedItem = null;
		}

		const deselected = selectedMovables.length==0;

		if (deselected) {
			// On deselection, restore the form and hide properties popup
			// this.restorePropertyForm();
			this.hidePropertiesPopup();
			return;
		}

		// Element selected
		// When directly passing from a select to another, restore the form.
		// In such case the $propertyFormUnselected variable is not null.
		// if (this.$propertyFormUnselected!=null) {
		// 	this.restorePropertyForm();
		// }

		// Save the current selects
		// this.savePropertyForm();

		// Find the properties that are common to all the selected elements, and the common values
		const databaseSelectedModels = selectedMovables.map(sm => this.state.applicationData.models[sm.type]);
		const commonProperties = this.getCommonProperties(databaseSelectedModels);

		this.showPropertiesPopup(commonProperties, selectedMovables);
		const allAttachment = selectedMovables.every(m => m.data.attachment === true);

		// Handle change of value via select
		$(".elementProperties select", this.$propertiesPopup).on("change", e => {
			const name = (e.target as HTMLSelectElement).name;
			const value = (e.target as HTMLSelectElement).value;
			selectedMovables.forEach(m => m.data[name] = value);
			if (name === "colorIta" && !allAttachment) {
				$("#structureColors").val(value).trigger('change');
			}
		});

		// Initialize change of value via range input
		$('.elementProperties input[type=range]', this.$propertiesPopup).each((index: number, input:HTMLInputElement)=>{
			const $input = $(input);
			const name = input.name; // e.g. "pendantLength"
			const movable = selectedMovables[0];
			
			// If the movable has this property, initialize the input
			if(movable.data[name]) {
				input.value = (movable.data[name]);
				$input.trigger('input')
			}

			// Handle change of value via range input
			$input.on('input', (e) =>{    
				const name = (e.target as HTMLInputElement).name;
				const value = (e.target as HTMLInputElement).value;
				selectedMovables.forEach(m => {
					m.data[name] = parseFloat(value)
					m.refresh = true;
				});
			});
		})
		
		// Clicking on the sidebar outside the form resets the selection and the form itself
		$("#sidebar").on("mousedown", (e) => { this.resetSelection(e); this.hidePropertiesPopup(); });

		// Also change the description in the toolbar
		const multiselect = selectedMovables.length>1;
		if (!multiselect) {
			const description = $("#sidebar .palette .icon[data-type='"+selectedMovables[0].type+"'] div").text();
			this.showSelectionDescription(description);
		} else {
			this.showSelectionDescription("---");
		}
	}

	/**
	 * Given an array of applicationData.models, return only the common values
	 */
	private getCommonProperties(models: IElement[]): IElement {
		const commonProperties = {};
		const allKeys: string[][] = [];

		// Find the keys intersection
		models.forEach(model => allKeys.push(Object.keys(model))); // ["kelvin", "beam", "attachment", ...]
		let commonKeys = allKeys.reduce((a, b) => a.filter(c => b.includes(c)));

		//TODO: Replace with a more general solution
		if(commonKeys.find((key)=> key == this.cableRangeKey)){
			let areSame = true;
   
			for (let i = 1; i < models.length; i++) {
				const model1 = models[i-1];
				const model2 = models[i];
				const value1 = model1[this.cableRangeKey];
				const value2 = model2[this.cableRangeKey];
				
				if(value1[0] != value2[0] || value1[1] != value2[1]){
					areSame = false;
					break;
				}
			}

			if(areSame){
				commonProperties[this.cableRangeKey] = [...models[0][this.cableRangeKey]];
			}

			commonKeys = commonKeys.filter((key)=> key != this.cableRangeKey);
		}
		
		commonKeys.forEach((key)=>{
			// Assuming properties on all objects are the same type
			models.forEach(model=>{
				const value = model[key];
				const isArray = Array.isArray(value);
				
				if(!commonProperties[key]) {
					if(isArray) {
						commonProperties[key] = [...value];
					}
					else {
						commonProperties[key] = value;
					}
				}else {
					if(isArray) {
						// TODO: Add merge strategy based on property
						commonProperties[key] = commonProperties[key].filter(v => value.includes(v));
					}else{
						// TODO: Merge/filter based on type
					}
				}
			})
		});

		return commonProperties as IElement;
	}

	/**
	 * Restore the property form to the state it was before element selection
	 */
	// TODO delete eventually
	private restorePropertyForm() {
		// This was needed when the properties of the selection were shown in the sidebar box.
		// Now that the properties are shown in the popup, this is not needed because
		// the sidebar box doesn't change anymore.
		// if (this.$propertyFormUnselected != null) {
		//	$("#sidebar form.elementProperties").replaceWith(this.$propertyFormUnselected);
		//	this.$propertyFormUnselected = null;
		//}
	}

	/**
	 * Saves the state of the property form
	 */
	// TODO delete eventually
	private savePropertyForm() {
		// This was needed when the properties of the selection were shown in the sidebar box.
		// Now that the properties are shown in the popup, this is not needed because
		// the sidebar box doesn't change anymore.
		// if (this.$propertyFormUnselected == null) {
		// 	this.$propertyFormUnselected = $("#sidebar form.elementProperties").clone(true);
		// }
	}

	// ChatGPT suggested "using an arrow function syntax, which is important for preserving this context of the class instance."
	resetSelection = (e):void => {
		const overForm: boolean = $(e.target).closest("form.elementProperties").length > 0;
		if (!overForm) {
			// Clicking outside the form resets the selection
			setSelected([]);
			// eslint-disable-next-line no-invalid-this
			// this.restorePropertyForm();
			// eslint-disable-next-line no-invalid-this
			$("#sidebar").off("mousedown", this.resetSelection);
		}
	}
	
	private openSaveModal(openBomPage: boolean = false): void {
		$("#saveConfigurationForm input[name=saveName]").val(this.productConfigurationName);
		$("#saveModal").modal("show");
		$("#saveModal").data("openBomPage", openBomPage);
	}

	//
	// Save
	//
	private initializeSaveHandling(): void {
		MessageHub.subscribe(Events.save, () => {
			this.openSaveModal();
		});
		// $("#saveConfigurationForm").on("submit", e => {
		$(document).on("submit", "#saveConfigurationForm", e => {
			e.preventDefault();
			const action = $(e.target).attr("action");
			this.productConfigurationName = $("#saveConfigurationForm input[name=saveName]").val() as string;
			const openBomPage = $("#saveModal").data("openBomPage") == true;
			this.saveToServer(this.productConfigurationName, action, openBomPage);
			return false;
		});
	}

	// This Factory captures the value of saveName to prevent overwriting by a sequence of saves, and the productConfigurationId
	private postSaveHandlerFactory(saveName: string, openBomPage: boolean) {
		return (responseText, responseHtml) => {
			const result = yada.getEmbeddedResult(responseHtml);
			this.productConfigurationId = result["productConfigurationId"];
			this.productConfigurationName = saveName;
			this.engine.setUnsaved(false);
			if (openBomPage) {
				$("#saveModal").data("openBomPage", false); // Just in case
				this.showStepBom();
			}
		};
	}
	
	/**
	 * Sends the configuration to the server for storage.
	 * @param saveName 
	 * @param serverUrl
	 * @param openFinalPage is true when a successful save must open the final page.
	 * This is used to force a save before opening the BOM page.
	 */
	public saveToServer(saveName: string, serverUrl: string, openBomPage: boolean): void {
		this.globalValues.gateway = this.gatewayChecked;
		const scene = this.engine.save();
		const data = {
			"saveName": saveName,
			"scene": JSON.stringify(scene),
			"jsonGlobalValues": JSON.stringify(this.globalValues),
			// "installation": 0, // No installation needed
			// "configuratorId": 0, // No configurator id needed
			"productConfigurationId": this.productConfigurationId
		};
		yada.ajax(serverUrl, data, this.postSaveHandlerFactory(saveName, openBomPage), "POST");
	}

   /**
	 * Loads a full configuration with data coming from the server
	 */
	public loadScene(jsonGlobalValues: string, sceneJson: string): void {        
		if (jsonGlobalValues!=null && sceneJson!=null) {
			this.globalValues = JSON.parse(jsonGlobalValues);
			this.engine.load(JSON.parse(sceneJson));
			this.engine.setUnsaved(false);
			this.gatewayChecked = this.globalValues.gateway;
			// Update the fixing type
			const fixingTypeChosen = this.globalValues.fixingType;
			this.globalValues.fixingType = ''; // To trigger the change event
			const $inputChosen = $("#fixingTypeContainer input[value='" + fixingTypeChosen + "']");
			$inputChosen.closest('.option').trigger('click');
		}
	}
	
	private isNotDeveloment(): boolean {
		return !this.serverConfig.developmentEnvironment;
	}


}
