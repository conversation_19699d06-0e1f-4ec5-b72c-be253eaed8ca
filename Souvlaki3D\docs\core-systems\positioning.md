# Positioning System

## Overview

The Positioning System is responsible for managing the spatial relationships between movable items in the 3D space. It handles:
- Snap point calculation and validation
- Alignment and connection logic
- Distance constraints
- Wall attachments
- Collision detection

## Architecture

```mermaid
graph TD
    A[Positioning System] --> B[Snap System]
    A --> C[Alignment]
    A --> D[Constraints]
    A --> E[Collision]
    
    B --> B1[Snap Points]
    B --> B2[Connection Points]
    B --> B3[Validation]
    
    C --> C1[Wall Alignment]
    C --> C2[Ceiling Alignment]
    C --> C3[Grid Alignment]
    
    D --> D1[Distance]
    D --> D2[Angle]
    D --> D3[Power]
    
    E --> E1[Intersection]
    E --> E2[Proximity]
    E --> E3[Bounds]
```

## Core Components

### PositioningUtils
The main utility class for positioning operations:

```typescript
class PositioningUtils {
    // Snap point calculations
    public static getSnapOptions(
        design: DesignState,
        draggedGroup: MovableItem[]
    ): {
        free: ISnapPoint[];
        taken: ISnapPoint[];
        forbidden: ISnapPoint[];
    }
    
    // Rotation utilities
    public static getRotatedSize(item: MovableItem): {
        width: number;
        height: number;
        depth: number;
    }
    
    // Distance calculations
    public static distanceToWall(design: DesignState, item: MovableItem): number
}
```

## Snap System

### Snap Points
```typescript
interface ISnapPoint {
    x: number;
    y: number;
    z: number;
    side: IPoint3D;  // Direction vector
    movable?: MovableItem;  // Associated item
}

// Snap point calculation
function getMovablePoints(
    movable: MovableItem,
    snaps: ISnapPoint[],
    reference: IPoint3D,
    includeRotation: boolean = false
): ISnapPoint[] {
    return snaps.map(snap => ({
        x: reference.x + snap.x,
        y: reference.y + snap.y,
        z: reference.z + snap.z,
        side: includeRotation
            ? PolygonUtils.rotatePoint(snap.side, movable)
            : snap.side,
        movable
    }));
}
```

### Connection Points
```typescript
interface IMovableSnap {
    movable: MovableItem;
    absoluteSnaps: ISnapPoint[];
}

// Get connected items
function getConnectedGroups(movables: MovableItem[]): IMovableSnap[][] {
    const groups: IMovableSnap[][] = [];
    const processed = new Set<string>();
    
    for (const m of movables) {
        if (!processed.has(m.id)) {
            const group = findConnectedGroup(m, movables);
            groups.push(group);
            group.forEach(x => processed.add(x.movable.id));
        }
    }
    
    return groups;
}
```

## Alignment System

### Wall Alignment
```typescript
function alignToWall(
    item: MovableItem,
    wall: IWall,
    offset: number = 0
): void {
    const wallVector = new Vector3(
        wall.end.x - wall.start.x,
        0,
        wall.end.z - wall.start.z
    ).normalize();
    
    const itemPosition = new Vector3(item.x, item.y, item.z);
    const projectedPosition = projectOntoWall(itemPosition, wall);
    
    // Apply offset
    projectedPosition.add(wallVector.multiplyScalar(offset));
    
    item.x = projectedPosition.x;
    item.z = projectedPosition.z;
}
```

### Ceiling Alignment
```typescript
function alignToCeiling(
    item: MovableItem,
    ceiling: number,
    offset: number = 0
): void {
    const size = PositioningUtils.getRotatedSize(item);
    item.y = ceiling - size.height / 2 - offset;
}
```

## Constraint System

### Distance Constraints
```typescript
function checkDistanceConstraints(
    item: MovableItem,
    constraints: IDistanceConstraint[]
): boolean {
    for (const constraint of constraints) {
        const distance = PolygonUtils.pointsDistance(
            item,
            constraint.target
        );
        
        if (distance < constraint.minimum ||
            distance > constraint.maximum) {
            return false;
        }
    }
    
    return true;
}
```

### Angle Constraints
```typescript
function checkAngleConstraints(
    item: MovableItem,
    constraints: IAngleConstraint[]
): boolean {
    for (const constraint of constraints) {
        const angle = calculateAngle(item, constraint.reference);
        
        if (!isValidAngle(angle, constraint.allowedAngles)) {
            return false;
        }
    }
    
    return true;
}
```

## Collision Detection

### Intersection Testing
```typescript
function checkIntersection(
    item: MovableItem,
    others: MovableItem[]
): boolean {
    const itemBounds = getBoundingBox(item);
    
    for (const other of others) {
        if (item === other) continue;
        
        const otherBounds = getBoundingBox(other);
        if (intersectBoxes(itemBounds, otherBounds)) {
            return true;
        }
    }
    
    return false;
}
```

### Proximity Checking
```typescript
function checkProximity(
    item: MovableItem,
    others: MovableItem[],
    minDistance: number
): MovableItem[] {
    return others.filter(other => {
        if (item === other) return false;
        
        const distance = PolygonUtils.pointsDistance(item, other);
        return distance < minDistance;
    });
}
```

## Utility Functions

### Rotation Utilities
```typescript
function cleanRotation(angle: number): number {
    angle = angle % 360;
    return angle < 0 ? angle + 360 : angle;
}

function isUpDownAngle(item: MovableItem): boolean {
    const rotY = cleanRotation(item.rotationY);
    return rotY === 90 || rotY === 270;
}
```

### Distance Utilities
```typescript
function distanceToWall(design: DesignState, item: MovableItem): number {
    const walls = design.walls;
    let minDistance = Infinity;
    
    for (let i = 0; i < walls.length; i++) {
        const wall = walls[i];
        const distance = pointToLineDistance(
            item.x,
            item.z,
            wall.start.x,
            wall.start.z,
            wall.end.x,
            wall.end.z
        );
        
        minDistance = Math.min(minDistance, distance);
    }
    
    return minDistance;
}
```

## Best Practices

1. **Snap Point Management**
   - Cache snap points when possible
   - Validate connections thoroughly
   - Handle edge cases

2. **Collision Detection**
   - Use broad-phase detection first
   - Implement spatial partitioning
   - Optimize for common cases

3. **Constraint Validation**
   - Check constraints early
   - Provide clear feedback
   - Handle multiple constraints

4. **Performance**
   - Cache calculations
   - Use efficient algorithms
   - Minimize updates

## Common Operations

### 1. Item Placement
```typescript
function placeItem(
    item: MovableItem,
    target: ISnapPoint,
    design: DesignState
): boolean {
    // Check constraints
    if (!checkConstraints(item, design)) {
        return false;
    }
    
    // Align to target
    alignToSnap(item, target);
    
    // Validate placement
    if (!validatePlacement(item, design)) {
        return false;
    }
    
    // Update state
    design.movableItems.push(item);
    return true;
}
```

### 2. Group Movement
```typescript
function moveGroup(
    group: MovableItem[],
    delta: IPoint3D,
    design: DesignState
): boolean {
    // Check group constraints
    if (!checkGroupConstraints(group, design)) {
        return false;
    }
    
    // Move items
    group.forEach(item => {
        item.x += delta.x;
        item.y += delta.y;
        item.z += delta.z;
    });
    
    // Validate new positions
    if (!validateGroupPlacement(group, design)) {
        // Revert changes
        group.forEach(item => {
            item.x -= delta.x;
            item.y -= delta.y;
            item.z -= delta.z;
        });
        return false;
    }
    
    return true;
}
```

## Future Considerations

1. **Advanced Constraints**
   - Load-bearing calculations
   - Power distribution
   - Complex geometries

2. **Optimization**
   - Spatial indexing
   - Parallel processing
   - Predictive positioning

3. **Enhanced Features**
   - Magnetic fields
   - Dynamic constraints
   - Auto-arrangement
