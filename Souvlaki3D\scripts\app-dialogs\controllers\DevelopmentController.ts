import GUI from "lil-gui";
import { CanvasUtils } from "../../app-3d/utils/CanvasUtils";
import { Scene } from "../../models/scene/Scene";
import { StateService } from "../../state/StateService";
import { environmentMaps } from "../../models/state/EnvironmentOptions";
import { logDataToConsole } from "../../utils/logDataToConsole";
import { DxfExportUtils } from "../../logic/helpers/DxfExportUtils";
import { DebugUtils } from "../../app-3d/utils/DebugUtils";
import { getRelativeConnections } from "../../logic/helpers/movables/getRelativeConnections";
import { BuildingLogic } from "../../logic/BuildingLogic";
import { settings } from "../../configurations/setup_global";

export class DevelopmentController {

    public static $inject = ["StateService", "scene", "BuildingLogic"];

    constructor(private state: StateService, private scene: Scene, private buildingLogic: BuildingLogic) {
        const gui = new GUI().hide();
        gui.add(state.view, "environment", Object.keys(environmentMaps)).name("Environment map").onChange(value => {
            scene.control.object3D.environment = CanvasUtils.getEnvironment(scene.renderer, value);
        });

        // Shortcut to show/hide gui
        document.addEventListener("keydown", e => {
            if (e.key === "`" || e.key === "~") {
                gui._hidden ? gui.show() : gui.hide();
            }

            if (e.ctrlKey || e.metaKey) {
                switch (e.code) {
                    case "KeyI":
                        logDataToConsole(DxfExportUtils.getDxfExportData(this.state.design, this.state.view.labels, settings));
                        break;
                    case "KeyM":
                        this.logDesign();
                        break;
                    case "KeyB":
                        if (settings.app.debug) {
                            DebugUtils.highlightMovables(this.scene.control.movableControls);
                        }
                        break;
                    case "KeyQ":
                        if (settings.app.debug) {
                            this.logConnections();
                        }
                        break;
                    default:
                        console.log(e.code);
                        break;
                }
            }
        });
    }

    private logConnections(): void {
        const con = getRelativeConnections(this.state.design);
        const l = con.map(c => ({
            movable: c.movable.type.replace("AoL_Suspension_", "").replace("AoL_Ceiling_", ""),
            connected: c.connected.map(x => `${x.item.type.replace("AoL_Suspension_", "").replace("AoL_Ceiling_", "")}:${x.direction}`).join()
        }));
        console.table(l);
    }

    private logDesign(): void {
        const saveData = this.buildingLogic.getSaveData(this.state.design);
        localStorage.design = logDataToConsole(saveData);

        if (this.state.view.labels?.length) {
            localStorage.labels = logDataToConsole(this.state.view.labels);
        }
    }
}