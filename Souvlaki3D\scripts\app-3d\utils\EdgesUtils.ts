import { LineBasicMaterial, Object3D, EdgesGeometry, Mesh, LineSegments, BufferGeometry } from "three";

export class EdgesUtils {

    private static materials: { [key: string]: LineBasicMaterial } = {};

    public static drawEdges(mesh: Object3D, color: string, thresholdAngle: number): void {
        const material = this.material(color);
        mesh.traverse(o => {
            if (o instanceof Mesh) {
                const geo = new EdgesGeometry(o.geometry, thresholdAngle);
                const edges = new LineSegments(geo, material);

                edges.position.copy(o.position);
                edges.scale.copy(o.scale);
                edges.rotation.copy(o.rotation);
                edges.name = `edge_${o.name}`;

                o.parent.add(edges);
            }
        });
    }

    public static getEdges(geo: BufferGeometry, color: string): LineSegments {
        return new LineSegments(
            new EdgesGeometry(geo, 1),
            this.material(color)
        );
    }

    private static material(color: string): LineBasicMaterial {
        if (!this.materials[color]) {
            this.materials[color] = new LineBasicMaterial({color: color, linewidth: 1});
        }

        return this.materials[color];
    }
}