import { IApplicationSettings } from "../../../models/infrastructure/IApplicationSettings";
import { IApplicationState } from "../../../models/state/IApplicationState";
import { MovableItem } from "../../../models/state/MovableItem";
import { PowerLogic } from "../../../logic/PowerLogic";
import { CanvasControl } from "../CanvasControl";
import { Scene } from "../../../models/scene/Scene";
import { AvailableActions, ElementShape } from "../../../models/common/Enums";
import { TaDotControl } from "./TaDotControl";
import { PositioningUtils } from "../../../logic/helpers/PositioningUtils";
import { getChildrenWithinDistanceToPoint } from "../../../logic/helpers/movables/getChildrenWithinDistanceToPoint";
import { getLedStripsPassingWithinDistanceToPoint } from "../../../logic/helpers/movables/getLedStripsPassingWithinDistanceToPoint";

export class TaPowerControl extends CanvasControl {

    public static $inject = ["settings", "PowerLogic", "scene"]
    private attachmentCollisionDistance = 120;

    constructor(private settings: IApplicationSettings, private powerLogic: PowerLogic, private scene: Scene) {
        super();
    }

    private movables: MovableItem[] = [];

    public update(s: IApplicationState): void {
        if (!s.view.isActionAvailable(AvailableActions.Power)) {
            if (this.children.length > 0) {
                this.movables = [];
                this.clearChildren();
            }

            return;
        }

        this.object3D.position.x = -s.view.offset.x;
        this.object3D.position.z = -s.view.offset.y;

        const movables = s.design.movableItems.slice();
        if (this.changed(movables)) {
            this.clearChildren();
            this.movables = movables;
            this.setControls(movables);
        }

        super.update(s);
    }

    private setControls(movables: MovableItem[]): void {
        const snaps = PositioningUtils.getMovableSnaps(movables.filter(x => x.shape === ElementShape.Linear));

        for (const sm of snaps) {
            sm.absoluteSnaps.forEach(snapPoint => {
                const index = sm.absoluteSnaps.indexOf(snapPoint); // Index in the original array
                const colliding = [
                    ...getChildrenWithinDistanceToPoint(sm.movable, movables, snapPoint, this.attachmentCollisionDistance),
                    ...getLedStripsPassingWithinDistanceToPoint(movables, snapPoint, this.attachmentCollisionDistance),
                ];
                const dot = new TaDotControl(sm.movable, index, colliding, this.settings, this.scene);
                this.addChild(dot);
            });
        }
    }

    private changed(movables: MovableItem[]): boolean {
        if (this.movables.length !== movables.length) {
            return true;
        }

        for (let i = 0; i < movables.length; i++) {
            const m = movables[i];
            const c = this.movables[i];
            if (m !== c) {
                return true;
            }
        }

        return false;
    }
}