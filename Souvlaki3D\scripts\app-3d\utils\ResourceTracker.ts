import type {
    ShaderMaterial,
    Mesh,
    Object3D,
    Texture,
    Material,
    Uniform,
  } from "three";
  
  export type DisposeType = {
    dispose: () => void;
  };
  
  export type Disposable =
    | Object3D
    | Mesh
    | Texture
    | Material
    | Material[]
    | Object3D[]
    | DisposeType;
  
  /**
   * Tracks the scene resources such as geometry and textures and disposes 
   * the memory associated with each resource to prevent leaks
   */
  export class ResourceTracker {
    protected resources: Set<Disposable>;
  
    constructor() {
      this.resources = new Set();
    }
  
    /**
     * Tracks a resource or array of resources and their children
     * @param resource the resource to keep track of, can be an array
     * @returns returns the tracked resource
     */
    track<T extends Disposable>(resource: T): T {
      if (!resource) {
        return resource;
      }
  
      // handle children and when material is an array of materials or
      // uniform is array of textures
      if (Array.isArray(resource)) {
        resource.forEach((resource) => this.track(resource));
        return resource;
      }
  
      if (
        (resource as DisposeType)?.dispose ||
        (resource as Object3D)?.removeFromParent
      ) {
        this.resources.add(resource);
      }
  
      if ((resource as Mesh).isMesh) {
        this.track((resource as Mesh).geometry);
        this.track((resource as Mesh).material);
      } else if ((resource as Material).isMaterial) {
        // We have to check if there are any textures on the material
        for (const value of Object.values(resource)) {
          if (value && (value as Texture).isTexture) {
            this.track(value);
          }
        }
  
        // We also have to check if any uniforms reference textures or arrays of textures
        if ((resource as ShaderMaterial).uniforms) {
          for (const value of Object.values(
            (resource as ShaderMaterial)?.uniforms
          )) {
            if (!value) continue;
  
            const uniformValue = (value as Uniform).value;
            if (uniformValue.isTexture || Array.isArray(uniformValue)) {
              this.track(uniformValue);
            }
          }
        }
      }
  
      if ((resource as Object3D).isObject3D) {
        this.track((resource as Object3D).children);
      }
  
      return resource;
    }
  
    untrack(resource: Disposable) {
      //TODO: add recursive option to prevent deletion of sub-resources if already tracked
      this.resources.delete(resource);
    }
  
    /**
     * Disposes all resources that the instance is currently tracking
     */
    dispose() {
      this.resources.forEach((resource: Disposable) => {
        if ((resource as Object3D).removeFromParent) {
          (resource as Object3D).removeFromParent();
        }
  
        if ((resource as DisposeType).dispose) {
          (resource as DisposeType).dispose();
        }
      });
  
      this.resources.clear();
    }
  }
  