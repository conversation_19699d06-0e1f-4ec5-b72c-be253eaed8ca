import { BaseActionController } from "./BaseActionController";
import { StateService } from "../../../state/StateService";
import { MovablesLogic } from "../../../logic/MovablesLogic";
import { IToolbarActionOptions } from "./IToolbarActionOptions";
import { Direction } from "../../../models/common/Enums";
import { CollisionUtils } from "../../../logic/helpers/CollisionUtils";
import { settings } from "../../../configurations/setup_global";
import { pushState } from "../../../state/actions/pushState";
import { unique } from "../../../utils/unique";
import { Modal } from "../../services/Modal";

export class FlipController extends BaseActionController {

    public static $inject = ["StateService", "MovablesLogic"];

    constructor(private state: StateService, private movablesLogic: MovablesLogic, options: IToolbarActionOptions) {
        super(options);
        this.defaultInitialize();
    }

    public execute(): void {
        const clone = this.state.design.clone();
        const movables = clone.movableItems.filter(x => x.selected);
        for (const movable of movables) {
            const previousHeight = movable.currentAttachmentPoints[0].y;
            movable.attachDirection = movable.attachDirection === Direction.Top ? undefined : Direction.Top;
            const currentAttachmentHeight = movable.currentAttachmentPoints[0].y;
            movable.y += previousHeight - currentAttachmentHeight;
        }

        const outside = CollisionUtils.outside(clone);
        if (outside.length) {
            Modal.showInfoModal(null, settings.messages.cannotFlip);

        } else {
            pushState(clone);
        }
    }

    public update(): void {
        const selected = (this.state.design?.movableItems.filter(x => x.selected) || []);
        const enabled = selected.length && selected.every(x => x.attachmentPoints?.length > 2 && unique(x.attachmentPoints.map(x => x.attachDirection)).length > 1);
        this.element.classList.toggle("disabled", !enabled);
    }
}