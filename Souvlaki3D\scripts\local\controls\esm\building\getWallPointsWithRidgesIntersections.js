import { Line2D } from "@immugio/three-math-extensions";
export function getWallPointsWithRidgesIntersections(wallPoints, ridgesProjectedOnPlan) {
    const wallLines = Line2D.fromPolygon(wallPoints);
    return wallLines.map(line => {
        const intersections = ridgesProjectedOnPlan.map(ridge => {
            const intersection = ridge.intersect(line);
            return intersection && line.isPointCloseToAndBesideLineSection(intersection, 1) ? intersection : null;
        }).filter(x => x !== null);
        const intersectionsSortedByDistanceToP1 = intersections.sort((a, b) => line.start.distanceTo(a) - line.start.distanceTo(b));
        return [line.start, ...intersectionsSortedByDistanceToP1, line.end];
    });
}
