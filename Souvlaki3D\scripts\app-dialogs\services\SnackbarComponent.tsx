import { IApplicationState } from "../../models/state/IApplicationState";
import { BaseController } from "../controllers/BaseController";
import { dom } from "@immugio/jsx-and-html-utils";

export class SnackbarComponent extends BaseController {

    private snackbar: string = undefined;
    private readonly element: HTMLElement;

    constructor() {
        super();
        this.element = <div className="snackbar"></div>;
        document.body.appendChild(this.element);
    }

    public update(s: IApplicationState): void {
        if ((s.view.snackbar && !this.snackbar) || (!s.view.snackbar && this.snackbar)) {
            this.snackbar = s.view.snackbar;
            this.element.innerHTML = s.view.snackbar;
            this.element.classList.toggle("show", !!this.snackbar);
        }
    }
}