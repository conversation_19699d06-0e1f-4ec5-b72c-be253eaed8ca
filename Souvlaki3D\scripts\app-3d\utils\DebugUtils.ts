import { IApplicationState } from "../../models/state/IApplicationState";
import { IPoint3D } from "../../models/geometry/IPoint3D";
import { Scene } from "../../models/scene/Scene";
import { DesignState } from "../../models/state/DesignState";
import { CanvasControl } from "../controls/CanvasControl";
import { Box } from "../../logic/helpers/Box";
import { Box3, Box3Helper, BoxGeometry, BufferAttribute, Color, Intersection, Mesh, MeshBasicMaterial, Object3D, Triangle, Vector3 } from "three";
import { CanvasUtils } from "./CanvasUtils";
import { getParentWithNameStarting, getParentWithUserData } from "./MeshUtils";
import { toRad } from "../../logic/helpers/PositioningUtils";
import { axes } from "../../logic/helpers/geometry/Axes";
import { Vec3 } from "@immugio/three-math-extensions";

export class DebugUtils {

    private static scene: Scene;
    private static state: IApplicationState;
    private static objects: Object3D[];

    public static initialize(scene: Scene, state: IApplicationState): void {
        this.scene = scene;
        this.state = state;
        this.objects = [];

        window.DebugUtils = this;
    }

    public static addPoint(p: IPoint3D, color = "red", size = 60, mark: Mesh = null): Mesh {
        const abs = { x: p.x - this.state.view.offset.x, y: p.y, z: p.z - this.state.view.offset.y };

        return this.addPointAbsolute(abs, color, size, mark);
    }

    public static addPointAbsolute(p: IPoint3D, color = "red", size = 60, mark: Mesh = null): Mesh {
        if (!mark) {
            mark = new Mesh(
                new BoxGeometry(size, size, size),
                new MeshBasicMaterial({ color: color })
            );

            this.objects.push(mark);
            this.scene.control.object3D.add(mark);
        }

        mark.position.set(p.x, p.y, p.z);

        return mark;
    }

    public static addBox(box: Box, color = "green", transparent = false): Mesh {
        const mesh = new Mesh(
            new BoxGeometry(box.width, box.height, box.depth),
            new MeshBasicMaterial({ transparent: transparent, color: color, opacity: 0.3 })
        );

        mesh.position.set(box.position.x - this.state.view.offset.x, box.position.y, box.position.z - this.state.view.offset.y);

        this.objects.push(mesh);
        this.scene.control.object3D.add(mesh);

        return mesh;
    }

    public static addBox3(box: Box3, color: Color = new Color(0xffff00)): Box3Helper {
        const helper = new Box3Helper(box, color);
        this.objects.push(helper);
        this.scene.control.object3D.add(helper);

        return helper;
    }

    public static highlightDrawing(d: DesignState): void {
        this.clear();

        const room = Box.fromRoom(d);
        this.addBox(room, "red", true);

        for (const item of d.movableItems) {
            const box = Box.fromMovable(item);
            this.addBox(box);
        }
    }

    public static highlightMovables(movs: CanvasControl[]): void {
        this.clear();

        for (const item of movs) {
            const box = new Box3().setFromObject(item.object3D);
            const helper = new Box3Helper(box, new Color(0xffff00));
            this.objects.push(helper);
            this.scene.control.object3D.add(helper);
        }
    }

    public static clear(): void {
        for (let i = this.objects.length - 1; i >= 0; i--) {
            const o = this.objects.pop();
            o.parent.remove(o);
        }
    }

    public static showbox(box: Box3): void {
        this.clear();
        const helper = new Box3Helper(box, new Color(0xffff00));
        this.objects.push(helper);
        this.scene.control.object3D.add(helper);
    }

    public static showFaceInfo(e: HammerInput): void {
        if (!this.state.view.ctrl) {
            return;
        }

        const intersections = CanvasUtils.intersectObjects(this.scene, e.center.x, e.center.y);
        const withMesh = intersections.filter(x => x.object instanceof Mesh) as Intersection<Mesh>[];
        withMesh.sort((a, b) => a.distance - b.distance);
        const movableWithParent = withMesh.map(intersection => {
            const parent = getParentWithNameStarting(intersection.object, "Movable_");
            return { parent, intersection };
        }).filter(x => x.parent)[0];

        if (movableWithParent) {
            this.logFaceInfo(movableWithParent.intersection, movableWithParent.parent);
        }
    }

    private static logFaceInfo(intersection: Intersection<Mesh>, parent: Object3D): void {
        const face = intersection.face;
        const position = intersection.object.geometry.attributes["position"] as BufferAttribute;
        const scale = getParentWithUserData<Vector3>(intersection.object, "scale");
        if (scale) {
            const a = new Vec3().fromBufferAttribute(position, face.a).multiply(scale);
            const b = new Vec3().fromBufferAttribute(position, face.b).multiply(scale);
            const c = new Vec3().fromBufferAttribute(position, face.c).multiply(scale);
            const centerAB = Vec3.fromPoint(a).moveHalfWayTowards(b);
            const centerBC = Vec3.fromPoint(b).moveHalfWayTowards(c);
            const centerCA = Vec3.fromPoint(c).moveHalfWayTowards(a);
            const triangle = new Triangle(a, b, c);
            const normal = triangle.getNormal(new Vector3());
            ["x", "y", "z"].forEach(key => {
                if (Math.abs(normal[key]) < 0.00001) {
                    normal[key] = 0;
                }
            });
            console.table({ name: parent.name, a, b, c, normal, centerAB, centerBC, centerCA, scale });
        }
    }

    public static getCalculatedVector(angle: number): void {
        const vec = Vec3.fromPoint({ x: -1, y: 0, z: 0 });
        vec.applyAxisAngle(axes.y, toRad(angle));
        vec.normalize();
        console.log(vec);
    }
}