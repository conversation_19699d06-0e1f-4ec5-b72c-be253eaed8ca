import { getUvMap } from "./getUvMap";
import { GeometryPart } from "./GeometryBuilder";
import { project3DPointsOnVerticalPlane } from "./project3DPointsOnVerticalPlane";
import { triangulate } from "./triangulate";
export function getVerticalFaces(top, bottom, materials, offsets) {
    const parts = [];
    for (let i = 0; i < top.length - 1; i++) {
        const contour = [
            bottom[i],
            bottom[i + 1],
            top[i + 1],
            top[i]
        ];
        parts.push(getVerticalFace(contour, [], offsets?.[i], materials[i].textureSize, i, true));
    }
    return parts;
}
export function getVerticalFace(contour, cutouts, offset, textureSize, materialIndex, reverseFaces) {
    const position = [
        ...contour.map(point => [point.x, point.y, point.z]).flat(),
        ...cutouts.flatMap(cutout => cutout.map(point => [point.x, point.y, point.z]).flat())
    ];
    const { contour: outer, holes } = project3DPointsOnVerticalPlane(contour, cutouts);
    const triangulatedShape = triangulate(outer, holes);
    const faces = triangulatedShape.faces.map(face => reverseFaces ? face.reverse() : face).flat();
    return new GeometryPart(position, faces, getUvMap(textureSize, triangulatedShape.points, offset), materialIndex);
}
