# Core Components

## Overview

The Souvlaki 3D Configuration Engine is built on a modular architecture with several key components that work together to provide a complete 3D configuration experience. This document details these components and their interactions.

## Component Architecture

```mermaid
graph TD
    A[Core] --> B[Scene]
    A --> C[State Service]
    A --> D[Message Hub]
    A --> E[Logic Components]
    
    B --> B1[Three.js Renderer]
    B --> B2[Camera Controls]
    B --> B3[Scene Controls]
    
    E --> E1[Movables Logic]
    E --> E2[Building Logic]
    E --> E3[Dimensions Logic]
    E --> E4[Zones Logic]
    E --> E5[Labels Logic]
```

## Core Component

The Core component (`Core.ts`) serves as the central orchestrator:

```typescript
class Core {
    constructor(
        private movablesLogic: MovablesLogic,
        private buildingLogic: BuildingLogic,
        private dimensionsLogic: DimensionsLogic,
        private zonesLogic: ZonesLogic,
        private labelsLogic: LabelsLogic,
        private alerts: AlertService,
        private state: StateService,
        private controllers: IController[],
        private messageHub: MessageHub,
        private scene: Scene
    )
```

### Responsibilities
- System initialization
- Component coordination
- Event handling
- Render loop management
- User interaction processing

## Logic Components

### 1. MovablesLogic
Handles all movable items in the 3D space:

```typescript
class MovablesLogic {
    // Core operations
    public insert(d: DesignState, dragged: MovableItem, insertPoints: ISnapPoint[]): void
    public swap(d: DesignState, current: MovableItem, replacement: MovableItem): MovableItem
    public rotate(d: DesignState, movables: MovableItem[], clockwise: boolean): void
    
    // Positioning
    public attachToSnap(m: MovableItem, snap: ISnapPoint): void
    public createPositionGroup(d: DesignState, movSnaps: IMovableSnap[]): IPositionGroup
    
    // Validation
    public canReplace(d: DesignState, m: MovableItem): MovableItem
    public canInsert(inserts: ISnapPoint[], m: MovableItem): ISnapPoint[]
}
```

### 2. BuildingLogic
Manages the building structure and constraints:

- Wall management
- Room dimensions
- Building constraints
- Elevation handling

### 3. DimensionsLogic
Handles dimensional aspects:

- Measurement calculations
- Size constraints
- Distance validation
- Scale management

### 4. ZonesLogic
Manages spatial zones and areas:

- Zone definition
- Area calculations
- Space management
- Placement validation

### 5. LabelsLogic
Handles labeling and annotations:

- Label positioning
- Text management
- Visual indicators
- Measurement displays

## Scene Management

### Scene Component
Manages the 3D visualization:

```typescript
class Scene {
    public renderer: THREE.WebGLRenderer
    public camera: THREE.Camera
    public control: SceneControl
    public center: THREE.Vector3
    
    // View modes
    public get inPerspectiveMode(): boolean
    public get inOrthoMode(): boolean
}
```

### Scene Controls
Handles user interaction with the 3D scene:

```typescript
class SceneControl {
    // Control groups
    public movableControls: MovableItemControl[]
    public buildingControl: BuildingControl
    public dimensionsControl: DimensionsControl
    
    // Updates
    public update(state: IApplicationState): void
    public updateCamera(): void
}
```

## UI Components

### 1. Controls
```typescript
interface IControl {
    interaction: IInteractionOptions
    mouseOver(): void
    mouseOut(): void
    update(state: IApplicationState): void
}
```

### 2. Dialogs
```typescript
class Modal {
    public static showErrorModal(title: string, message: string): void
    public static showWarningModal(title: string, message: string): void
    public static showConfirmModal(title: string, message: string): Promise<boolean>
}
```

### 3. Controllers
```typescript
interface IController {
    update?(state: IApplicationState): void
    destroy?(): void
}
```

## Component Communication

### 1. Direct Communication
Components can communicate directly through method calls:

```typescript
// Core initiating movable insertion
this.movablesLogic.insert(this.state.design, dragged, insertPoints);

// Core updating scene
this.scene.control.update(this.state);
```

### 2. Event-Based Communication
Components communicate through the MessageHub:

```typescript
// Broadcasting events
this.messageHub.broadcast<DesignState>(Events.setState, design);
this.messageHub.broadcast(Events.updateViewMode);

// Subscribing to events
this.messageHub.subscribe(Events.zoomIn, () => this.zoom(1));
this.messageHub.subscribe<DesignState>(Events.setState, (d) => this.setState(d));
```

### 3. State-Based Communication
Components react to state changes:

```typescript
// State updates
this.state.compare();
if (this.state.diff.someProperty) {
    // Handle property change
}
```

## Component Lifecycle

### 1. Initialization
```typescript
constructor() {
    // Initialize components
    this.initEventsHandlers();
    
    // Set initial state
    const design = MovablesUtils.getDesignStateFromTemplate(settings.buildingTemplates[0]);
    messageHub.broadcast<DesignState>(Events.setState, design);
    
    // Start render loop
    this.run();
}
```

### 2. Update Cycle
```typescript
private run(): void {
    while (this.state.applyUpdate()) {
        this.update();
    }
    
    this.update();
    requestAnimationFrame(() => this.run());
}
```

### 3. Cleanup
```typescript
destroy(): void {
    // Cleanup resources
    this.scene.dispose();
    this.controllers.forEach(c => c.destroy?.());
}
```

## Component Integration

### 1. Scene Integration
```typescript
private updateViewMode(): void {
    if (this.state.view.displayMode & Display.PerspectiveView) {
        CanvasUtils.toPerspective(this.scene);
    } else {
        CanvasUtils.toOrtho(this.scene);
    }
    
    this.state.design.movableItems.forEach(mi => mi.refresh = true);
    this.state.view.controlOptions = ResizingLogic.getResizeOptions(
        this.state.design,
        this.state.view
    );
}
```

### 2. Logic Integration
```typescript
private tryInsert(): void {
    const dragged = this.state.view.activeItem.movable;
    const insertPoints = this.movablesLogic.canInsert(
        this.state.view.inserts,
        dragged
    );
    
    if (insertPoints) {
        this.movablesLogic.insert(this.state.design, dragged, insertPoints);
    }
}
```

### 3. UI Integration
```typescript
private initEventsHandlers(): void {
    // Handle user input
    this.scene.renderer.domElement.addEventListener(
        "mousemove",
        e => this.onMouseMove(e)
    );
    
    // Handle touch events
    const mc = new Hammer.Manager(dragRoot, {
        recognizers: [
            [Hammer.Press, { enable: true }],
            [Hammer.Pan, { enable: true }]
        ]
    });
    
    mc.on("press", e => this.onTouch(e));
    mc.on("pan", e => this.onDrag(e));
}
```

## Best Practices

1. **Component Design**
   - Single responsibility principle
   - Clear interfaces
   - Minimal dependencies

2. **State Management**
   - Centralized state
   - Immutable updates
   - Clear update patterns

3. **Event Handling**
   - Type-safe events
   - Clear event flow
   - Proper cleanup

4. **Performance**
   - Efficient updates
   - Resource management
   - Render optimization
