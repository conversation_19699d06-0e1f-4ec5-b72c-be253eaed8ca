import { BaseController } from "../BaseController";
import { MessageHub } from "../../../infrastructure/MessageHub";
import { StateService } from "../../../state/StateService";
import { Events } from "../../../models/infrastructure/Events";
import { IInteractionOptions } from "../../../models/positioning/IInteractionOptions";
import { PowerLogic } from "../../../logic/PowerLogic";
import { IPowerOptions } from "../../../models/state/IPowerOptions";
import { IApplicationSettings } from "../../../models/infrastructure/IApplicationSettings";
import { canHaveFeedPoints, isHoyCircuitPowered, isHoyControlTypeSelected } from "../../../logic/helpers/PowerUtils";
import { IApplicationState } from "../../../models/state/IApplicationState";
import { AvailableActions } from "../../../models/common/Enums";
import { PositioningUtils } from "../../../logic/helpers/PositioningUtils";
import { MovableItem } from "../../../models/state/MovableItem";
import { isNear } from "../../../logic/helpers/IsNear";
import { getConnectedGroups } from "../../../logic/helpers/positioning/getConnectedGroups";

export class HoyPowerController extends BaseController {

    public static $inject = ["PowerLogic", "MessageHub", "StateService", "settings"];

    constructor(private powerLogic: PowerLogic, private messageHub: MessageHub, private state: StateService, private settings: IApplicationSettings) {
        super();
        this.initialize();
    }

    public update(s: IApplicationState): void {
        if (s.view.isActionAvailable(AvailableActions.Power)) {
            this.setColors();
        }
     }

    private async initialize(): Promise<void> {
        this.messageHub.subscribe<IInteractionOptions>(Events.optionsSelected, (info) => this.onSupplySelected(info));
        this.messageHub.subscribe(Events.powerSourcesNeedUpdate, () => this.setColors());
    }

    private onSupplySelected(info: IInteractionOptions): void {
        const m = info.movable;
        const powerSource = m.data.powerSource.find(x => x.sideIndex === info.lineIndex); // TODO: How this will work? Will I always assume there will be just a single PW?

        if (powerSource) { // Item is a power source with params set
            m.data.powerSource = m.data.powerSource.filter(x => x.sideIndex !== info.lineIndex);

        } else {
            const pw: IPowerOptions = {
                nominal: null,
                effective: null,
                installation: null,
                color: null,
                typology: null,
                productCode: null,
                powerSupplyIcon: null,
                appControlled: null,
                powerSourceId: null,
                onEnd: this.isLast(this.state.design.movableItems, m, info.lineIndex),
                sideIndex: info.lineIndex,
                connected: [{ id: m.id, partId: null }]
            };
            m.data.powerSource = [...m.data.powerSource, pw];
        }

        m.data = { ...m.data };

        this.powerLogic.updatePowerIds(this.state.design.movableItems);

        this.messageHub.broadcast(Events.powerSourcesNeedUpdate);
    }

    private isLast(movableItems: MovableItem[], m: MovableItem, lineIndex: number): boolean {
        const movableSnaps = PositioningUtils.getMovableSnaps(movableItems);
        const currentSnap = movableSnaps.find(x => x.movable === m).absoluteSnaps[lineIndex];

        let count = 0;

        for (const sn of movableSnaps) {
            for (const s of sn.absoluteSnaps) {
                if (isNear(currentSnap, s)) {
                    count++;
                }
            }
        }

        return count < 2;
    }

    private setColors(): void {
        // Clear all
        for (const cl of this.state.design.movableItems) {
            cl.data.color = null;
        }

        const groups = getConnectedGroups(this.state.design.movableItems).filter(x => canHaveFeedPoints(x));

        // Green colors is set on one of these conditions
        // 1) Group is powered up
        if (groups.some(g => isHoyCircuitPowered(g))) {
            for (const group of groups) {
                const hasPowerSource = isHoyCircuitPowered(group);
                if (hasPowerSource) {
                    for (const items of group) {
                        items.movable.data.color = [this.settings.textures.powerConnected.color];
                    }
                }
            }
        }

        // 2) There are more groups and only some of them has control type selected
        else if (groups.length > 1) {
            const groupWithControlType = groups.filter(group => isHoyControlTypeSelected(group.map(x => x.movable)));
            if (groups.length > groupWithControlType.length) {
                for (const group of groups) {
                    const controlTypeSelected = isHoyControlTypeSelected(group.map(x => x.movable));
                    if (controlTypeSelected) {
                        for (const items of group) {
                            items.movable.data.color = [this.settings.textures.powerConnected.color];
                        }
                    }
                }
            }
        }
    }
}