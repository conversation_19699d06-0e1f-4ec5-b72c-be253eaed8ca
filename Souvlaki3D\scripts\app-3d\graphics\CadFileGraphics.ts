﻿import { IGraphicsData, IApplicationSettings } from "../../models/infrastructure/IApplicationSettings";
import { MovableItem } from "../../models/state/MovableItem";
import { DesignState } from "../../models/state/DesignState";
import { Display } from "../../models/common/Enums";
import { MovableGraphics } from "./MovableGraphics";
import { IGraphicsLoaded } from "./IGraphicsLoaded";
import { ModelLoadUtils } from "../utils/ModelLoadUtils";
import { MaterialUtils } from "../utils/MaterialUtils";
import { EdgesUtils } from "../utils/EdgesUtils";
import { MeshUtils } from "../utils/MeshUtils";
import { Highlight } from "../utils/Highlight";
import { Material, Object3D } from "three";

export class CadFileGraphics extends MovableGraphics {

    public static $inject = ["settings"];

    constructor(private settings: IApplicationSettings) {
        super();
    }

    public createGraphics(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData, callback: IGraphicsLoaded): Object3D {
        if (this.cachedGraphics) {
            this.updateColor(m, this.cachedGraphics, data.currentFinishes);
            return this.cachedGraphics;
        }

        ModelLoadUtils.load(data.finishes[0], m, data.initialTransform).then(result => {
            if (result) {
                MeshUtils.removeHelperPoints(result);
                result.name = "Movable_" + m.type;

                this.cachedGraphics = result;

                this.createEdges(this.cachedGraphics, data);
                this.updateColor(m, this.cachedGraphics, data.currentFinishes);

                if (callback) return callback(this.cachedGraphics);
            } else {
                console.log(`3d model not found, url: ${data && data.finishes && data.finishes[0]}`);
            }
        });

        return undefined;
    }

    protected createEdges(object: Object3D, data: IGraphicsData){
        if (this.settings.building.defaultItemEdgesColor && data.edgeThresholdAngle > -1) {
            EdgesUtils.drawEdges(object, this.settings.building.defaultItemEdgesColor, data.edgeThresholdAngle);
        }
    }

    public createHighlight(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData, material: Material, partId?: string): Object3D {
        if (!this.cachedHighlight && this.cachedGraphics) {
            this.cachedHighlight = Highlight.create(this.cachedGraphics);
        }

        if (this.cachedHighlight) { // Highlight only a specific part when an id is provided
            this.cachedHighlight.traverse(x => {
                x.visible = !!(!partId || !x.name || x.name === partId);
            });
        }

        return this.cachedHighlight;
    }

    private updateColor(m: MovableItem, object3D: Object3D, colors: string[]): void {
        colors = colors || [this.settings.building.defaultItemColor];

        if (colors.length === 1) {
            MaterialUtils.updateMaterial(object3D, "movable", colors[0]);

        } else if (m.parts && m.parts.length === colors.length) {
            for (let i = 0; i < m.parts.length; i++) {
                const partId = m.parts[i];
                const part = object3D.getObjectByName(partId);
                if (part) {
                    const color = colors[i] || this.settings.building.defaultItemColor;
                    MaterialUtils.updateMaterial(part, "movable", color);
                }
            }
        }
    }
}