{
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Debug Webpack Dev Server",
        "request": "launch",
        "runtimeArgs": ["run", "dev-svk"],
	    "runtimeExecutable": "npm",
        "skipFiles": [
          "<node_internals>/**"
        ],
        "type": "node"
      },
    {
      "name": "Open in Chrome",
      "type": "chrome",
      "request": "launch",
       "url": "http://localhost:8081/en/configurator",
    },
      
    {
      "name": "Attach to Chrome",
      "type": "chrome",
      "request": "attach",
      "port": 9222,
      "url": "http://localhost:8081/en/configurator",
      "webRoot": "${workspaceFolder}",
    }
  ]
}