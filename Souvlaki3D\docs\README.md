# Souvlaki 3D Configuration Engine

## Overview

The Souvlaki 3D Configuration Engine is a TypeScript-based application that provides an interactive 3D environment for configuring modular lighting systems. It enables users to design and customize lighting setups by manipulating tracks, connectors, and magnetic accessories in a virtual 3D space.

## Key Features

- **Interactive 3D Environment**: Real-time 3D visualization using Three.js
- **Modular Component System**: 
  - Linear tracks with magnetic mounting
  - Angular connectors for track joining
  - Magnetic accessories (spot lights, etc.)
- **Smart Positioning**:
  - Automatic snapping and alignment
  - Collision detection
  - Intelligent connection points
- **State Management**:
  - Undo/redo functionality
  - Real-time state synchronization
  - Persistent configurations
- **Power Management**:
  - Power source placement
  - Chain management
  - Load calculation

## Technology Stack

- **Core**: TypeScript
- **3D Rendering**: Three.js
- **UI Components**: React
- **Build System**: Webpack
- **Testing**: Jest

## Project Structure

```
Souvlaki3D/
├── scripts/
│   ├── app-3d/          # Core 3D functionality
│   ├── app-dialogs/     # UI components and dialogs
│   ├── infrastructure/  # Core services and setup
│   ├── logic/          # Business logic
│   ├── models/         # Data models and interfaces
│   └── state/          # State management
```

## Documentation Structure

- [Architecture Overview](./architecture/overview.md)
  - System design and component relationships
  - State management system
  - Event system and message hub

- [Core Systems](./core-systems/scene.md)
  - Scene management
  - Movable system
  - Positioning system

- [Interaction Model](./interaction/controls.md)
  - User input handling
  - Drag and drop operations
  - UI integration

- [API Documentation](./api/core-api.md)
  - Core APIs
  - State management APIs
  - Event system APIs

## Quick Start

1. **Installation**
   ```bash
   npm install
   ```

2. **Development**
   ```bash
   npm run dev
   ```
   This will start the development server with hot reloading.

3. **Building**
   ```bash
   npm run build
   ```
   Compiles the TypeScript code and bundles the application.

## Key Concepts

### Movable Items
The core concept of the application revolves around "movable items" - objects that can be placed, rotated, and connected in the 3D space. These include:
- Tracks
- Connectors
- Lighting fixtures
- Power sources

### Positioning System
The application implements a sophisticated positioning system that handles:
- Snap points for precise alignment
- Connection validation
- Collision detection
- Height and distance constraints

### State Management
The application uses a robust state management system that:
- Maintains the current configuration state
- Handles undo/redo operations
- Manages view state and camera positions
- Synchronizes UI and 3D state

## Contributing

See the [Architecture Overview](./architecture/overview.md) for detailed information about the system design and components.

## License

Proprietary - All rights reserved
