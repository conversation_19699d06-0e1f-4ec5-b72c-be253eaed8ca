import { intersectControls } from "./intersectObjects";
import { Vec2 } from "@immugio/three-math-extensions";
import { HorizontalPlaneDragger, LineDragger } from "../controls/dragging/BaseDragger";
/**
 * CanvasController is a helper class that handles mouse and touch events on the canvas and dispatches them to the controls.
 * It should be un-opinionated, it should not contain any logic, it should just handle the events and pass them on
 * TODO: The current implementation more or less follows the vision but the dragger handling needs to be reviewed
 */
export class CanvasController {
    context;
    disableOrbitControls;
    dragger;
    constructor(context, disableOrbitControls) {
        this.context = context;
        this.disableOrbitControls = disableOrbitControls;
        this.handleStart = this.handleStart.bind(this);
        this.handleEnd = this.handleEnd.bind(this);
        this.handleCancel = this.handleCancel.bind(this);
        this.handleMove = this.handleMove.bind(this);
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.start();
    }
    start() {
        this.context.canvas.addEventListener("touchstart", this.handleStart);
        this.context.canvas.addEventListener("touchend", this.handleEnd);
        this.context.canvas.addEventListener("touchcancel", this.handleCancel);
        this.context.canvas.addEventListener("touchmove", this.handleMove);
        this.context.canvas.addEventListener("mousemove", this.handleMouseMove);
        this.context.canvas.addEventListener("mousedown", this.handleMouseDown);
        this.context.canvas.addEventListener("mouseup", this.handleMouseUp);
    }
    dispose() {
        this.context.canvas.removeEventListener("touchstart", this.handleStart);
        this.context.canvas.removeEventListener("touchend", this.handleEnd);
        this.context.canvas.removeEventListener("touchcancel", this.handleCancel);
        this.context.canvas.removeEventListener("touchmove", this.handleMove);
        this.context.canvas.removeEventListener("mousemove", this.handleMouseMove);
        this.context.canvas.removeEventListener("mousedown", this.handleMouseDown);
        this.context.canvas.removeEventListener("mouseup", this.handleMouseUp);
    }
    handleStart(evt) {
        console.log("handleStart", evt);
    }
    handleMove(evt) {
        console.log("handleMove", evt);
    }
    handleCancel(evt) {
        console.log("handleCancel", evt);
    }
    handleEnd(evt) {
        console.log("handleEnd", evt);
    }
    lastControl = null;
    handleMouseUp(evt) {
        this.disableOrbitControls?.(false);
        this.dragger?.dispose();
        this.dragger = null;
    }
    handleMouseMove(evt) {
        const control = this.getControl(evt);
        if (control !== this.lastControl) {
            this.lastControl?.onHover(false);
        }
        this.lastControl = control;
        this.lastControl?.onHover(true);
        const coords = this.getCoords(evt);
        this.dragger?.onDrag(coords);
        document.body.style.cursor = control ? "pointer" : "default";
    }
    handleMouseDown(e) {
        const control = this.getControl(e);
        if (!control) {
            return;
        }
        this.disableOrbitControls?.(true);
        control.onMouseDown(e);
        if (control.interaction?.dragger === "HorizontalPlaneDragger") {
            this.dragger = new HorizontalPlaneDragger(this.context, control.interaction);
        }
        if (control.interaction?.dragger === "LineDragger") {
            this.dragger = new LineDragger(this.context, control.interaction);
        }
    }
    getControl(evt) {
        const coords = this.getCoords(evt);
        return intersectControls(this.context.canvas, this.context.camera, coords, this.context.scene.children);
    }
    getCoords(evt) {
        return evt instanceof TouchEvent ? new Vec2(evt.touches[0].clientX, evt.touches[0].clientY) : new Vec2(evt.clientX, evt.clientY);
    }
}
