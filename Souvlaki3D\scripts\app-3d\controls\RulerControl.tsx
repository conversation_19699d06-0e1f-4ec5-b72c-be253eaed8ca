import { IApplicationState } from "../../models/state/IApplicationState";
import { ISnapPoint } from "../../models/positioning/ISnapPoint";
import { MousePosition } from "../../models/state/MousePosition";
import { IPoint2D } from "../../models/geometry/IPoint2D";
import { Scene } from "../../models/scene/Scene";
import { CanvasControl } from "./CanvasControl";
import { CanvasUtils } from "../utils/CanvasUtils";
import { PositioningUtils } from "../../logic/helpers/PositioningUtils";
import { PolygonUtils } from "../../logic/helpers/PolygonUtils";
import { TextUtils } from "../utils/TextUtils";
import { BufferAttribute, BufferGeometry, Line, LineBasicMaterial, Mesh, MeshBasicMaterial, SphereGeometry, Vector3 } from "three";
import { TooltipMessage } from "../../app-dialogs/components/TooltipMessage";
import { isNear } from "../../logic/helpers/IsNear";
import { dom } from "@immugio/jsx-and-html-utils";
import { settings } from "../../configurations/setup_global";

export class RulerControl extends CanvasControl {

    public static $inject = ["scene"];

    private readonly dot1: Mesh;
    private readonly dot2: Mesh;
    private readonly line: Line;

    private point1: Vector3;
    private point2: Vector3;

    private lastPoint1: Vector3;
    private lastPoint2: Vector3;

    private readonly tooltip: TooltipMessage;

    private lastClickPosition: MousePosition;
    private snaps: ISnapPoint[];

    constructor(private scene: Scene) {
        super();

        this.dot1 = this.createDot();
        this.dot2 = this.createDot();
        this.line = this.createLine();
        this.tooltip = document.body.appendChild(<TooltipMessage message={settings.messages.rulerStart} />);
        this.tooltip.hide();
    }

    public update(s: IApplicationState): void {
        if (!s.view.ruler) {
            if (this.object3D.children.length > 0) {
                this.clearChildren();
                this.tooltip.hide();
                this.tooltip.message = settings.messages.rulerStart;
            }
        }

        if (s.view.ruler && s.view.rawMousePosition) {
            if (this.object3D.children.length === 0) {
                this.point1 = undefined;
                this.point2 = undefined;
                this.lastPoint1 = undefined;
                this.lastPoint2 = undefined;

                this.snaps = this.getSnapPoints();
                this.lastClickPosition = s.view.onMouseDownPos;

                this.updateObjects(new Vector3(), new Vector3());
                this.object3D.add(this.dot1, this.dot2, this.line);
            }

            this.updateRuler(s);
        }
    }

    private getSnapPoints(): ISnapPoint[] {
        const snaps: ISnapPoint[] = [];
        this.scene.control.object3D.traverse(o => {

            if (o instanceof Mesh) {
                const vertices = o.geometry.getAttribute("position").array;
                for (let i = 0; i < vertices.length; i += 3) {
                    const v = new Vector3(vertices[i], vertices[i + 1], vertices[i + 2]);
                    const w = o.localToWorld(v);
                    snaps.push({ x: w.x, y: w.y, z: w.z, side: null });
                }
            }
        });

        return snaps;
    }

    private updateRuler(s: IApplicationState) {
        const point = this.intersect(s.view.rawMousePosition);
        if (point) {
            const snapAdjustment = PositioningUtils.snapPoints(this.snaps, [{ x: point.x, y: point.y, z: point.z, side: null }], 100);
            if (snapAdjustment) {
                point.x -= snapAdjustment.x;
                point.y -= snapAdjustment.y;
                point.z -= snapAdjustment.z;
            }
        }

        // On each mouse click cyclically set point1, point2 and null them when both are set
        if (s.view.onMouseDownPos !== this.lastClickPosition && point) {
            const t1 = s.view.onMouseDownPos?.time ?? 0;
            const t2 = this.lastClickPosition?.time ?? 0;

            if (t1 - t2 > 300) {
                this.lastClickPosition = s.view.onMouseDownPos;
                if (!this.point1) {
                    this.point1 = point;

                } else if (!this.point2) {
                    this.point2 = point;

                } else {
                    this.point1 = null;
                    this.point2 = null;
                }
            }
        }

        // Dots will be either on click position of follow the cursor if no click positions are set
        const p1 = this.point1 || point;
        const p2 = this.point2 || point;

        // Update graphics
        if (p1 && p2) {
            // Only update if there is a change
            if (!this.lastPoint1 || !this.lastPoint2 || !isNear(p1, this.lastPoint1) || !isNear(p2, this.lastPoint2)) {
                this.lastPoint1 = p1;
                this.lastPoint2 = p2;

                this.updateObjects(p1, p2);

                const distance = PolygonUtils.pointsDistance(p1, p2);
                this.tooltip.message = p1 === p2 ? settings.messages.rulerStart : TextUtils.formatText(distance, 0, s.view.unitType);
            }
        }
    }

    private intersect(p: IPoint2D): Vector3 {
        let point: Vector3 = null;

        const intersections = CanvasUtils.intersectObjects(this.scene, p.x, p.y);
        if (intersections.length) {
            const intersection = intersections.find(x => !this.object3D.children.includes(x.object) && !this.object3D.children.includes(x.object.parent));

            if (intersection) {
                point = intersection.point;
            }
        }

        return point;
    }

    private updateObjects(p1: Vector3, p2: Vector3): void {
        this.dot1.position.copy(p1);
        this.dot2.position.copy(p2);

        const vertices = new Float32Array([
            p1.x, p1.y, p1.z,
            p2.x, p2.y, p2.z
        ]);
        this.line.geometry.setAttribute("position", new BufferAttribute(vertices, 3));

        this.tooltip.show();
    }

    private createDot(): Mesh {
        return new Mesh(
            new SphereGeometry(20, 8, 8),
            new MeshBasicMaterial({ color: "red" })
        );
    }

    private createLine(): Line {
        const geo = new BufferGeometry().setFromPoints([new Vector3(), new Vector3()]);
        return new Line(
            geo,
            new LineBasicMaterial({ color: "red" })
        );
    }
}