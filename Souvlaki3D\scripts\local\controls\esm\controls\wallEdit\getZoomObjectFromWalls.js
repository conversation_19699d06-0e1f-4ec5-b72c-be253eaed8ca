import { MeshBasicMaterial, PlaneGeometry } from "three";
import { Mesh3D } from "../../objects/Mesh3D";
/**
 * // Create an object slightly larger than the current building to get some margins around on auto zoom
 * @param walls
 * @param offset
 */
export function getZoomObjectFromWalls(walls, offset) {
    const sizeX = Math.max(Math.max(...walls.map(w => w.startPointOnPlan.x)) - Math.min(...walls.map(w => w.endPointOnPlan.x)), 1000);
    const sizeZ = Math.max(Math.max(...walls.map(w => w.startPointOnPlan.y)) - Math.min(...walls.map(w => w.endPointOnPlan.y)), 1000);
    const zoomObject = new Mesh3D(new PlaneGeometry(sizeX * 1.5, sizeZ * 1.5), new MeshBasicMaterial({ wireframe: true, transparent: true, opacity: 0 }));
    zoomObject.position.set(-offset.x, -200, -offset.z);
    zoomObject.rotation.x = -Math.PI / 2;
    return zoomObject;
}
