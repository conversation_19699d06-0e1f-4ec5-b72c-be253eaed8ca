# Movable Items System

## Overview

The Movable Items System is a core component that handles all aspects of movable objects in the 3D space, including:
- Tracks and connectors
- Magnetic accessories
- Power sources
- Positioning and snapping
- Grouping and relationships

## Architecture

```mermaid
graph TD
    A[MovablesLogic] --> B[Positioning]
    A --> C[Validation]
    A --> D[Operations]
    A --> E[Groups]
    
    B --> B1[Snap Points]
    B --> B2[Alignment]
    B --> B3[Constraints]
    
    C --> C1[Collision]
    C --> C2[Connection]
    C --> C3[Power]
    
    D --> D1[Insert]
    D --> D2[Swap]
    D --> D3[Rotate]
    
    E --> E1[Connected Items]
    E --> E2[Power Groups]
    E --> E3[Position Groups]
```

## Core Components

### MovablesLogic
The central system for managing movable items:

```typescript
class MovablesLogic {
    // Core operations
    public insert(d: DesignState, dragged: MovableItem, insertPoints: ISnapPoint[]): void
    public swap(d: DesignState, current: MovableItem, replacement: MovableItem): MovableItem
    public rotate(d: DesignState, movables: MovableItem[], clockwise: boolean): void
    
    // Positioning
    public attachToSnap(m: MovableItem, snap: ISnapPoint): void
    public createPositionGroup(d: DesignState, movSnaps: IMovableSnap[]): IPositionGroup
    
    // Validation
    public canReplace(d: DesignState, m: MovableItem): MovableItem
    public canInsert(inserts: ISnapPoint[], m: MovableItem): ISnapPoint[]
}
```

## Movable Items

### Item Structure
```typescript
interface MovableItem {
    id: string;
    type: string;
    x: number;
    y: number;
    z: number;
    width: number;
    height: number;
    depth: number;
    rotationX: number;
    rotationY: number;
    rotationZ: number;
    
    // Placement
    placementMode: PlacementMode;
    elevation: number;
    offset: number;
    
    // State
    active: boolean;
    selected: boolean;
    refresh: boolean;
    
    // Relationships
    parentId?: string;
    behavior: IMovableBehavior;
    
    // Power
    data: {
        power: number;
        kelvin: number;
        control: string;
    };
}
```

### Placement Modes
```typescript
enum PlacementMode {
    None = 0,
    Horizontal = 1,
    Ceiling = 2,
    Walls = 4,
    ExternalWalls = 8,
    Corners = 16,
    Attachment = 32
}
```

## Positioning System

### Snap Points
```typescript
interface ISnapPoint {
    x: number;
    y: number;
    z: number;
    side: IPoint3D;  // Direction vector
    movable?: MovableItem;  // Associated item
}
```

### Position Groups
```typescript
interface IPositionGroup {
    group: MovableItem;  // Group container
    placements: PlacementMode[];  // Allowed placements
    contains: { movable: MovableItem }[];  // Group members
}
```

## Operations

### 1. Insertion
```typescript
public insert(d: DesignState, dragged: MovableItem, insertPoints: ISnapPoint[]): void {
    // 1. Get fixed point
    const startPoint = insertPoints[0];
    const startItem = this.getMovableBySnap(d.movableItems, startPoint);
    
    // 2. Attach dragged item
    this.attachToSnap(dragged, startPoint);
    
    // 3. Get connection point
    const draggedSnaps = getMovablePoints(dragged, dragged.snaps, dragged);
    const free = draggedSnaps.find(x => !isNear(x, startPoint));
    
    // 4. Connect group
    const groupItems = this.getConnectedItems(movables, groupStartItem);
    this.moveGroup(d, groupItems, groupStartPoint, free);
}
```

### 2. Swapping
```typescript
public swap(d: DesignState, current: MovableItem, replacement: MovableItem): MovableItem {
    // Copy position
    MovablesUtils.copyPosition(current, replacement);
    
    // Update connections
    const currentSnaps = getMovablePoints(current, current.snaps, current);
    const replacementSnaps = getMovablePoints(replacement, replacement.snaps, replacement);
    
    // Reconnect groups
    for (const snap of currentSnaps) {
        const group = this.getConnectedItemsToPoint(d.movableItems, current, snap);
        this.reconnectGroup(d, group, snap, replacementSnaps);
    }
    
    return replacement;
}
```

### 3. Rotation
```typescript
public rotate(d: DesignState, movables: MovableItem[], clockwise: boolean): void {
    const m = movables[0];
    if (m.behavior.rotateAlgorithm) {
        const rotateAlgorithm = Activator.get<IRotateAlgorithm>(m.behavior.rotateAlgorithm);
        rotateAlgorithm.rotate(d, movables, clockwise);
    }
}
```

## Validation

### 1. Connection Validation
```typescript
public canInsert(inserts: ISnapPoint[], m: MovableItem): ISnapPoint[] {
    if (!(m.placementMode & (PlacementMode.Ceiling | PlacementMode.Walls))) {
        return null;
    }
    
    const distances = inserts.map(insert => ({
        insert,
        distance: PolygonUtils.pointsDistance(m, insert)
    }));
    
    return this.validateConnections(distances);
}
```

### 2. Replacement Validation
```typescript
public canReplace(d: DesignState, m: MovableItem): MovableItem {
    const max = 200;
    const distances = d.movableItems
        .filter(o => m !== o)
        .map(o => ({
            movable: o,
            distance: PolygonUtils.pointsDistance(m, o)
        }))
        .filter(x => x.distance < max);
    
    return this.findValidReplacement(distances);
}
```

## Groups and Relationships

### 1. Connected Items
```typescript
public getConnectedItems(movables: MovableItem[], m: MovableItem): IMovableSnap[] {
    const groups = getConnectedGroups(movables);
    return groups.find(group => group.some(x => x.movable.id === m.id));
}
```

### 2. Position Groups
```typescript
public createPositionGroup(d: DesignState, movSnaps: IMovableSnap[]): IPositionGroup {
    const movs = movSnaps.map(x => x.movable);
    const placements = this.getCommonPlacements(movs);
    
    return {
        group: MovablesUtils.createGroupItem(movs),
        placements,
        contains: movs.map(c => ({ movable: c }))
    };
}
```

## Best Practices

1. **Item Management**
   - Maintain clear item relationships
   - Handle cleanup properly
   - Validate connections

2. **Positioning**
   - Use snap points for alignment
   - Validate placement constraints
   - Handle edge cases

3. **Performance**
   - Optimize group operations
   - Cache calculations
   - Minimize updates

4. **State Management**
   - Track item state changes
   - Handle undo/redo properly
   - Maintain consistency

## Common Operations

### 1. Adding Items
```typescript
// Create new item
const m = MovablesUtils.getInstance(item.type);
m.data = { ...item.data };
m.active = true;

// Add to scene
if (this.validatePlacement(m)) {
    this.state.saveHistory();
    this.addMovable(m);
    this.state.view.activeItem = {
        movable: m,
        interactionType: InteractionType.InsertingItem
    };
}
```

### 2. Moving Groups
```typescript
private moveGroup(d: DesignState, group: MovableItem[], current: IPoint3D, newLocation: IPoint3D): void {
    const diff = {
        x: current.x - newLocation.x,
        y: current.y - newLocation.y,
        z: current.z - newLocation.z
    };
    
    group.forEach(item => {
        item.x -= diff.x;
        item.y -= diff.y;
        item.z -= diff.z;
        
        // Update children
        const children = d.movableItems.filter(ch => ch.parentId === item.id);
        children.forEach(child => {
            child.x -= diff.x;
            child.y -= diff.y;
            child.z -= diff.z;
        });
    });
}
```

### 3. Validation
```typescript
private checkInsertDistance(movable: MovableItem, currentDistance: number): boolean {
    const globalMaxDistance = 400;
    const maximumDistance = movable.behavior.insertBetweenMaxDistance || globalMaxDistance;
    return maximumDistance > currentDistance;
}
```

## Future Considerations

1. **Enhanced Validation**
   - Power constraints
   - Weight distribution
   - Complex connections

2. **Performance Optimization**
   - Spatial partitioning
   - Calculation caching
   - Update batching

3. **Feature Extensions**
   - Custom behaviors
   - Advanced grouping
   - Dynamic constraints
