﻿import { MovableItem } from "../../models/state/MovableItem";
import { DesignState } from "../../models/state/DesignState";
import { Display } from "../../models/common/Enums";
import { IGraphicsData } from "../../models/infrastructure/IApplicationSettings";
import { IGraphicsLoaded } from "./IGraphicsLoaded";
import { MaterialUtils } from "../utils/MaterialUtils";
import { Material, Object3D } from "three";

export class MovableGraphics {

    protected cachedGraphics: Object3D;
    protected cachedHighlight: Object3D;

    public createGraphics(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData, callback: IGraphicsLoaded): Object3D {
        if (this.shouldUpdate(d, m, display, data) || !this.cachedGraphics) {
            this.cachedGraphics = this.createMesh(d, m, display, data, this.getMaterial(d, m, data));
            this.cachedHighlight = null;
        }

        return this.cachedGraphics;
    }

    public createHighlight(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData, material: Material, partId?: string): Object3D {
        if (!this.cachedHighlight) {
            this.cachedHighlight = this.createMesh(d, m, display, data, [material]);
            if (this.cachedHighlight) this.cachedHighlight.position.y += 1;
        }

        return this.cachedHighlight;
    }

    protected createMesh(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData, materials: Material[]): Object3D {
        return undefined;
    }

    protected shouldUpdate(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData): boolean {
        return false;
    }

    protected getMaterial(d: DesignState, m: MovableItem, data: IGraphicsData): Material[] {
        const key = m?.finishes[0] || data?.finishes[0];
        return key ? [MaterialUtils.getMaterial(key)] : undefined;
    }
}