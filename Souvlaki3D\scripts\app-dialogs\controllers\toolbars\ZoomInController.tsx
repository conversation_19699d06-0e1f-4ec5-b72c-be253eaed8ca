import { BaseActionController } from "./BaseActionController";
import { IToolbarActionOptions } from "./IToolbarActionOptions";
import { MessageHub } from "../../../infrastructure/MessageHub";
import { Events } from "../../../models/infrastructure/Events";

export class ZoomInController extends BaseActionController {

    constructor(options: IToolbarActionOptions) {
        super(options);
        this.defaultInitialize();
    }

    public execute(): void {
        MessageHub.broadcast(Events.zoomIn);
    }
}