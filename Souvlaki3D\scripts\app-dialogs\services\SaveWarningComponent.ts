import { StateService } from "../../state/StateService";
import { BaseController } from "../controllers/BaseController";

export class SaveWarningComponent extends BaseController {

    public static $inject = ["StateService"];

    constructor(stateService: StateService) {
        super();

        const text = "Your unsaved work will be lost if you close the browser window";

        window.onbeforeunload = (e) => {
            if (stateService.view.changed && localStorage.getItem("disableWarning") !== "true") {
                e.returnValue = text;
                return text;
            }
        };
    }
}