import { Object3D } from "three";
import { IPoint3D } from "../../models/geometry/IPoint3D";
import { IInteractionOptions } from "../../models/positioning/IInteractionOptions";

export class Container3D extends Object3D {

    public interaction?: IInteractionOptions;

    constructor(...children: Object3D[]) {
        super();

        if (children?.length) {
            this.add(...children.filter(child => child));
        }
    }

    public addInteraction(interaction: IInteractionOptions): this {
        this.interaction = interaction;
        return this;
    }

    public setPosition(x: number = undefined, y: number = undefined, z: number = undefined): Container3D {
        this.position.set(x || 0, y || 0, z || 0);
        return this;
    }

    public setRotation(x: number = undefined, y: number = undefined, z: number = undefined): Container3D {
        this.rotation.set(x || 0, y || 0, z || 0);
        return this;
    }

    public lookAtPoint(point: IPoint3D): Container3D {
        this.lookAt(point.x, point.y, point.z);
        return this;
    }

    public setRenderOrder(order: number): Container3D {
        this.traverse((child: Object3D) => {
            child.renderOrder = order;
        });
        this.renderOrder = order;
        return this;
    }
}