﻿import { ISnapPoint } from "../../models/positioning/ISnapPoint";
import { IPoint2D } from "../../models/geometry/IPoint2D";
import { Size } from "../../models/geometry/Size";
import { Box3, BufferGeometry, ExtrudeGeometry, Material, Mesh, Object3D, Path, Shape, Vector2 } from "three";
import { Vec3 } from "@immugio/three-math-extensions";

export function getParentWithNameStarting(object: Object3D, name: string): Object3D {
    if (object.parent) {
        if (object.parent.name.startsWith(name)) {
            return object.parent;
        }
        return getParentWithNameStarting(object.parent, name);
    }
    return null;
}

export function getParentWithUserData<T>(object: Object3D, name: string): T {
    if (object.parent) {
        if (object.parent.userData[name]) {
            return object.parent.userData[name];
        }
        return getParentWithUserData<T>(object.parent, name);
    }
    return null;
}

export class MeshUtils {

    public static createExtrusion(outer: IPoint2D[], inners: Array<IPoint2D>[], height: number, materials: Material): Mesh {

        const extrusionSettings = { bevelEnabled: false, material: 0, extrudeMaterial: 1, depth: height };

        const shape = new Shape(outer as Vector2[]); // Cast should be safe, it expects '{x: number; y: number }[]' not necessarily Vector2[]

        if (inners) {

            for (let i = 0, l = inners.length; i < l; i++) {

                shape.holes.push(new Path(inners[i] as Vector2[]));  // Cast should be safe, it expects '{x: number; y: number }[]' not necessarily Vector2[]
            }
        }

        const geo = new ExtrudeGeometry(shape, extrusionSettings);
        const mesh = new Mesh(geo, materials);
        mesh.rotation.x = 90 * Math.PI / 180;

        return mesh;
    }

    public static getMeshSize(geometry: BufferGeometry, compute: boolean): Size {

        if (compute) geometry.computeBoundingBox();

        const width = geometry.boundingBox.max.x - geometry.boundingBox.min.x;
        const height = geometry.boundingBox.max.y - geometry.boundingBox.min.y;
        const depth = geometry.boundingBox.max.z - geometry.boundingBox.min.z;

        return new Size(width, height, depth);
    }

    public static removeHelperPoints(model: Object3D): void {

        const remove: Object3D[] = [];

        model.traverse(o => {
            if (o.name.startsWith("snap-") || o.name.startsWith("holder-") || o.name.startsWith("attachment-")) {
                remove.push(o);
            }
        });

        for (const r of remove) {
            r.parent.remove(r);
        }

        model.updateMatrix();
    }

    public static getSnaps(model: Object3D): ISnapPoint[] {

        const source = this.getFunctionPoints(model, "snap");
        const snaps: ISnapPoint[] = [];

        for (let i = 0; i < source.length; i += 2) {

            const start = Vec3.fromPoint(source[i]);
            const direction = Vec3.fromPoint(source[i + 1]);

            const side = direction.clone().sub(start).normalize();

            const p = {
                x: start.x,
                y: start.y,
                z: start.z,
                side: {
                    x: side.x,
                    y: side.y,
                    z: side.z
                }
            };
            snaps.push(p);
        }

        return snaps;
    }

    public static getFunctionPoints(model: Object3D, prefix: string): ISnapPoint[] {

        const objects: Object3D[] = [];

        model.traverse(o => {
            if (o.name.startsWith(`${prefix}-`)) {
                objects.push(o);
            }
        });

        const points: ISnapPoint[] = [];

        for (const a of objects) {
            if (!a.name.endsWith("a")) {
                continue;
            }

            const b = objects.find(x => x.name === (a.name.replace("-a", "-b")));
            if (!b) {
                console.log("Matching point missing");
                continue;
            }

            [a, b].forEach(x => {
                const p = this.getMeshCenter(x);
                points.push({ x: p.x, y: p.y, z: p.z, side: null });
            });
        }

        return points;
    }

    private static getMeshCenter(o: Object3D) {
        const pos = o.position.clone();

        if (o instanceof Mesh) {
            if (o.geometry) {
                const box = new Box3().setFromBufferAttribute(o.geometry.attributes.position);
                const center = box.getCenter(new Vec3());
                pos.add(center);
            }
        }

        return pos;
    }
}