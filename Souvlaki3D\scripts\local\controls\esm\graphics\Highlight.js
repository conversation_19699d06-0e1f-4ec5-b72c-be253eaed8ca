import { EdgesGeometry, LineBasicMaterial, LineSegments, Mesh, MeshBasicMaterial } from "three";
export class Highlight {
    static color = "#F2EFBD";
    static line = new LineBasicMaterial({ color: Highlight.color, opacity: 0.7, transparent: true, linewidth: 1, depthTest: false });
    static trans = new MeshBasicMaterial({ color: Highlight.color, opacity: 0.5, transparent: true, depthTest: false });
    static create(o) {
        const copy = o?.clone(true);
        if (copy) {
            copy.traverse(x => {
                if (x instanceof Mesh) {
                    x.material = this.trans;
                    const geo = new EdgesGeometry(x.geometry, 20);
                    const edges = new LineSegments(geo, this.line);
                    edges.position.copy(x.position);
                    edges.scale.copy(x.scale);
                    edges.rotation.copy(x.rotation);
                    edges.name = x.name;
                    (x.parent || copy).add(edges);
                }
            });
        }
        copy.position.set(0, 0, 0);
        copy.rotation.set(0, 0, 0);
        return copy;
    }
}
