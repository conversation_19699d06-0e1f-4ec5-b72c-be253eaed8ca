﻿import { StateService } from "../../state/StateService";
import { translate } from "./Translator";

export class AlertService {

    public static $inject = ["StateService"];

    private loading: HTMLElement;

    constructor(private state: StateService) {
        this.loading = document.getElementById("loading-indicator");
        this.loading.style.display = "none";
    }

    public snackbar(message: string | number, timeout: number): void {
        this.state.view.snackbar = translate((message || "").toString());

        if (timeout) {
            setTimeout(() => {
                this.state.view.snackbar = "";
            }, timeout);
        }
    }
}