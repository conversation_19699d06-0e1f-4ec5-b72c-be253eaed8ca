import { IApplicationSettings } from "../../../models/infrastructure/IApplicationSettings";
import { IApplicationState } from "../../../models/state/IApplicationState";
import { MovableItem } from "../../../models/state/MovableItem";
import { PowerLogic } from "../../../logic/PowerLogic";
import { CanvasControl } from "../CanvasControl";
import { DotControl } from "./DotControl";
import { Scene } from "../../../models/scene/Scene";
import { AvailableActions } from "../../../models/common/Enums";
import { PositioningUtils } from "../../../logic/helpers/PositioningUtils";
import { IMovableSnap } from "../../../models/positioning/IMovableSnap";

type ItemControl = {
    dots: DotControl[],
    movable: MovableItem
};

export class PowerControl extends CanvasControl {

    public static $inject = ["settings", "PowerLogic", "scene"]

    constructor(private settings: IApplicationSettings, private powerLogic: PowerLogic, private scene: Scene) {
        super();
    }

    private controls: ItemControl[] = [];

    public update(s: IApplicationState): void {
        if (!s.view.isActionAvailable(AvailableActions.Power)) {
            if (this.children.length > 0) {
                this.controls = [];
                this.clearChildren();
            }

            return;
        }

        this.object3D.position.x = -s.view.offset.x;
        this.object3D.position.z = -s.view.offset.y;

        const movables = s.design.movableItems.filter(x => !x.data.attachment);
        if (this.changed(movables)) {
            this.clearChildren();
            this.controls = [];

            const snaps = PositioningUtils.getMovableSnaps(movables);

            for (const m of movables) {
                const control = this.getControlsForMovable(m, snaps);
                this.controls.push(control);
            }
        }

        super.update(s);
    }

    private changed(movables: MovableItem[]): boolean {
        if (this.controls.length !== movables.length) {
            return true;
        }

        for (let i = 0; i < movables.length; i++) {
            const m = movables[i];
            const c = this.controls[i];
            if (m !== c.movable) {
                return true;
            }
        }

        return false;
    }

    private getControlsForMovable(m: MovableItem, snaps: IMovableSnap[]) {
        const control: ItemControl = {
            dots: [],
            movable: m
        };

        const allowed = this.powerLogic.getAllowedPoints(m, snaps);

        allowed.points.forEach(x => {
            const index = m.snaps.indexOf(x); // Index in the original array
            const dot = new DotControl(m, index, allowed.endIndex !== undefined, this.settings, this.scene);
            control.dots.push(dot);
            this.addChild(dot);
        });

        return control;
    }
}