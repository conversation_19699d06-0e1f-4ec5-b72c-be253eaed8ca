import { CanvasControl } from "./CanvasControl";
import { IApplicationState } from "../../models/state/IApplicationState";
import { CylinderGeometry, Mesh, Material, BufferGeometry } from "three";
import { MovableItem } from "../../models/state/MovableItem";
import { getMaterial } from "../utils/MaterialUtils";
import { InteractionType } from "../../models/common/Enums";
import { ISuspensionCable, SuspensionCableUtils } from "../../logic/helpers/SuspensionCableUtils";
import { PipeGeometry } from "../graphics/PipeGeometry";
import { EdgesUtils } from "../utils/EdgesUtils";

interface ISuspensionCableOptions {
    edgeColor: string;
    suspensionCableMaterial: string;
    suspensionCableRadius: number;
    suspensionCableInnerRadius: number;
}

export class SuspensionControl extends CanvasControl {

    private readonly material: Material;
    private readonly geometry: BufferGeometry;
    private current: MovableItem[] = [];

    constructor(private options: ISuspensionCableOptions) {
        super();
        this.material = getMaterial(options.suspensionCableMaterial);
        this.geometry = options.suspensionCableInnerRadius
            ? new PipeGeometry(options.suspensionCableRadius, this.options.suspensionCableInnerRadius, 1)
            : new CylinderGeometry(this.options.suspensionCableRadius, this.options.suspensionCableRadius, 1);
    }

    public update(s: IApplicationState): void {
        if (s.view.activeItem?.interactionType & (InteractionType.DraggingItem | InteractionType.InsertingItem)) {
            if (this.object3D.children.length) {
                this.clear();
            }
            return;
        }

        if (this.changed(s.design.movableItems) || s.diff.structureChanged) {
            this.object3D.position.x = -s.view.offset.x;
            this.object3D.position.z = -s.view.offset.y;

            this.clear();

            this.current = s.design.movableItems.map(x => x.clone());
            SuspensionCableUtils.getSuspensionCablesPositions(s.design).forEach(x => this.addCylinder(x));
        }
    }

    private clear(): void {
        this.clearChildren();
        this.current = [];
    }

    private addCylinder(point: ISuspensionCable) {
        const mesh = new Mesh(this.geometry, this.material);
        mesh.scale.y = point.height;
        mesh.position.copy(point.center);
        this.object3D.add(mesh);

        if (this.options.edgeColor) {
            EdgesUtils.drawEdges(mesh, this.options.edgeColor, 5);
        }
    }

    private changed(items: MovableItem[]): boolean {
        if (this.current.length !== items.length) {
            return true;
        }

        for (let i = 0; i < items.length; i++) {
            if (!items[i].equals(this.current[i])) {
                return true;
            }
        }

        return false;
    }
}