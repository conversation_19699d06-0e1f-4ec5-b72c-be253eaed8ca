# Scene Management System

## Overview

The Scene Management System is responsible for handling all aspects of the 3D visualization using Three.js. It manages:
- Scene setup and configuration
- Camera controls and positioning
- Rendering pipeline
- Scene updates and optimization
- User interaction with 3D elements

## Architecture

```mermaid
graph TD
    A[Scene] --> B[Renderer]
    A --> C[Camera]
    A --> D[Controls]
    A --> E[Scene Objects]
    
    B --> B1[WebGL Renderer]
    B --> B2[Render Loop]
    
    C --> C1[Perspective Camera]
    C --> C2[Orthographic Camera]
    
    D --> D1[Camera Controls]
    D --> D2[Object Controls]
    D --> D3[Interaction Controls]
    
    E --> E1[Movable Items]
    E --> E2[Building Elements]
    E --> E3[Visual Helpers]
```

## Scene Setup

### Scene Initialization
```typescript
class Scene {
    public renderer: THREE.WebGLRenderer;
    public camera: THREE.Camera;
    public control: SceneControl;
    public center: THREE.Vector3;
    
    constructor() {
        this.initRenderer();
        this.initCamera();
        this.initControls();
        this.setupLighting();
    }
}
```

### Camera Configuration
```typescript
private initCamera(): void {
    // Perspective camera for 3D view
    this.cameraP = new THREE.PerspectiveCamera(
        45,
        window.innerWidth / window.innerHeight,
        1,
        10000
    );
    
    // Orthographic camera for 2D view
    this.cameraO = new THREE.OrthographicCamera(
        window.innerWidth / -2,
        window.innerWidth / 2,
        window.innerHeight / 2,
        window.innerHeight / -2,
        1,
        10000
    );
}
```

## Camera Controls

### View Modes

1. **Perspective View**
```typescript
public toPerspective(scene: Scene): void {
    scene.camera = scene.cameraP;
    scene.camera.position.set(
        settings.view.radius * Math.sin(theta) * Math.cos(phi),
        settings.view.radius * Math.sin(phi),
        settings.view.radius * Math.cos(theta) * Math.cos(phi)
    );
    scene.camera.lookAt(scene.center);
}
```

2. **Orthographic View**
```typescript
public toOrtho(scene: Scene): void {
    scene.camera = scene.cameraO;
    scene.camera.position.set(0, 1000, 0);
    scene.camera.lookAt(scene.center);
}
```

### Camera Movement
```typescript
class Pan {
    public static pan(x: number, y: number, camera: THREE.Camera, center: THREE.Vector3): void {
        const deltaX = (x - this.lastX) * settings.interaction.panSpeed;
        const deltaY = (y - this.lastY) * settings.interaction.panSpeed;
        
        // Update camera position
        camera.position.x -= deltaX;
        camera.position.y += deltaY;
        center.x -= deltaX;
        center.y += deltaY;
        
        camera.lookAt(center);
    }
}
```

## Scene Controls

### Control Types

1. **MovableItemControl**
```typescript
class MovableItemControl implements IControl {
    public interaction: IInteractionOptions;
    
    public update(state: IApplicationState): void {
        // Update movable item position and rotation
        this.updatePosition();
        this.updateRotation();
        this.updateScale();
    }
}
```

2. **BuildingControl**
```typescript
class BuildingControl implements IControl {
    public update(state: IApplicationState): void {
        // Update building elements
        this.updateWalls();
        this.updateFloor();
        this.updateCeiling();
    }
}
```

3. **ResizeControl**
```typescript
class ResizeControl implements IControl {
    public update(state: IApplicationState): void {
        // Handle resizing operations
        this.updateDimensions();
        this.updateHandles();
        this.updateConstraints();
    }
}
```

## Rendering System

### Render Loop
```typescript
private run(): void {
    // Process state updates
    while (this.state.applyUpdate()) {
        this.update();
    }
    
    // Update scene
    this.update();
    
    // Request next frame
    requestAnimationFrame(() => this.run());
}
```

### Scene Updates
```typescript
private update(): void {
    // Compare state changes
    this.state.compare();
    
    // Update controls
    this.scene.control.update(this.state);
    
    // Update UI components
    this.controllers.forEach(x => x.update?.(this.state));
    
    // Store previous state
    this.state.setPreviousState();
}
```

## Interaction System

### Mouse Interaction
```typescript
private onMouseMove(e: MouseEvent): void {
    if (!this.state.view.activeItem) {
        this.state.view.rawMousePosition = { x: e.pageX, y: e.pageY };
        
        const hoveredItem = CanvasUtils.handleObjectHover(
            this.scene,
            this.state.view.rawMousePosition,
            this.state.view.hoveredItem,
            this.state.view.allowedInteractions
        );
        
        if (hoveredItem !== this.state.view.hoveredItem) {
            this.state.view.hoveredItem = hoveredItem;
            this.messageHub.broadcast(Events.hoverItem);
        }
    }
}
```

### Touch Interaction
```typescript
private onTouch(e: HammerInput): void {
    const hovered = CanvasUtils.objectFromMouse(
        this.scene,
        e.center.x,
        e.center.y,
        this.state.view.allowedInteractions
    );
    
    if (hovered?.interaction) {
        this.state.view.activeItem = hovered.interaction;
        this.messageHub.broadcast(Events.drawingInteraction, hovered.interaction);
    }
}
```

## Scene Utilities

### Canvas Utils
```typescript
class CanvasUtils {
    public static setSceneSize(scene: Scene): void {
        const width = scene.container.clientWidth;
        const height = scene.container.clientHeight;
        
        scene.renderer.setSize(width, height);
        scene.cameraP.aspect = width / height;
        scene.cameraP.updateProjectionMatrix();
        
        scene.cameraO.left = width / -2;
        scene.cameraO.right = width / 2;
        scene.cameraO.top = height / 2;
        scene.cameraO.bottom = height / -2;
        scene.cameraO.updateProjectionMatrix();
    }
    
    public static zoomExtends(scene: Scene, view: ViewState, target: THREE.Object3D): void {
        // Calculate bounding box
        const box = new THREE.Box3().setFromObject(target);
        const size = box.getSize(new THREE.Vector3());
        const center = box.getCenter(new THREE.Vector3());
        
        // Update camera and controls
        this.fitCameraToObject(scene.camera, size, center);
        this.updateControls(scene.control, view);
    }
}
```

## Performance Optimization

### 1. Render Optimization
- Request animation frame usage
- Selective rendering
- View frustum culling

### 2. Scene Graph Optimization
- Object pooling
- Geometry instancing
- Level of detail management

### 3. Memory Management
- Resource disposal
- Texture management
- Geometry cleanup

## Best Practices

1. **Scene Management**
   - Maintain clean scene graph
   - Implement proper cleanup
   - Use object pooling for frequent operations

2. **Camera Handling**
   - Smooth camera transitions
   - Proper view bounds
   - Responsive camera controls

3. **Performance**
   - Optimize render calls
   - Manage memory efficiently
   - Implement proper culling

4. **Interaction**
   - Responsive controls
   - Clear visual feedback
   - Proper event handling
