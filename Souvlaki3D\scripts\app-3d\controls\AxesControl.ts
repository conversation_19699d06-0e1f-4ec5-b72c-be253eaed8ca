import { IApplicationState } from "../../models/state/IApplicationState";
import { Attributes } from "../../models/common/Enums";
import { CanvasUtils } from "../utils/CanvasUtils";
import { CanvasControl } from "./CanvasControl";
import { TextUtils } from "../utils/TextUtils";
import { Scene } from "../../models/scene/Scene";
import { AxesHelper, LineBasicMaterial, Mesh, MeshBasicMaterial, Object3D, Scene as ThreeScene } from "three";
import { autogenerated } from "../../models/state/EnvironmentOptions";

export class AxesControl extends CanvasControl {

    public static $inject = ["scene"];

    private axes: AxesHelper;

    private texts: Mesh[] = [];
    private color = 0x707070;

    private textSize = 200;
    private textMat = new MeshBasicMaterial({ color: this.color });

    private axesScene: Scene;
    private threeScene: ThreeScene;

    private lastPhi: number;
    private lastTheta: number;

    private root = new Object3D();

    constructor(private mainScene: Scene) {
        super();

        this.createObjects();
        this.createScene();

        this.run();
    }

    private addText(text: string): Mesh {
        const mesh = TextUtils.getText(text, this.textSize, this.textMat, 1, false);
        this.root.add(mesh);
        this.texts.push(mesh);

        return mesh;
    }

    public update(s: IApplicationState): void {
        if (this.lastPhi === s.view.phi && this.lastTheta === s.view.theta) {
            return;
        }

        this.lastPhi = s.view.phi;
        this.lastTheta = s.view.theta;

        CanvasUtils.setCamera(this.axesScene, { theta: s.view.theta, phi: s.view.phi, radius: 3500 });
        this.axesScene.camera.lookAt(this.axesScene.center);

        for (const text of this.texts) {
            text.quaternion.copy(this.axesScene.camera.quaternion);
        }
    }

    private createObjects() {
        const axesSize = 1000;

        this.axes = new AxesHelper(axesSize);
        this.axes.material = new LineBasicMaterial({ color: this.color });

        this.root.add(this.axes);
        this.root.name = Attributes.ZoomIgnore;

        const margin = 20;

        this.addText("x").position.set(axesSize + margin, -this.textSize * 0.4, 0);
        this.addText("z").position.set(0, -this.textSize * .4, axesSize + margin);
        this.addText("y").position.set(0, axesSize + this.textSize / 2, 0);
    }

    private createScene() {
        const size = this.mainScene.container.clientWidth / 8;

        this.axesScene = CanvasUtils.createScene(this.mainScene.container, size, size, "canvas-axes", false, new Scene(), autogenerated);
        this.axesScene.renderer.setClearColor(0xFFFFFF, 0);
        const canvas = this.axesScene.renderer.domElement;

        canvas.style.position = "absolute";
        canvas.style.left = "0";
        canvas.style.bottom = "0";
        canvas.style.pointerEvents = "none";

        this.threeScene = new ThreeScene();
        this.threeScene.add(this.root);
    }

    private run(): void {
        this.axesScene.renderer.render(this.threeScene, this.axesScene.camera);
        window.requestAnimationFrame(() => this.run());
    }
}