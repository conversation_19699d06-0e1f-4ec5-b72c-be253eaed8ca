import { CanvasControl } from "./CanvasControl";
import { ISuspensionCableEx, SuspensionCableUtils } from "../../logic/helpers/SuspensionCableUtils";
import { MessageHub } from "../../infrastructure/MessageHub";
import { Events } from "../../models/infrastructure/Events";
import { StateService } from "../../state/StateService";
import { ISvkServerData } from "../../app-dialogs/controllers/SvkController";
import { IApplicationSettings, IMovableTemplate } from "../../models/infrastructure/IApplicationSettings";
import { ModelLoadUtils } from "../utils/ModelLoadUtils";
import { MathUtils, Object3D, Vector3 } from "three";
import { IApplicationState } from "../../models/state/IApplicationState";
import { AvailableActions } from "../../models/common/Enums";
import { InteractionType } from "../../models/common/Enums";
import { getRelativeConnections } from "../../logic/helpers/movables/getRelativeConnections";
import { makeOrderedConnections } from "../../logic/helpers/movables/makeOrderedConnections";
import { axes } from "@immugio/controls/esm/utils/axes";
import { getPrimaryPSU } from "../../logic/helpers/movables/getPrimaryPSU";

// Manages fixing types graphics based on user selection
export class FixingTypeControl extends CanvasControl {
    private currentTemplate: IMovableTemplate;
    protected defaultTemplate: IMovableTemplate;


    public static $inject = ["StateService", "settings"]
    private hidden = false;

    constructor(private state: StateService<ISvkServerData>, private settings: IApplicationSettings) {
        super();

        MessageHub.subscribe(Events.fixingTypeChanged, (type:string) => this.updateFixingType(type));
        MessageHub.subscribe(Events.powerSourceUpdated, () => this.updateModels());

        this.defaultTemplate = this.getFixingTypeTemplate('A05707');
    }

    public update(s: IApplicationState): void {
        // Are we in power view/page? 
        if (!s.view.isActionAvailable(AvailableActions.Power) || s.view.activeItem?.interactionType & (InteractionType.DraggingItem | InteractionType.InsertingItem)) {
            if (!this.hidden) {    
                this.hidden = true;
                this.showChildren(false);
            }
            return;
        }

        if(this.hidden) {
            this.hidden = false;
            this.showChildren(true);
        }

        super.update(s);
    }

    protected showChildren(show: boolean) {
        this.object3D.visible = show;
    }

    protected getFixingTypeTemplate(type) {
        return this.settings.movables.find(x => x.type == type);
    }

    protected updateFixingType(type: string) {
        const typeMap = {
            'I06946-FIX-C': 'A05707',
            'I06949-FIX-E': 'A05706',
            'PLAFONE': 'A05664'
        }

        if(!typeMap.hasOwnProperty(type)) {
            console.warn(`Fixing type ${type} is not supported.`);
            return;
        }
        
        this.currentTemplate = this.getFixingTypeTemplate(typeMap[type]);
        this.updateModels();
    }

    /**
     * Refreshes the 3D objects based on user selection
     */
    public updateModels(): void {
        this.clearChildren();

        if(!this.state.view.offset || !this.currentTemplate) return;

        this.object3D.position.x = -this.state.view.offset.x;
        this.object3D.position.z = -this.state.view.offset.y;

        const cables = SuspensionCableUtils.getSuspensionCables(this.state.design);
        
        cables
        .filter(x => x.movable.data.powerSource?.length)
        .forEach(x => {
            this.currentTemplate.type == 'A05664' ?  
            this.addTypeF(x, this.currentTemplate) :
            this.addFixingType(x, this.currentTemplate)
        });

        cables
        .filter(x => !x.movable.data.powerSource?.length)
        .forEach(x => this.addFixingType(x, this.defaultTemplate));
    }

    private async loadModel(template: IMovableTemplate){
        const graphicsPath = template.skin?.data?.finishes[0];

        if(!graphicsPath) {
            console.warn('Graphics for current fixing type does not exist.');
            return;
        }

        let model: Object3D = null;

        try {
            model = await ModelLoadUtils.load(graphicsPath);
        } catch(ex) {
            console.warn(ex);
        }

        return model;
    }
    
    /**
     * Adds fixing type F graphics (canopy)
     */
    private async addTypeF(cable: ISuspensionCableEx, template: IMovableTemplate) {
        // Add the default type
        await this.addFixingType(cable, this.defaultTemplate);
        const model = await this.loadModel(template);
        
        const primaryPSU = getPrimaryPSU(this.state.design);
        const relativeConnections = getRelativeConnections(this.state.design).filter(x => !x.movable.parentId);
        const orderedConnections = makeOrderedConnections(relativeConnections, primaryPSU);

        if(orderedConnections.length == 0) {
            // Should not happen
            console.warn('There are zero connections in the configuration.');
            return;
        }

        let target = orderedConnections[0];

        // Find the joint index in connections
        let movableIndex = orderedConnections.findIndex(m=>m == cable.movable);
    
        // Find next/prev connected movable
        if(orderedConnections.length > 1){
            if(movableIndex == orderedConnections.length - 1){
                movableIndex--;
            } else {
                movableIndex++;
            }
        }

        target = orderedConnections[movableIndex]

        model.position.copy(cable.movable as any);
        model.position.y = cable.center.y + cable.height * 0.5;
        model.quaternion.setFromAxisAngle(axes.y, target.rotationY * MathUtils.DEG2RAD);

        const moveDir = new Vector3().subVectors(target as any, cable.movable as any).setY(0);
        moveDir.normalize();
        model.position.addScaledVector(moveDir, template.depth * 0.5 + this.defaultTemplate.depth * 0.5);
        
        this.object3D.add(model);
    }

    /**
     * Adds fixing type graphics based on current user selection
     */
    private async addFixingType(cable: ISuspensionCableEx, template: IMovableTemplate) {
        const model = await this.loadModel(template);

        if(!model) return;

        model.position.copy(cable.center);
        model.position.y = model.position.y + cable.height * 0.5;

        this.object3D.add(model);
    }
}