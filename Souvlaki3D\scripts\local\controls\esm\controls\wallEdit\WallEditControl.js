import { Vec2, Vec3 } from "@immugio/three-math-extensions";
import { CircleGeometry, LineBasicMaterial, Mesh, MeshBasicMaterial, PlaneGeometry } from "three";
import { Pencil } from "../../elements/Pencil";
import { intersectObjects } from "../../utils/intersectObjects";
import { CanvasController } from "../../utils/CanvasController";
import { BaseControl, CenterControl, CornerControl } from "../BaseControl";
import { Wall } from "../../core/Wall";
import { WallHelper2D } from "../../graphics/WallHelper2D";
import { Interaction } from "../../objects/Interaction";
import { snapDirection, snapDirections, snapToGrid, snapWalls } from "../../utils/SnappingUtils";
import { Container3D } from "../../objects/Container3D";
export class WallEditControl {
    options;
    context;
    state = {
        isAddingWall: false,
        start: null,
        end: null,
        lastMouse: new Vec2(),
        dragControls: []
    };
    get position() {
        return this.root.position;
    }
    canvasController;
    root = new Container3D();
    defaultY;
    snapGridSpacing = 100;
    snapToGridMaxDistance = 50;
    snapToDirectionMaxDistance = 200;
    snapToWallEndpointMaxDistance = 150;
    plane = new Mesh(new PlaneGeometry(100000, 100000).rotateX(-Math.PI / 2), new MeshBasicMaterial({ transparent: true, opacity: 0, wireframe: true, visible: false }));
    wallLineMaterial = new LineBasicMaterial({ color: "red", depthTest: false });
    ballMaterial = new MeshBasicMaterial({ color: "blue" });
    ballGeometry = new CircleGeometry(20);
    ball1 = new Mesh(this.ballGeometry, this.ballMaterial);
    ball2 = new Mesh(this.ballGeometry, this.ballMaterial);
    pencil = Pencil();
    newWallObject3D;
    walls = [];
    wallSet;
    previousWallSet;
    snapPoints;
    constructor(options, context, wallSet) {
        this.options = options;
        this.context = context;
        this.ball1.rotation.x = -Math.PI / 2;
        this.ball2.rotation.x = -Math.PI / 2;
        context.scene.add(this.root);
        options.position && this.root.position.copy(options.position);
        this.root.add(this.plane, this.ball1, this.ball2, this.pencil);
        if (options.dragPlaneUserData) {
            Object.entries(options.dragPlaneUserData).forEach(([key, value]) => this.plane.userData[key] = value);
        }
        this.canvasController = new CanvasController(this.context, options?.disableOrbitControls);
        context.renderer.domElement.addEventListener("mousedown", () => this.onMouseDown());
        context.renderer.domElement.addEventListener("mousemove", e => this.onMouseMove(e));
        context.renderer.domElement.addEventListener("mouseup", () => this.onMouseUp());
        this.setWalls(wallSet);
    }
    setWalls(wallSet) {
        this.wallSet = wallSet;
        this.previousWallSet = wallSet.clone();
        this.defaultY = wallSet.walls[0]?.bottomY || 0;
        this.plane.position.y = this.defaultY;
        this.toggleIsAddingWall(false);
        this.toggleDragControls(true);
        this.showWalls();
        this.setSnapPoints();
    }
    toggleIsAddingWall(force) {
        this.state.isAddingWall = force ?? !this.state.isAddingWall;
        this.state.start = this.state.end = null;
        this.pencil.visible = this.state.isAddingWall;
        this.ball1.visible = this.ball2.visible = this.state.isAddingWall;
        this.toggleDragControls(!this.state.isAddingWall);
    }
    toggleDragControls(show) {
        this.root.remove(...this.state.dragControls.map(c => c.root));
        this.state.dragControls = [];
        if (show) {
            const centers = this.wallSet.getCenterPoints();
            const corners = this.wallSet.getCornerPoints();
            const controls = [
                ...centers.map((centerPoint) => {
                    const initialPosition = new Vec3(centerPoint.centerPoint.x, centerPoint.wall.bottomY, centerPoint.centerPoint.y);
                    return new CenterControl(centerPoint, initialPosition, e => this.onCenterDragged(e));
                }),
                ...corners.map((cornerPoint) => {
                    const initialPosition = new Vec3(cornerPoint.cornerPoint.x, cornerPoint.attachedWalls[0].wall.bottomY, cornerPoint.cornerPoint.y);
                    return new CornerControl(cornerPoint, initialPosition, e => this.onCornerDragged(e));
                }),
            ];
            this.state.dragControls = controls;
            this.root.add(...controls.map(c => c.root));
        }
    }
    hideOtherControls(current) {
        const toRemove = this.state.dragControls.filter(c => c !== current);
        this.root.remove(...toRemove.map(c => c.root));
        this.state.dragControls = [current];
    }
    onCornerDragged(control) {
        if (!snapWalls(control.position, this.snapPoints, this.snapToWallEndpointMaxDistance, control.initialPosition)) {
            snapDirections(this.snapPoints, control.position, this.snapToDirectionMaxDistance, control.initialPosition);
            snapToGrid(control.position, this.snapGridSpacing, this.snapToGridMaxDistance);
        }
        this.wallSet.moveCornerPoint(control.wallCornerPoint, Vec3.fromPoint(control.position).onPlan());
        this.showWalls();
        this.reportChange();
        this.hideOtherControls(control);
    }
    onCenterDragged(control) {
        this.wallSet.moveWallCenterPoint(control.wallCenterPoint, Vec3.fromPoint(control.position).onPlan());
        this.showWalls();
        this.reportChange();
        this.hideOtherControls(control);
    }
    onMouseDown() {
        if (this.state.isAddingWall) {
            if (!this.state.start) { // Set both wall start & end to mouse pos
                this.state.start = Vec3.fromPoint(this.pencil.position);
            }
            else if (!this.state.end) { // Set end to mouse pos, start stays in a previously set pos
                this.state.end = Vec3.fromPoint(this.pencil.position);
                this.wallSet.addWall(this.wallDataFromPoints(this.state.start, this.state.end));
                this.wallSet.normalize();
                this.showWalls();
                this.setSnapPoints();
                this.state.start = this.state.end = null;
                this.options.onChange?.(this.wallSet);
            }
        }
    }
    onMouseMove(e) {
        const mousePosition = new Vec2(e.clientX, e.clientY);
        if (!mousePosition.equals(this.state.lastMouse)) {
            this.state.lastMouse = mousePosition;
            const movingPoint = this.getScenePos(mousePosition);
            if (movingPoint) {
                if (this.state.isAddingWall) {
                    if (!this.state.end && this.state.start) {
                        if (!snapWalls(movingPoint, this.snapPoints, this.snapToWallEndpointMaxDistance, this.state.start)) {
                            if (!snapDirection(this.state.start, movingPoint, this.snapToDirectionMaxDistance)) {
                                snapToGrid(movingPoint, this.snapGridSpacing, this.snapToGridMaxDistance);
                            }
                        }
                    }
                    else if (!this.state.end) {
                        if (!snapWalls(movingPoint, this.snapPoints, this.snapToWallEndpointMaxDistance)) {
                            if (!snapDirections(this.snapPoints, movingPoint, this.snapToDirectionMaxDistance)) {
                                snapToGrid(movingPoint, this.snapGridSpacing, this.snapToGridMaxDistance);
                            }
                        }
                    }
                    this.pencil.position.copy(movingPoint);
                    this.showCurrentWall(this.state.start || movingPoint, this.state.end || movingPoint);
                }
            }
        }
    }
    showCurrentWall(p1, p2) {
        if (!p1 || !p2 || (p1.equals(this.ball1.position) && p2.equals(this.ball2.position))) {
            return;
        }
        this.ball1.position.copy(p1);
        this.ball2.position.copy(p2);
        this.root.remove(this.newWallObject3D);
        if (p1.equals(p2)) {
            return;
        }
        const newWall = this.wallDataFromPoints(p1, p2);
        this.newWallObject3D = WallHelper2D.getWall(newWall, null, null, this.wallLineMaterial);
        this.options?.onChange?.(this.previousWallSet.clone().addWall(newWall));
        this.root.add(this.newWallObject3D);
    }
    showWalls() {
        this.root.remove(...this.walls);
        const walls = WallHelper2D.render(this.wallSet, this.wallLineMaterial);
        const controls = walls.map((mesh, i) => {
            const interaction = new Interaction(this.wallSet.walls[i]);
            return new BaseControl({
                content: mesh,
                onMouseDown: (control, e) => !this.state.isAddingWall && this.options?.onWallClicked?.(this.wallSet, control.interaction.options, e),
                interaction,
            });
        });
        this.walls = controls.map(x => x.root);
        this.root.add(...this.walls);
    }
    wallDataFromPoints(p1, p2) {
        return new Wall([p1.onPlan(), p2.onPlan()], this.options.defaultWallHeight, this.options.defaultWallHeight, this.defaultY, this.options.defaultWallThickness, null, null, null);
    }
    getScenePos(canvasPoint) {
        const intersections = intersectObjects(this.context.renderer.domElement, this.context.camera, canvasPoint, [this.plane]);
        const point = intersections[0]?.point;
        if (point) {
            point.sub(this.root.position);
        }
        return Vec3.fromPoint(point);
    }
    setSnapPoints() {
        this.snapPoints = this.wallSet.walls.map(w => [w.startPointInSpace.setY(this.defaultY), w.endPointInSpace.setY(this.defaultY)]).flat();
    }
    getWallData() {
        return this.wallSet.clone();
    }
    reportChange() {
        if (!this.wallSet.equals(this.previousWallSet)) {
            this.previousWallSet = this.wallSet.clone();
            this.options?.onChange?.(this.previousWallSet);
        }
    }
    onMouseUp() {
        this.wallSet.normalize();
        this.setSnapPoints();
        this.options?.onStep?.(this.wallSet);
        this.toggleDragControls(!this.state.isAddingWall);
    }
    dispose() {
        this.root.clear();
        this.root.parent.remove(this.root);
        this.context.canvas.removeEventListener("mousedown", this.onMouseDown);
        this.context.canvas.removeEventListener("mousemove", this.onMouseMove);
        this.context.canvas.removeEventListener("mouseup", this.onMouseUp);
        this.canvasController.dispose();
    }
}
