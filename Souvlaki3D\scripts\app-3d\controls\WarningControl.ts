import { CanvasControl } from "./CanvasControl";
import { IApplicationState } from "../../models/state/IApplicationState";
import { Mesh, BoxGeometry, MeshBasicMaterial } from "three";
import { EdgesUtils } from "../utils/EdgesUtils";
import { InteractionType } from "../../models/common/Enums";
import { settings } from "../../configurations/setup_global";

export class WarningControl extends CanvasControl {

    private obj: Mesh;

    private red = new MeshBasicMaterial({ color: "red", transparent: true, opacity: 0.2, depthTest: false });
    private invisible = new MeshBasicMaterial({ color: "green", transparent: true, opacity: 0 });

    public update(s: IApplicationState): void {

        if (s.diff.structureChanged || s.diff.maxSuspensionChanged) {

            if (this.obj) {
                this.object3D.remove(this.obj);
            }

            const over = (s.design.height - s.design.baseHeight - s.design.roofWidth) - s.view.maxSuspension - 50;

            if (over > 0) {
                const depth = s.design.extDepth() - s.design.wallThickness * 2;
                const width = s.design.extWidth() - s.design.wallThickness * 2;

                this.obj = new Mesh(
                    new BoxGeometry(width, over, depth),
                    [
                        this.red,
                        this.red,
                        this.red,
                        this.invisible,
                        this.red,
                        this.red,
                    ]
                );

                const e = EdgesUtils.getEdges(this.obj.geometry, "#000000");
                this.obj.add(e);

                this.obj.position.y = s.design.baseHeight + over / 2;

                this.object3D.add(this.obj);
            }
        }

        if (this.obj) {
            this.obj.visible = (s.view.activeItem?.interactionType !== InteractionType.ResizeBuilding) && !s.view.isExporting && settings.app.configuratorId === 2;
        }
    }
}