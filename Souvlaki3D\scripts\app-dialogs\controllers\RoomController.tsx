import { MessageHub } from "../../infrastructure/MessageHub";
import { Events } from "../../models/infrastructure/Events";
import { DesignState } from "../../models/state/DesignState";
import { trySetRoomSize } from "../../state/actions/trySetRoomSize";

export class RoomController {

    private roomwidth : HTMLInputElement;
    private roomheight: HTMLInputElement;
    private roomdepth : HTMLInputElement;

    constructor() {
        this.roomwidth = document.querySelector("#roomwidth");
        this.roomwidth.onchange = () => trySetRoomSize(parseInt(this.roomwidth.value), null, null, true);

        this.roomdepth = document.querySelector("#roomdepth");
        this.roomdepth.onchange = () => trySetRoomSize(null, parseInt(this.roomdepth.value), null, true);

        this.roomheight = document.querySelector("#roomheight");
        this.roomheight.onchange = () => trySetRoomSize(null, null, parseInt(this.roomheight.value), true);

        MessageHub.subscribe([Events.pushState, Events.setState, Events.dragResize], (d: DesignState) => this.setValuesFromDesign(d));
    }

    private setValuesFromDesign(d: DesignState): void {
        this.roomwidth.value = d.extWidth().toString();
        this.roomdepth.value = d.extDepth().toString();
        this.roomheight.value = d.height.toString();
    }
}