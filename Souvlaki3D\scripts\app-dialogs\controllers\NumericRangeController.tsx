import { UnitType } from "../../models/common/Enums";

export class NumericRangeController {

    private regex = /^\d*$/;
    private unitType: UnitType;
    private lastValidNumberMm: number;

    constructor(private range: HTMLInputElement, private input: HTMLInputElement, private unitElement: HTMLElement, valueMM: number, minMm: number, maxMm: number, unitType: UnitType, onChange: (valueMm: number) => void) {

        this.setUnitType(unitType);
        this.setValues(minMm, maxMm, valueMM);

        range.addEventListener("input", () => {
            this.lastValidNumberMm = parseInt(range.value);
            input.value = this.getDisplayValue(this.lastValidNumberMm);
            onChange(this.lastValidNumberMm);
        });

        input.addEventListener("keyup", () => {
            if (input.value.match(this.regex)) {
                const current = this.readValue(parseInt(input.value));
                if (current >= minMm && current <= maxMm) {
                    this.lastValidNumberMm = current;
                    range.value = current.toString();
                    onChange(current);
                }
            } else {
                input.value = this.lastValidNumberMm.toString();
            }
        });
    }

    public setUnitType(unitType: UnitType): void {
        this.unitType = unitType;
        this.input.value = this.getDisplayValue(this.lastValidNumberMm);
        this.unitElement.innerText = unitType === UnitType.Metric ? "cm" : "inches";
    }

    public setValues(minMm: number, maxMm: number, valueMm: number): void {
        this.range.min = minMm.toString();
        this.range.max = maxMm.toString();
        this.range.value = valueMm.toString();
        this.lastValidNumberMm = valueMm;
        this.input.value = this.getDisplayValue(valueMm);
    }

    private getDisplayValue(mm: number): string {
        const value = this.unitType === UnitType.Metric
            ? mm / 10
            : mm / 25.4;

        return Math.round(value).toString();
    }

    private readValue(n: number): number {
        return this.unitType === UnitType.Metric
            ? n * 10
            : Math.round(n * 25.4);
    }
}