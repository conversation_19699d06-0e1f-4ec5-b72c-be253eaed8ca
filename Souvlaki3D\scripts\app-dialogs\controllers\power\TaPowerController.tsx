import { BaseController } from "../BaseController";
import { MessageHub } from "../../../infrastructure/MessageHub";
import { StateService } from "../../../state/StateService";
import { Events } from "../../../models/infrastructure/Events";
import { IInteractionOptions } from "../../../models/positioning/IInteractionOptions";
import { MovableItem } from "../../../models/state/MovableItem";
import { PowerLogic } from "../../../logic/PowerLogic";
import { IPowerOptions } from "../../../models/state/IPowerOptions";
import { ITapInfo } from "../../../models/menu/ITapInfo";
import { MovablesLogic } from "../../../logic/MovablesLogic";
import { PositioningUtils } from "../../../logic/helpers/PositioningUtils";
import { IPowerInteractionOptions } from "../../../models/events/IPowerInteractionOptions";
import { TaPowerInfo } from "../../components/TaPowerInfo";
import { PlacementMode } from "../../../models/common/Enums";
import { dom } from "@immugio/jsx-and-html-utils";
import { getPowerGroup } from "../../../logic/helpers/power/getPowerGroup";
import { getChildrenWithinDistanceToPoint } from "../../../logic/helpers/movables/getChildrenWithinDistanceToPoint";
import { getLedStripsPassingWithinDistanceToPoint } from "../../../logic/helpers/movables/getLedStripsPassingWithinDistanceToPoint";
import { getTags } from "../../../logic/helpers/power/getTags";
import { IPowerChoice } from "../../../models/state/IPowerChoice";
import { removeMovables } from "../../../logic/helpers/movables/removeMovables";
import { Modal } from "../../services/Modal";

export class TaPowerController extends BaseController {

    public static $inject = ["PowerLogic", "MovablesLogic", "MessageHub", "StateService"];
    private attachmentCollisionDistance = 120;
    private dialog: HTMLElement;

    // TODO: This was moved from Gui2 so that I could delete it, if this is going to be used, it should be set somewhere
    private powerChoice: IPowerChoice = { // Contains the power selections: undimmable, dimmable etc.
        appControlled: null,
        capacity: null,
        installation: null,
        color: null,
        dimmable: null,
        movable: null,
        index: null,
        connected: null,
        control: null,
    }

    constructor(private powerLogic: PowerLogic, private movablesLogic: MovablesLogic, private messageHub: MessageHub, private state: StateService) {
        super();
        this.initialize();
    }

    private async initialize(): Promise<void> {
        this.messageHub.subscribe(Events.powerReset, () => this.clearAll());
        this.messageHub.subscribe<IInteractionOptions>(Events.optionsSelected, info => this.onSupplySelected(info));
        this.messageHub.subscribe<ITapInfo>(Events.itemSelected, info => this.onItemSelected(info));
        this.messageHub.subscribe<IPowerInteractionOptions>(Events.powerSourceAdded, o => this.powerSourceAdded(o));
        this.messageHub.subscribe(Events.powerSourcesNeedUpdate, () => this.setColors());
    }

    private reset(): void {
        const m = this.state.view.selectedPowerSupply.movable;
        m.data = { ...m.data, powerSource: [] };
        this.state.view.selectedPowerSupply = null;
        this.powerLogic.setColors(this.state.design, null, null);
        this.powerLogic.updatePowerIds(this.state.design.movableItems);
        this.togglePowerDg(null, null);
    }

    private onItemSelected(info: ITapInfo): void {
        const toConnect = info.interaction.movable;
        const main = this.state.view.selectedPowerSupply?.movable;
        const powerSourceIndex = this.state.view.selectedPowerSupply?.index;

        if (!toConnect || !main) {
            return;
        }

        toConnect.selected = false;

        const toggled = this.powerLogic.toggleItem(this.state.design, main, powerSourceIndex, toConnect, info.interaction.partId);
        if (toggled) {
            this.powerLogic.setColors(this.state.design, main, powerSourceIndex);
            this.togglePowerDg(main, powerSourceIndex);

        } else if (toggled === null) { // Can't connect due to insufficient power
            const snaps = PositioningUtils.getMovableSnaps(this.state.design.movableItems);
            const allowed = this.powerLogic.getAllowedPoints(toConnect, snaps);

            if (allowed.points.length > 0) { // Zero points typically means that the element is not allowed to have its own power sources
                const index = toConnect.snaps.indexOf(allowed.points[0]);
                const connected = (this.movablesLogic.getConnectedItems(this.state.design.movableItems, toConnect) || []).map(x => x.movable);
                this.confirmSource(toConnect, index, connected);
            }
        }
    }

    private onSupplySelected(info: IInteractionOptions): void {
        const m = info.movable, d = this.state.design;
        const powerSource = m.data.powerSource.find(x => x.sideIndex === info.lineIndex);

        if (!powerSource && this.powerLogic.isPowered(d, m, info.partId)) {
            // Item is connected to another power source already
            return;
        }

        const snaps = PositioningUtils.getMovableSnaps([m]);
        const collidingAttachments = [
            ...getChildrenWithinDistanceToPoint(m, this.state.design.movableItems, snaps[0].absoluteSnaps[info.lineIndex], this.attachmentCollisionDistance),
            ...getLedStripsPassingWithinDistanceToPoint(this.state.design.movableItems, snaps[0].absoluteSnaps[info.lineIndex], this.attachmentCollisionDistance),
        ];
        if (collidingAttachments.length) {
            Modal.confirm("Colliding appliance", "In the chosen position there is no space for both the power supply and "
                + "the existing lighting appliance. Do you want to delete the lighting appliance?", confirmed => {
                if (confirmed) {
                    removeMovables(this.state.design, collidingAttachments);
                    this.addFirstOrConfirmAdditionalPowerSource(info);
                }
                // Else return and do nothing
            }, "Delete", "Keep");
        } else {
            this.addFirstOrConfirmAdditionalPowerSource(info);
        }
    }

    private addFirstOrConfirmAdditionalPowerSource(info: IInteractionOptions): void {
        const m = info.movable, d = this.state.design;
        const powerSource = m.data.powerSource.find(x => x.sideIndex === info.lineIndex);
        if (powerSource) { // Item is a power source with params set
            this.selectGroup(m, powerSource.sideIndex);
            this.state.view.selectedPowerSupply = { movable: m, index: powerSource.sideIndex };

        } else { // New item to become power source, params to be selected
            const isFirst = d.movableItems.every(x => !x.data.powerSource.length);
            const connected = (this.movablesLogic.getConnectedItems(d.movableItems, m) || []).map(x => x.movable);

            if (isFirst) {
                const powerChoice = {...this.powerChoice};
                powerChoice.control = null;
                powerChoice.installation = null;
                powerChoice.color = null;
                powerChoice.capacity = null;
                powerChoice.movable = m;
                powerChoice.index = info.lineIndex;
                yada.ajax("TODO: actual url", powerChoice, null, "POST", null, () => console.log("TODO: Hide loader"), true);

            } else {
                this.confirmSource(m, info.lineIndex, connected);
            }
        }
    }

    private confirmSource(m: MovableItem, index: number, connected: MovableItem[]): void {
        Modal.confirm(null, "Do you want to add another power supply?", r => {
            if (r) {
                console.log("TODO: openPowerSelect", m, index, connected);
            }
        }, "Ok", "Cancel");
    }

    private powerSourceAdded(o: IPowerInteractionOptions): void {
        this.powerLogic.addPowerSource(this.state.design, o.selection, o.movable, o.index);
        this.state.view.selectedPowerSupply = { movable: o.movable, index: o.index };
        this.selectGroup(o.movable, o.index);
    }

    private selectGroup(m: MovableItem, index: number): void {
        this.togglePowerDg(m, index);
        this.powerLogic.setColors(this.state.design, m, index);
    }

    private togglePowerDg(m: MovableItem, index: number) {
        this.dialog?.remove();
        if (m) {
            const ps = m.data.powerSource.find(x => x.sideIndex === index);
            if (ps) {
                const currentLoad = this.powerLogic.groupConsumption(this.state.design.movableItems, m, index);
                const left = Math.round((ps.effective - currentLoad) * 10) / 10;
                const canAdd = this.powerLogic.hasPowerForNeighbour(this.state.design, m, index);
                const warn = canAdd ? "" : "error-color";
                this.dialog = document.body.appendChild(
                    <TaPowerInfo
                        powerSourceId={ps.powerSourceId}
                        warn={warn} remainingWats={left}
                        remainingChannels={this.getRemainingChannels(ps, m)}
                        tags={getTags(ps)}
                        onReset={() => this.reset()}/>
                );
            }
        }
    }

    private getRemainingChannels(ps: IPowerOptions, main: MovableItem): number {
        if (ps.control !== "DALI") {
            return null;
        }

        const initial = 64;
        const group = getPowerGroup(this.state.design.movableItems, main, ps.sideIndex);
        const children = group.filter(x => x.item.placementMode === PlacementMode.Attachment);
        return initial - children.length;
    }

    private setColors(): void {
        this.powerLogic.setPowerFrom(this.state.design.movableItems);
        this.powerLogic.setColors(this.state.design, null, null);
    }

    /**
     * Remove "invalid" color tag/color from elements and close power select
     */
    private clearAll(): void {
        this.state.view.selectedPowerSupply = null;
        this.state.design.movableItems.forEach(x => {
            x.data = { ...x.data, color: null, invalid: false };
            x.selected = false;
        });
        this.togglePowerDg(null, null);
    }
}