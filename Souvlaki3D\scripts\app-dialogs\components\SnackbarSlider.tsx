import { AnyContent, dom, isElementChildOf } from "@immugio/jsx-and-html-utils";

interface IProps {
    min: number;
    max: number;
    step: number;
    value: number;
    header: AnyContent;
    roundDisplayValue?: boolean;
    readOnly?: boolean;
    onChange: (value: number) => void;
    onClose?: () => void;
}

export interface ISlider extends HTMLElement {

    close?: () => void;
    setValue?: (val: string) => void;
}

const pattern = /^\d*$/;

export function SnackbarSlider({min, max, step, value, header, onClose, onChange, roundDisplayValue, readOnly}: IProps): ISlider {

    let modal: ISlider;
    let slider: HTMLInputElement;
    let input: HTMLInputElement;

    function inputChange() {
        if (input.value.match(pattern)) {
            const value = parseInt(input.value);
            if (value >= min && value <= max) {
                slider.value = value.toString();
                onChange(value);
            }
        }
    }

    function setValue(val: string): void {
        input.value = val;
        slider.value = val;
    }

    function loaded(e: HTMLElement) {
        modal = e;
        modal.close = close;
        modal.setValue = setValue;
        document.body.appendChild(modal);
        setTimeout(() => modal.classList.add("show"), 100);
    }

    function close() {
        modal.classList.remove("show");
        setTimeout(() => modal.remove(), 1000);
        if (onClose) {
            onClose();
        }
    }

    function getDisplayValue(value: number) {
        return (roundDisplayValue ? Math.round(value) : value).toString();
    }

    function sliderChange() {
        const value = parseInt(slider.value);
        onChange(value);
        input.value = getDisplayValue(value);
    }

    setTimeout(function() {
        window.onmousedown = function(e) {
            if (!isElementChildOf(e.target, modal)) {
                close();
            }
        };
    }, 500);

    return (
        <div ref={loaded} className="snackbar with-slider">
            <div>
                <div>
                    <span>{header}:</span>
                    <input ref={e => input = e} value={getDisplayValue(value)} type="text" oninput={inputChange} readOnly={readOnly} />
                </div>
                <input ref={e => slider = e} min={min} max={max} step={step} value={value} type="range" className="slider" oninput={sliderChange} />
                <button type="button" className="close" onclick={close}>×</button>
            </div>
        </div>
    );
}