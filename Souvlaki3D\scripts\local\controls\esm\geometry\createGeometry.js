import { BufferAttribute, BufferGeometry } from "three";
export function createGeometry(position, uv, faces, groups = []) {
    const geo = new BufferGeometry();
    geo.setAttribute("position", new BufferAttribute(new Float32Array(position), 3));
    geo.setAttribute("uv", new BufferAttribute(new Float32Array(uv), 2));
    geo.setAttribute("uv2", new BufferAttribute(new Float32Array(uv), 2));
    geo.setIndex(faces);
    groups.forEach(group => geo.addGroup(group.start, group.count, group.materialIndex));
    geo.computeVertexNormals();
    return geo;
}
