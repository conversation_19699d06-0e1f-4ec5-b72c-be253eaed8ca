﻿import { CanvasControl } from "./CanvasControl";
import { IApplicationState } from "../../models/state/IApplicationState";
import { AmbientLight, DirectionalLight, DirectionalLightHelper } from "three";
import { settings } from "../../configurations/setup_global";

export class LightsControl extends CanvasControl {

    private readonly directionalLight: DirectionalLight;
    private readonly directionalLightBack: DirectionalLight;

    private helper: DirectionalLightHelper;
    private helperBack: DirectionalLightHelper;

    constructor() {
        super();

        const ambientLight = new AmbientLight(settings.view.ambientLightColor, settings.view.ambientLightIntensity);
        this.object3D.add(ambientLight);

        this.directionalLight = new DirectionalLight(settings.view.directionalLightColor, settings.view.directionalLightIntensity);
        this.directionalLight.position.set(2000, 5000, 800);
        this.object3D.add(this.directionalLight);
        // this.helper = new DirectionalLightHelper(this.directionalLight, 500, "red");
        // this.object3D.add(this.helper);

        this.directionalLightBack = new DirectionalLight(settings.view.directionalLightColor, settings.view.directionalLightIntensity);
        this.directionalLightBack.position.set(-2000, 5000, -800);
        this.object3D.add(this.directionalLightBack);
        // this.helperBack = new DirectionalLightHelper(this.directionalLightBack, 500, "red");
        // this.object3D.add(this.helperBack);
    }

    public update(s: IApplicationState): void {

        // TODO: Always rotate with camera?

        const ds = 5000;
        const dir = s.view.phi < 0 ? -ds : ds;

        this.directionalLight.position.y = this.directionalLightBack.position.y = dir;

        // this.helper.update();
        // this.helperBack.update();
    }
}