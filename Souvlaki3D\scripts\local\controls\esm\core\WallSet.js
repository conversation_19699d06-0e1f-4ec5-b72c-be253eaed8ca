import { Line2D, Vec2 } from "@immugio/three-math-extensions";
export class WallSet {
    walls;
    neighborTolerance = .1;
    constructor(walls = []) {
        this.walls = walls;
    }
    addWall(wall) {
        this.walls.push(wall);
        return this;
    }
    removeWall(id) {
        this.walls.splice(this.walls.findIndex(x => x.id === id), 1);
        return this;
    }
    next(current) {
        return this.walls.find(next => next !== current &&
            (next.startPointOnPlan.isNear(current.endPointOnPlan, this.neighborTolerance) || next.endPointOnPlan.isNear(current.endPointOnPlan, this.neighborTolerance)));
    }
    previous(current) {
        return this.walls.find(previous => previous !== current &&
            (previous.endPointOnPlan.isNear(current.startPointOnPlan, this.neighborTolerance) || previous.startPointOnPlan.isNear(current.startPointOnPlan, this.neighborTolerance)));
    }
    getCenterPoints() {
        return this.walls.map(wall => ({
            centerPoint: wall.wallLineOnPlan.center,
            wall
        }));
    }
    normalize() {
        this.removeZeroLengthAndDuplicateWalls();
        this.normalizeWallDirections();
    }
    normalizeWallDirections() {
        const connectedSections = this.getConnectedSections();
        const allSortedWalls = [];
        for (const section of connectedSections) {
            const sorted = this.normalizeSectionDirections(section);
            allSortedWalls.push(...sorted);
        }
        // Possible improvement: The walls could be ordered so that the array is sorted by the order and connections of the walls in the plan.
        this.walls = allSortedWalls;
    }
    removeZeroLengthAndDuplicateWalls() {
        const nonZeroLengthWalls = this.walls.filter(wall => wall.wallLineOnPlan.length > this.neighborTolerance);
        let duplicateWall = nonZeroLengthWalls.find(wall => nonZeroLengthWalls.some(other => other !== wall && other.wallLineOnPlan.equals(wall.wallLineOnPlan)));
        while (duplicateWall) {
            nonZeroLengthWalls.splice(nonZeroLengthWalls.indexOf(duplicateWall), 1);
            duplicateWall = nonZeroLengthWalls.find(wall => nonZeroLengthWalls.some(other => other !== wall && other.wallLineOnPlan.equals(wall.wallLineOnPlan)));
        }
        this.walls = nonZeroLengthWalls;
    }
    normalizeSectionDirections(wallSegments) {
        const sortedSegments = [];
        const remainingSegments = [...wallSegments];
        sortedSegments.push(remainingSegments.shift());
        while (remainingSegments.length > 0) {
            const candidateSegment = remainingSegments.shift();
            for (let j = sortedSegments.length - 1; j >= 0; j--) {
                const lastSortedSegment = sortedSegments[j];
                if (lastSortedSegment.endPointOnPlan.isNear(candidateSegment.startPointOnPlan, this.neighborTolerance) ||
                    lastSortedSegment.startPointOnPlan.isNear(candidateSegment.endPointOnPlan, this.neighborTolerance)) {
                    sortedSegments.push(candidateSegment); // Connectable without flipping
                    break;
                }
                else if (lastSortedSegment.endPointOnPlan.isNear(candidateSegment.endPointOnPlan, this.neighborTolerance) ||
                    lastSortedSegment.startPointOnPlan.isNear(candidateSegment.startPointOnPlan, this.neighborTolerance)) {
                    candidateSegment.reverseDirection(); // Connectable with flipping
                    sortedSegments.push(candidateSegment);
                    break;
                }
            }
        }
        return sortedSegments;
    }
    getConnectedSections() {
        const connectedSections = [];
        for (const wall of this.walls) {
            let added = false;
            for (const section of connectedSections) {
                if (section.some(wallSection => wallSection.startPointOnPlan.isNear(wall.startPointOnPlan, this.neighborTolerance) ||
                    wallSection.endPointOnPlan.isNear(wall.startPointOnPlan, this.neighborTolerance) ||
                    wallSection.startPointOnPlan.isNear(wall.endPointOnPlan, this.neighborTolerance) ||
                    wallSection.endPointOnPlan.isNear(wall.endPointOnPlan, this.neighborTolerance))) {
                    section.push(wall);
                    added = true;
                    break;
                }
            }
            if (!added) {
                connectedSections.push([wall]);
            }
        }
        return connectedSections;
    }
    getCornerPoints() {
        const cornerPoints = [];
        for (const wall of this.walls) {
            const start = wall.startPointOnPlan;
            const end = wall.endPointOnPlan;
            const startCornerPoint = cornerPoints.find((cp) => cp.cornerPoint.equals(start));
            const endCornerPoint = cornerPoints.find((cp) => cp.cornerPoint.equals(end));
            if (startCornerPoint) {
                startCornerPoint.attachedWalls.push({ wall, point: start });
            }
            else {
                cornerPoints.push({ cornerPoint: start.clone(), attachedWalls: [{ wall, point: start }] });
            }
            if (endCornerPoint) {
                endCornerPoint.attachedWalls.push({ wall, point: end });
            }
            else {
                cornerPoints.push({ cornerPoint: end.clone(), attachedWalls: [{ wall, point: end }] });
            }
        }
        return cornerPoints;
    }
    moveCornerPoint(cornerPoint, newPosition) {
        cornerPoint.attachedWalls.forEach(w => w.point.copy(newPosition));
    }
    moveWallCenterPoint(wallCenterPoint, newPosition) {
        const wall = wallCenterPoint.wall;
        const wallLine = new Line2D(wall.startPointOnPlan, wall.endPointOnPlan);
        const otherWalls = this.getOtherWalls(wall);
        const affectedWalls = [];
        for (const wallEndpoint of wallLine.endpoints) {
            for (const otherWall of otherWalls) {
                if (wallEndpoint.isNear(otherWall.startPointOnPlan, 1)) {
                    affectedWalls.push({ otherWall: otherWall, otherWallEndpoint: otherWall.startPointOnPlan, wallEndpoint });
                }
                if (wallEndpoint.isNear(otherWall.endPointOnPlan, 1)) {
                    affectedWalls.push({ otherWall: otherWall, otherWallEndpoint: otherWall.endPointOnPlan, wallEndpoint });
                }
            }
        }
        const wallMove = Vec2.fromPoint(newPosition).sub(wallLine.center);
        wallLine.endpoints.forEach(ep => {
            ep.x += wallMove.x;
            ep.y += wallMove.y;
        });
        for (const affectedWall of affectedWalls) {
            const otherWallLine = affectedWall.otherWall.wallLineOnPlan;
            const intersection = wallLine.intersect(otherWallLine);
            if (intersection) {
                affectedWall.wallEndpoint.copy(intersection);
                affectedWall.otherWallEndpoint.copy(intersection);
            }
        }
    }
    getOtherWalls(wall) {
        return this.walls.filter(w => w.id !== wall.id);
    }
    traverse(callback) {
        return this.walls.map(wall => callback(wall, this.previous(wall), this.next(wall)));
    }
    equals(other) {
        return this.walls.length === other?.walls.length && this.walls.every((wall, index) => wall.equals(other.walls[index]));
    }
    clone() {
        return new WallSet(this.walls.map(wall => wall.clone()));
    }
}
