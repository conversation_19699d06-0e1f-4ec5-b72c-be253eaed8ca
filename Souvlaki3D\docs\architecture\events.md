# Event System

## Overview

The Souvlaki 3D Configuration Engine uses an event-driven architecture through the MessageHub to facilitate communication between components. This system enables loose coupling and efficient updates across the application.

## MessageHub

The MessageHub is a central event bus that manages:
- Event broadcasting
- Event subscription
- Event handling
- Type-safe message passing

## Event Types

### Core Events
```typescript
enum Events {
    // State Events
    setState = "setState",
    pushState = "pushState",
    
    // View Events
    zoomIn = "zoomIn",
    zoomOut = "zoomOut",
    updateViewMode = "updateViewMode",
    
    // Interaction Events
    startInserting = "startInserting",
    insertingStarted = "insertingStarted",
    insertingEnded = "insertingEnded",
    draggingStarted = "draggingStarted",
    dragResize = "dragResize",
    dragResizing = "dragResizing",
    requestDragEnd = "requestDragEnd",
    
    // Selection Events
    itemSelected = "itemSelected",
    optionsSelected = "optionsSelected",
    hoverItem = "hoverItem",
    
    // Drawing Events
    drawingInteraction = "drawingInteraction",
    
    // System Events
    engineReady = "engineReady"
}
```

## Event Flow

### 1. Event Broadcasting
```typescript
// Broadcast an event
this.messageHub.broadcast<DesignState>(Events.setState, newState);
this.messageHub.broadcast(Events.updateViewMode);
this.messageHub.broadcast<IInteractionOptions>(Events.insertingStarted, options);
```

### 2. Event Subscription
```typescript
// Subscribe to events
this.messageHub.subscribe(Events.zoomIn, () => this.zoom(1));
this.messageHub.subscribe<DesignState>(Events.setState, (state) => this.setState(state));
this.messageHub.subscribe<IMenuItem>(Events.startInserting, (menuItem) => this.startInserting(menuItem));
```

## Common Event Patterns

### 1. State Updates
```typescript
// Broadcast state change
MessageHub.broadcast<DesignState>(Events.setState, {
    // New state properties
});

// Handle state change
MessageHub.subscribe<DesignState>(Events.setState, (newState) => {
    this.state.initialize(newState);
    this.state.view.controlOptions = ResizingLogic.getResizeOptions(
        this.state.design, 
        this.state.view
    );
});
```

### 2. User Interactions
```typescript
// Start inserting items
MessageHub.broadcast<IMenuItem>(Events.startInserting, menuItem);

// Handle insertion
MessageHub.subscribe<IMenuItem>(Events.startInserting, (item) => {
    const m = MovablesUtils.getInstance(item.type);
    // Configure movable
    m.data.kelvin = item.kelvin;
    m.data.colorIta = item.colorIta;
    // ... additional setup
    this.addMovable(m);
});
```

### 3. View Updates
```typescript
// Update view mode
MessageHub.broadcast(Events.updateViewMode);

// Handle view update
MessageHub.subscribe(Events.updateViewMode, () => {
    if (this.state.view.displayMode & Display.PerspectiveView) {
        CanvasUtils.toPerspective(this.scene);
    } else {
        CanvasUtils.toOrtho(this.scene);
    }
});
```

## Event Handlers

### Core Event Handlers
```typescript
private initEventsHandlers(): void {
    // View controls
    this.messageHub.subscribe(Events.zoomIn, () => this.zoom(1));
    this.messageHub.subscribe(Events.zoomOut, () => this.zoom(-1));
    
    // Item manipulation
    this.messageHub.subscribe<IMenuItem>(
        Events.startInserting,
        (menuItem) => this.startInserting(menuItem)
    );
    
    // State management
    this.messageHub.subscribe<DesignState>(
        Events.setState,
        (d) => this.setState(d)
    );
    this.messageHub.subscribe<DesignState>(
        Events.pushState,
        d => this.pushState(d)
    );
    
    // View updates
    this.messageHub.subscribe(
        Events.updateViewMode,
        () => this.updateViewMode()
    );
}
```

## Event Lifecycle

### 1. Initialization
```typescript
constructor() {
    // Set up event handlers
    this.initEventsHandlers();
    
    // Broadcast initial state
    const design = MovablesUtils.getDesignStateFromTemplate(
        settings.buildingTemplates[0]
    );
    messageHub.broadcast<DesignState>(Events.setState, design);
    
    // Signal system ready
    messageHub.broadcast<DesignState>(Events.engineReady);
}
```

### 2. Runtime Events
```typescript
// Handle user interaction
private onTouch(e: HammerInput): void {
    // Process touch event
    const hovered = CanvasUtils.objectFromMouse(
        this.scene,
        e.center.x,
        e.center.y,
        this.state.view.allowedInteractions
    );
    
    // Broadcast interaction event
    this.messageHub.broadcast(
        Events.drawingInteraction,
        hovered.interaction
    );
}
```

### 3. Cleanup
```typescript
// Handle system shutdown
process.on('SIGINT', async () => {
    await this.server.close();
    process.exit(0);
});
```

## Best Practices

1. **Type Safety**
   - Use TypeScript generics for type-safe events
   - Define clear event interfaces
   - Validate event payloads

2. **Event Naming**
   - Use clear, descriptive event names
   - Follow consistent naming conventions
   - Document event purposes

3. **Performance**
   - Minimize event payload size
   - Unsubscribe from unused events
   - Avoid event loops

4. **Error Handling**
   - Handle event errors gracefully
   - Provide meaningful error messages
   - Maintain system stability

## Example: Complex Interaction Flow

```typescript
// 1. User initiates drag
this.messageHub.broadcast<IInteractionOptions>(
    Events.draggingStarted,
    this.state.view.activeItem
);

// 2. System updates state
this.messageHub.broadcast<DesignState>(
    Events.pushState,
    updatedState
);

// 3. UI updates
this.messageHub.broadcast(
    Events.drawingInteraction,
    this.state.view.activeItem
);

// 4. Operation completes
this.messageHub.broadcast(
    Events.requestDragEnd
);
```

## Debugging Events

1. **Event Logging**
```typescript
this.messageHub.subscribe(Events.setState, (state) => {
    console.log('State Update:', state);
    this.setState(state);
});
```

2. **Error Tracking**
```typescript
this.server.onerror = (error) => {
    console.error('[MCP Error]', error);
};
```

## Future Considerations

1. **Event Optimization**
   - Event batching
   - Priority queuing
   - Performance monitoring

2. **Enhanced Debugging**
   - Event timeline
   - State snapshots
   - Performance metrics

3. **System Extensions**
   - Custom event types
   - Plugin support
   - External integrations
