import { Group } from "three";
export class Container3D extends Group {
    interaction;
    constructor(...children) {
        super();
        if (children?.length) {
            this.add(...children);
        }
    }
    setPosition(x = undefined, y = undefined, z = undefined) {
        this.position.set(x || 0, y || 0, z || 0);
        return this;
    }
    setPositionY(y) {
        this.position.setY(y);
        return this;
    }
    setRotation(x = undefined, y = undefined, z = undefined) {
        this.rotation.set(x || 0, y || 0, z || 0);
        return this;
    }
    setContextInfo(contextInfo) {
        this.userData.contextInfo = contextInfo;
        return this;
    }
    lookAtPoint(point) {
        this.lookAt(point);
        return this;
    }
    rotationFromLine(line) {
        const angle = Math.PI * 2 - line.direction.angle();
        this.rotation.set(0, angle, 0);
        return this;
    }
    setRenderOrder(order) {
        this.traverse((child) => {
            child.renderOrder = order;
        });
        this.renderOrder = order;
        return this;
    }
    setInteraction(interaction) {
        this.interaction = interaction;
        return this;
    }
    add(...children) {
        if (children?.length) {
            super.add(...children);
        }
        return this;
    }
}
