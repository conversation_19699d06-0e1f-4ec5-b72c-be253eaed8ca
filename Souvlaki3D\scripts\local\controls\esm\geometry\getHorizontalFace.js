import { project3DLinesOnHorizontalPlane } from "./project3DLinesOnHorizontalPlane";
import { GeometryPart } from "./GeometryBuilder";
import { getUvMap } from "./getUvMap";
import { triangulate } from "./triangulate";
export function getHorizontalFace(directionLine, pointOutsideLine, corners, textureSize, materialIndex, reverseFaces = false) {
    const points2d = project3DLinesOnHorizontalPlane(directionLine, pointOutsideLine, corners);
    return new GeometryPart(corners.map(p => [p.x, p.y, p.z]).flat(), triangulate(points2d).faces.map(x => reverseFaces ? x.reverse() : x).flat(), getUvMap(textureSize, points2d), materialIndex);
}
