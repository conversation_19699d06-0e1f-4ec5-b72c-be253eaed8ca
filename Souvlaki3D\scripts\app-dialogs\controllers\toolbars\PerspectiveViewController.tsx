import { BaseActionController } from "./BaseActionController";
import { StateService } from "../../../state/StateService";
import { ViewService } from "../../services/ViewService";
import { IToolbarActionOptions } from "./IToolbarActionOptions";
import { Display } from "../../../models/common/Enums";

export class PerspectiveViewController extends BaseActionController {

    public static $inject = ["StateService", "ViewService"];

    constructor(private state: StateService, private viewService: ViewService, options: IToolbarActionOptions) {
        super(options);
        this.defaultInitialize();
    }

    public execute(): void {
        this.viewService.rotateView(45, 45, Display.PerspectiveView);
    }

    public update(): void {
        const isInPosition = this.state.view.displayMode === Display.PerspectiveView;
        this.element.classList.toggle("disabled", isInPosition);
    }
}