import { IApplicationSettings } from "../models/infrastructure/IApplicationSettings";
import { AvailableActions, Direction, Display, ElementShape, PlacementMode, ShowDimensions, UnitType } from "../models/common/Enums";

export const overrides: Partial<IApplicationSettings> = {
    app: {
        controllers: [
            "SvkController",
            "SvkMovablesController",
            "RoomController",
            "CeilingDistanceController",
            "SvkSelectItemController",
            "SnackbarComponent",
            "SaveWarningComponent",
            "DevelopmentController",

            {type: "DeleteController", options: {selector: "#toolbar [data-action='Delete']"}},
            {type: "RotateMovableController", options: {selector: "#toolbar [data-action='RotateMovable']"}},
            {type: "RotateMovableCcwController", options: {selector: "#toolbar [data-action='RotateMovableCcw']"}},
            {type: "FlipController", options: {selector: "#toolbar [data-action='Flip']"}},
            {type: "UndoController", options: {selector: "#toolbar [data-action='Undo']", shortcut: "KeyZ"}},
            {type: "RedoController", options: {selector: "#toolbar [data-action='Redo']", shortcut: "KeyY"}},
            {type: "ZoomInController", options: {selector: "#toolbar [data-action='ZoomIn']"}},
            {type: "ZoomOutController", options: {selector: "#toolbar [data-action='ZoomOut']"}},
            {type: "TopViewController", options: {selector: "#toolbar [data-action='TopView']"}},
            {type: "FrontViewController", options: {selector: "#toolbar [data-action='FrontView']"}},
            {type: "SideViewController", options: {selector: "#toolbar [data-action='SideView']"}},
            {type: "PerspectiveViewController", options: {selector: "#toolbar [data-action='PerspectiveView']"}},
            {type: "GridController", options: {selector: "#toolbar [data-action='Grid']"}},
            {type: "RulerController", options: {selector: "#toolbar [data-action='Ruler']"}},
            {type: "SaveController", options: {selector: "#toolbar [data-action='Save']"}},
        ],
        controls: [
            {
                type: "DraggerControl", options: {
                    positionPlaneColor: "#ddf2dc",
                    dragPlaneColor: "#808080",
                    snapDistance: 200,
                    enabledSnapPointsColor: "green",
                    disabledSnapPointsColor: "red",
                    wireframe: false,
                }
            },
            {type: "PlaneControl", options: {color: "#d6d6d6"}},
            "LightsControl",
            "BuildingControl",
            "RendererControl",
            "RulerControl",
            "LabelControl",
            {type: "SuspensionControl", options: {suspensionCableMaterial: "cable", suspensionCableRadius: 9.3, suspensionCableInnerRadius: 8.3, edgeColor: "#000000" }},
            "ImageControl",
            "WarningControl",
            "JointsControl",
            "WeightConstrainsControl",
            "WallEditControlWrapper",
            "SvkPowerControl",
            "FixingTypeControl",
        ],
        containerId: "app-3d",
        dragRootId: "middleRow",
        configuratorId: 5,
        configuratorCode: "svk",
        services: [
            "StateService",
            "setup_svk",
            "Api",
            "AtLeastOneAccessoryPerGroupValidator",
            "AtLeastOneLinearPerGroupValidator", "CalculateDefaultPowerConsumption",
            "ZonesLogic",
            "MessageHub",
            "StateComparer",
            "DimensionsLogic",
            "BuildingLogic",
            "LabelsLogic",
            "MenuLogic",
            "MovablesLogic",
            "AlertService",
            "ViewService",
        ],
    },

    view: {
        scaleRatio: 1.25,
        theta: 22,
        phi: 30,
        zoomExtends: false,
        displayMode: Display.PerspectiveView,
        showBuildingResize: true,
        showDimensions: ShowDimensions.Vertical | ShowDimensions.Horizontal,
        showLabels: false,
        unitType: UnitType.Metric,
        showPlane: false,
        backgroundColor: "#FFFFFF",
        backgroundColorExport: "#FFFFFF",
        backgroundAlpha: 1,
        ambientLightColor: "#4D4D4C",
        ambientLightIntensity: 1,
        ambientLightIntensityNight: 0.1,
        directionalLightColor: "#FFFFFF",
        directionalLightIntensity: 0.9,
        directionalLightIntensityNight: 0.1,
        availableActions: {
            [AvailableActions.Insert]: {},
            [AvailableActions.Rotate]: {},
            [AvailableActions.Move]: {},
            [AvailableActions.Resize]: {},
            [AvailableActions.Properties]: {},
            [AvailableActions.Delete]: {},
        },
        maxSuspension: 4000
    },

    movables: [
        // Linear elements
        {
            type: "A05558",
            // databaseId: "A05558",
            typology: "false",
            shape: ElementShape.Linear,
            width: 25,
            depth: 890,
            height: 25,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: 0, y: 0, z: -445, side: { x: 0, y: 0, z: -1 } },
                { x: 0, y: 0, z: 445, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [
                { x: 0, y: 0, z: -445 },
                { x: 0, y: 0, z: 445 },
                { x: 0, y: 0, z: -445, attachDirection: Direction.Top },
                { x: 0, y: 0, z: 445, attachDirection: Direction.Top },
            ],
            childrenTypes: ["A2618", "A05667", "A05666", "A2874", "A05675", "A05676", "A2631", "ASVSG00063C00"],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                snapsTo: ["A2614", "A2620", "A2619"],
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05558.glb"
                    ],
                    edgeThresholdAngle: 30,
                }
            },
            data: null
        },
        {
            type: "A05563",
            // databaseId: "A05563",
            typology: "false",
            shape: ElementShape.Linear,
            width: 25,
            depth: 1190,
            height: 25,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: 0, y: 0, z: -595, side: { x: 0, y: 0, z: -1 } },
                { x: 0, y: 0, z: 595, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [
                { x: 0, y: 0, z: -595 },
                { x: 0, y: 0, z: 595 },
                { x: 0, y: 0, z: -595, attachDirection: Direction.Top },
                { x: 0, y: 0, z: 595, attachDirection: Direction.Top },
            ],
            childrenTypes: ["A2618", "A05667", "A05666", "A2874", "A05675", "A05676", "A2631", "ASVSG00063C00"],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                snapsTo: ["A2614", "A2620", "A2619"],
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05563.glb"
                    ],
                    edgeThresholdAngle: 40,
                }
            },
            data: null
        },
        {
            type: "A05569",
            // databaseId: "A05569",
            typology: "false",
            shape: ElementShape.Linear,
            width: 25,
            depth: 1490,
            height: 25,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: 0, y: 0, z: -745, side: { x: 0, y: 0, z: -1 } },
                { x: 0, y: 0, z: 745, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [
                { x: 0, y: 0, z: -745 },
                { x: 0, y: 0, z: 745 },
                { x: 0, y: 0, z: -745, attachDirection: Direction.Top },
                { x: 0, y: 0, z: 745, attachDirection: Direction.Top },
            ],
            childrenTypes: ["A2618", "A05667", "A05666", "A2874", "A05675", "A05676", "A2631", "ASVSG00063C00"],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                snapsTo: ["A2614", "A2620", "A2619"],
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05569.glb"
                    ],
                    edgeThresholdAngle: 40,
                }
            },
            data: null
        },

        // Linear elements with led strips
        {
            type: "A05544",
            // databaseId: "A05544",
            typology: "false",
            shape: ElementShape.Linear,
            width: 25,
            depth: 890,
            height: 25,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: 0, y: 0, z: -445, side: { x: 0, y: 0, z: -1 } },
                { x: 0, y: 0, z: 445, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [
                { x: 0, y: 0, z: -445 },
                { x: 0, y: 0, z: 445 },
                { x: 0, y: 0, z: -445, attachDirection: Direction.Top },
                { x: 0, y: 0, z: 445, attachDirection: Direction.Top },
            ],
            childrenTypes: ["A2618", "A05667", "A05666", "A2874", "A05675", "A05676", "A2631", "ASVSG00063C00"],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                snapsTo: ["A2614", "A2620", "A2619"],
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05544.glb"
                    ],
                    edgeThresholdAngle: 30,
                }
            },
            data: null
        },
        {
            type: "A05596",
            // databaseId: "A05596",
            typology: "false",
            shape: ElementShape.Linear,
            width: 25,
            depth: 1190,
            height: 25,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: 0, y: 0, z: -595, side: { x: 0, y: 0, z: -1 } },
                { x: 0, y: 0, z: 595, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [
                { x: 0, y: 0, z: -595 },
                { x: 0, y: 0, z: 595 },
                { x: 0, y: 0, z: -595, attachDirection: Direction.Top },
                { x: 0, y: 0, z: 595, attachDirection: Direction.Top },
            ],
            childrenTypes: ["A2618", "A05667", "A05666", "A2874", "A05675", "A05676", "A2631", "ASVSG00063C00"],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                snapsTo: ["A2614", "A2620", "A2619"],
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05596.glb"
                    ],
                    edgeThresholdAngle: 40,
                }
            },
            data: null
        },
        {
            type: "A05564",
            // databaseId: "A05564",
            typology: "false",
            shape: ElementShape.Linear,
            width: 25,
            depth: 1490,
            height: 25,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: 0, y: 0, z: -745, side: { x: 0, y: 0, z: -1 } },
                { x: 0, y: 0, z: 745, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [
                { x: 0, y: 0, z: -745 },
                { x: 0, y: 0, z: 745  },
                { x: 0, y: 0, z: -745, attachDirection: Direction.Top },
                { x: 0, y: 0, z: 745, attachDirection: Direction.Top  },
            ],
            childrenTypes: ["A2618", "A05667", "A05666", "A2874", "A05675", "A05676", "A2631", "ASVSG00063C00"],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                snapsTo: ["A2614", "A2620", "A2619"],
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05564.glb"
                    ],
                    edgeThresholdAngle: 40,
                }
            },
            data: null
        },

        // Linear light tubes
        {
            type: "A05648",
            // databaseId: "A05648",
            typology: "false",
            shape: ElementShape.Linear,
            width: 25,
            depth: 890,
            height: 25,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: 0, y: 0, z: -445, side: { x: 0, y: 0, z: -1 } },
                { x: 0, y: 0, z: 445, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                snapsTo: ["A2614", "A2620", "A2619"],
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05648.glb"
                    ],
                    edgeThresholdAngle: 30,
                }
            },
            data: null
        },
        {
            type: "A05649",
            // databaseId: "A05649",
            typology: "false",
            shape: ElementShape.Linear,
            width: 25,
            depth: 1190,
            height: 25,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: 0, y: 0, z: -595, side: { x: 0, y: 0, z: -1 } },
                { x: 0, y: 0, z: 595, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                snapsTo: ["A2614", "A2620", "A2619"],
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05649.glb"
                    ],
                    edgeThresholdAngle: 40,
                }
            },
            data: null
        },
        {
            type: "A05650",
            // databaseId: "A05650",
            typology: "false",
            shape: ElementShape.Linear,
            width: 25,
            depth: 1490,
            height: 25,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: 0, y: 0, z: -745, side: { x: 0, y: 0, z: -1 } },
                { x: 0, y: 0, z: 745, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                snapsTo: ["A2614", "A2620", "A2619"],
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05650.glb"
                    ],
                    edgeThresholdAngle: 40,
                }
            },
            data: null
        },

        // Connector, angle, terminal
        {
            type: "A2614",
            // databaseId: "A2614",
            typology: "false",
            shape: ElementShape.Linear,
            width: 25,
            depth: 80,
            height: 32,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: 0, y: -3.5, z: 20, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                insertBetweenMaxDistance: -1,
                noSnap: ["A2614", "A2620", "A2619"],
                suspensionCables: [
                    { x: 0, y: 16, z: -10, side: { x: 0, y: 1, z: 0 } }
                ],
                powerSupplyLabelPosition: { x: 0, y: 0, z: -100 },
                canHavePowerSource: true
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A2614.glb"
                    ],
                    edgeThresholdAngle: 18,
                }
            },
            data: null
        },
        {
            type: "A2620",
            // databaseId: "A2620",
            typology: "false",
            shape: ElementShape.Linear,
            width: 25,
            depth: 100,
            height: 31,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: 0, y: -3.5, z: -30, side: { x: 0, y: 0, z: -1 } },
                { x: 0, y: -3.5, z: 30, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                insertBetweenMaxDistance: -1,
                noSnap: ["A2614", "A2620", "A2619"],
                suspensionCables: [
                    { x: 0, y: 15, z: 0, side: { x: 0, y: 1, z: 0 } }
                ],
                powerSupplyLabelPosition: { x: 0, y: 0, z: -70 },
                canHavePowerSource: true,
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A2620.glb"
                    ],
                    edgeThresholdAngle: 30,
                }
            },
            data: null
        },
        {
            type: "A2619",
            // databaseId: "A2619",
            typology: "false",
            shape: ElementShape.Angle,
            width: 80,
            depth: 80,
            height: 32,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [
                { x: -20, y: -3.5, z: -10, side: { x: -1, y: 0, z: 0 } },
                { x: 10, y: -3.5, z: 20, side: { x: 0, y: 0, z: 1 } }
            ],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                insertBetweenMaxDistance: -1,
                noSnap: ["A2614", "A2620", "A2619"],
                suspensionCables: [
                    { x: 10, y: 16, z: -10, side: { x: 0, y: 1, z: 0 } }
                ],
                itemCenterLabelPosition: { x: -90, y: 0, z: 90 },
                powerSupplyLabelPosition: { x: 70, y: 0, z: -70 },
                canHavePowerSource: true,
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A2619.glb"
                    ],
                    edgeThresholdAngle: 18,
                }
            },
            data: null
        },

        // Power source, not shown in the scene
        {
            type: "A2631",
            // databaseId: "A2631",
            typology: "false",
            shape: ElementShape.Point,
            width: 50,
            depth: 50,
            height: 71,
            placementMode: PlacementMode.Ceiling,
            offset: 50,
            snaps: [],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [
                { x: 0, y: 10, z: -25 },
                { x: 0, y: 10, z: 25 },
            ],
            parentPlacement: [],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Ceiling],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A2631.glb"
                    ],
                    edgeThresholdAngle: 30,
                }
            },
            data: null
        },

        // Attachments
        {
            type: "A2618",
            typology: "false",
            shape: ElementShape.Point,
            width: 104,
            depth: 50,
            height: 157,
            placementMode: PlacementMode.Attachment,
            offset: 50,
            snaps: [],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [
                { x: -27, y: 53, z: -82 },
                { x: -27, y: 53, z: 82 },
                { x: -27, y: -53, z: -82, attachDirection: Direction.Top },
                { x: -27, y: -53, z: 82, attachDirection: Direction.Top },
            ],
            parentPlacement: [PlacementMode.Ceiling, PlacementMode.Horizontal],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Attachment],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                ignoreAttachDirectionForCollision: true,
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A2618.glb"
                    ],
                    edgeThresholdAngle: 30,
                    rotateXWhenAttachedToTop: true,
                }
            },
            data: null
        },
        {
            type: "A05667",
            typology: "false",
            shape: ElementShape.Point,
            width: 160,
            depth: 160,
            height: 252,
            placementMode: PlacementMode.Attachment,
            offset: 50,
            snaps: [],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [
                { x: 0, y: 101, z: -82 },
                { x: 0, y: 101, z: 82 },
                { x: 0, y: -101, z: -82, attachDirection: Direction.Top },
                { x: 0, y: -101, z: 82, attachDirection: Direction.Top },
            ],
            parentPlacement: [PlacementMode.Ceiling, PlacementMode.Horizontal],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Attachment],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                ignoreAttachDirectionForCollision: true,
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05667.glb"
                    ],
                    edgeThresholdAngle: 30,
                    rotateXWhenAttachedToTop: true,
                }
            },
            data: null
        },
        {
            type: "A05666",
            typology: "false",
            shape: ElementShape.Point,
            width: 160,
            depth: 160,
            height: 284,
            placementMode: PlacementMode.Attachment,
            offset: 50,
            snaps: [],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [
                { x: 0, y: 117, z: -82 },
                { x: 0, y: 117, z: 82 },
                { x: 0, y: -117, z: -82, attachDirection: Direction.Top },
                { x: 0, y: -117, z: 82, attachDirection: Direction.Top },
            ],
            parentPlacement: [PlacementMode.Ceiling, PlacementMode.Horizontal],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Attachment],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                ignoreAttachDirectionForCollision: true,
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05666.glb"
                    ],
                    edgeThresholdAngle: 30,
                    rotateXWhenAttachedToTop: true,
                }
            },
            data: null
        },
        {
            type: "A05675",
            // databaseId: "A05675",
            typology: "false",
            shape: ElementShape.Point,
            width: 160,
            depth: 160,
            height: 752,
            placementMode: PlacementMode.Attachment,
            offset: 50,
            snaps: [],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [
                { x: 0, y: 351, z: -82 },
                { x: 0, y: 351, z: 82 },
            ],
            parentPlacement: [PlacementMode.Ceiling, PlacementMode.Horizontal],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Attachment],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                ignoreAttachDirectionForCollision: true,
            },
            skin: {
                graphics3D: "SuspendedCadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05675.glb"
                    ],
                    edgeThresholdAngle: 30,
                }
            },
            data: null
        },
        {
            type: "A05676",
            // databaseId: "A05676",
            typology: "false",
            shape: ElementShape.Point,
            width: 160,
            depth: 160,
            height: 784,
            placementMode: PlacementMode.Attachment,
            offset: 50,
            snaps: [],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [
                { x: 0, y: 367, z: -82 },
                { x: 0, y: 367, z: 82 },
            ],
            parentPlacement: [PlacementMode.Ceiling, PlacementMode.Horizontal],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Attachment],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                ignoreAttachDirectionForCollision: true,
            },
            skin: {
                graphics3D: "SuspendedCadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05676.glb"
                    ],
                    edgeThresholdAngle: 30,
                }
            },
            data: null
        },
        {
            type: "A2874",
            typology: "false",
            shape: ElementShape.Point,
            width: 100,
            depth: 100,
            height: 186,
            placementMode: PlacementMode.Attachment,
            offset: 50,
            snaps: [],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [
                { x: 0, y: 68, z: -51 },
                { x: 0, y: 68, z: 51 },
            ],
            parentPlacement: [PlacementMode.Ceiling, PlacementMode.Horizontal],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [PlacementMode.Attachment],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                ignoreAttachDirectionForCollision: true,
            },
            skin: {
                graphics3D: "CadFileGraphics", data: {
                    finishes: ["/contents/configurator/svk/gltf/A2874.glb"],
                    edgeThresholdAngle: 15,
                }
            },
            data: null
        },
        // Fixing types
        {
            type: "A05664", // Ceiling rose for transformer
            typology: "false",
            shape: ElementShape.Point,
            width: 51,
            depth: 483,
            height: 63,
            placementMode: PlacementMode.Attachment,
            offset: 50,
            snaps: [],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [],
            parentPlacement: [PlacementMode.Ceiling],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                ignoreAttachDirectionForCollision: true,
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05664.glb"
                    ],
                }
            },
            data: null
        },
        {
            type: "A05706", // Mechanical connection for rigid suspension rod
            typology: "false",
            shape: ElementShape.Point,
            width: 150,
            depth: 150,
            height: 30,
            placementMode: PlacementMode.Attachment,
            offset: 50,
            snaps: [],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [],
            parentPlacement: [PlacementMode.Ceiling],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                ignoreAttachDirectionForCollision: true,
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05706.glb"
                    ],
                }
            },
            data: null
        },
        {
            type: "A05707", // Mechanical/electrical connection for rigid suspension rod
            typology: "false",
            shape: ElementShape.Point,
            width: 51,
            depth: 51,
            height: 31,
            placementMode: PlacementMode.Attachment,
            offset: 50,
            snaps: [],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [],
            parentPlacement: [PlacementMode.Ceiling],
            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [],
                maximumCeilingDistance: 2000,
                canHavePowerSource: false,
                ignoreAttachDirectionForCollision: true,
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [
                        "/contents/configurator/svk/gltf/A05707.glb"
                    ],
                }
            },
            data: null
        },
    ],
    powerSettings: {
        sharpingOrDiffused: [],
        maximumCircuitLengthsPerPowerPointOutput: [
            { effectiveOutput: 125, maxLengthCm: 9700 },
            { effectiveOutput: 210, maxLengthCm: 5400 },
            { effectiveOutput: 270, maxLengthCm: 4200 },
        ]
    },
};
