﻿import { MovableItem } from "../../models/state/MovableItem";
import { DesignState } from "../../models/state/DesignState";
import { Display } from "../../models/common/Enums";
import { IGraphicsData } from "../../models/infrastructure/IApplicationSettings";
import { MovableGraphics } from "./MovableGraphics";
import { BoxGeometry, Material, Mesh, Object3D } from "three";

export class BoxGraphics extends MovableGraphics {

    private lastKey: string;

    protected createMesh(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData, materials: Material[]): Object3D {
        const geometry = new BoxGeometry(m.width, m.height, m.depth);
        return new Mesh(geometry, materials[0]);
    }

    protected shouldUpdate(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData): boolean {
        const key = m && m.finishes[0]
            ? m.finishes[0]
            : data ? data.finishes[0] : undefined;

        if (key !== this.lastKey) {
                this.lastKey = key;
                return true;
        }

        return false;
    }
}