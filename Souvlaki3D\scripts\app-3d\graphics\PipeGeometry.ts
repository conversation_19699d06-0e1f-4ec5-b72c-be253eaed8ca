import { ExtrudeGeometry, Path, Shape } from "three";

export class PipeGeometry extends ExtrudeGeometry {

    constructor(outerRadius: number, innerRadius: number, height: number) {
        const arcShape = new Shape();
        arcShape.moveTo(outerRadius * 2, outerRadius);
        arcShape.absarc(outerRadius, outerRadius, outerRadius, 0, Math.PI * 2, false);
        const holePath = new Path();
        holePath.moveTo(outerRadius + innerRadius, outerRadius);
        holePath.absarc(outerRadius, outerRadius, innerRadius, 0, Math.PI * 2, true);
        arcShape.holes.push(holePath);

        super(arcShape, {
            depth: height,
            bevelEnabled: false,
            steps: 1,
            curveSegments: 60
        });

        this.center();
        this.rotateX(Math.PI * -.5);
    }
}