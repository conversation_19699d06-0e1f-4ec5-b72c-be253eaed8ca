import { dom } from "@immugio/jsx-and-html-utils";

interface Props {
    powerSourceId: number;
    warn: string;
    remainingWats: number;
    remainingChannels: number;
    tags: string[];
    onReset: () => void;
}

export function TaPowerInfo({powerSourceId, warn, remainingWats, remainingChannels, tags, onReset}: Props): HTMLElement {
    return (
        <div id="powerinfo">
            <div>
                <p>POWER SUPPLY {((powerSourceId > 9 ? "" : "0") + powerSourceId.toString())}</p>
                <p id="powerinfo-tags">
                    {tags.map(x => <span className="prop-label" title={x}>{x}</span>)}
                </p>
                <p className={warn}>{remainingWats} W LEFT</p>
                {typeof remainingChannels === "number" && <p className="powerinfo-channels">{remainingChannels} DALI channels left</p>}
            </div>
            <p>
                <button className="power-reset-btn" onclick={onReset}>RESET</button>
            </p>
        </div>
    );
}