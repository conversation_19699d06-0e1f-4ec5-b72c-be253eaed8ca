﻿import { IGraphicsData } from "../../models/infrastructure/IApplicationSettings";
import { MovableItem } from "../../models/state/MovableItem";
import { DesignState } from "../../models/state/DesignState";
import { Display, Attributes } from "../../models/common/Enums";
import { MaterialUtils } from "../utils/MaterialUtils";
import { MovableGraphics } from "./MovableGraphics";
import { DoubleSide, Material, Mesh, Object3D, Shape, ShapeGeometry, Vector2 } from "three";

export class ResizeFpInGraphics extends MovableGraphics {

    private readonly material: Material;
    private readonly transparent: Material;

    constructor() {
        super();

        this.material = MaterialUtils.getMaterial("#000", false);
        this.material.transparent = true;
        this.material.side = DoubleSide;
        this.material.opacity = 0.5;

        this.transparent = MaterialUtils.getMaterial("#fff", false);
        this.transparent.transparent = true;
        this.transparent.side = DoubleSide;
        this.transparent.opacity = 0;
    }

    protected createMesh(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData, materials: Material[]): Object3D {
        const object3D = new Object3D();
        object3D.name = Attributes.ZoomIgnore;

        const n = [-117.38, -166.24, -117.89, -166.68, -116.84, -165.84, -116.28, -165.47, -115.69, -165.15, -115.08, -164.86, -114.46, -164.62, -113.82, -164.41, -113.16, -164.26, -112.5, -164.14, -111.83, -164.07, -111.16, -164.05, -24.1, -163.77, -23.08, -163.73, -22.07, -163.62, -21.06, -163.45, -20.07, -163.2, -19.09, -162.89, -18.14, -162.51, -17.22, -162.07, -16.33, -161.57, -15.48, -161.01, -14.67, -160.39, -13.9, -159.71, -13.17, -158.99, -12.5, -158.22, -11.89, -157.4, -11.33, -156.55, -10.83, -155.66, -10.39, -154.73, -10.01, -153.78, -9.71, -152.81, -9.46, -151.81, -9.29, -150.81, -9.19, -149.79, -9.15, -148.77, -9.15, 56.85, -9.08, 58.9, -8.87, 60.93, -8.52, 62.95, -8.04, 64.94, -7.42, 66.9, -6.67, 68.8, -5.79, 70.65, -4.78, 72.44, -3.66, 74.15, -2.42, 75.78, -1.08, 77.33, 0.37, 78.77, 1.91, 80.12, 3.55, 81.36, 5.26, 82.48, 7.05, 83.49, 8.9, 84.37, 10.8, 85.12, 12.75, 85.74, 14.74, 86.22, 16.76, 86.57, 18.8, 86.78, 20.85, 86.85, 114.47, 86.85, 116.43, 86.79, 118.38, 86.59, 120.32, 86.27, 122.23, 85.83, 124.11, 85.26, 125.95, 84.57, 127.73, 83.76, 129.47, 82.83, 131.13, 81.79, 132.73, 80.65, 134.25, 79.4, 135.68, 78.06, 137.02, 76.63, 138.27, 75.11, 139.41, 73.52, 140.45, 71.85, 141.37, 70.12, 142.18, 68.33, 142.87, 66.49, 143.44, 64.61, 143.89, 62.7, 144.21, 60.77, 144.4, 58.81, 144.47, 56.85, 144.47, -148.77, 144.5, -149.79, 144.6, -150.81, 144.78, -151.82, 145.02, -152.81, 145.33, -153.78, 145.7, -154.73, 146.14, -155.66, 146.64, -156.55, 147.2, -157.4, 147.82, -158.22, 148.49, -158.99, 149.21, -159.71, 149.98, -160.39, 150.79, -161.01, 151.64, -161.57, 152.53, -162.07, 153.45, -162.51, 154.4, -162.89, 155.38, -163.2, 156.37, -163.45, 157.38, -163.62, 158.39, -163.73, 159.41, -163.77, 239.45, -164.04, 240.12, -164.07, 240.79, -164.14, 241.45, -164.25, 242.1, -164.41, 242.74, -164.61, 243.37, -164.86, 243.98, -165.14, 244.57, -165.47, 245.13, -165.84, 245.67, -166.24, 246.18, -166.68, 246.66, -167.15, 247.1, -167.65, 247.52, -168.18, 247.89, -168.74, 248.23, -169.32, 248.53, -169.92, 248.78, -170.54, 249, -171.18, 249.17, -171.83, 249.29, -172.49, 249.38, -173.16, 249.41, -173.83, 249.4, -174.5, 249.35, -175.17, 249.25, -175.84, 249.11, -176.49, 248.92, -177.14, 248.69, -177.77, 248.42, -178.38, 248.11, -178.98, 247.76, -179.55, 247.37, -180.1, 246.94, -180.62, 246.49, -181.11, 88.89, -338.71, 87.22, -340.28, 85.45, -341.73, 83.58, -343.06, 81.64, -344.27, 79.62, -345.35, 77.53, -346.3, 75.39, -347.1, 73.2, -347.77, 70.97, -348.29, 68.71, -348.66, 66.43, -348.89, 64.14, -348.96, 61.85, -348.89, 59.57, -348.66, 57.31, -348.29, 55.08, -347.77, 52.89, -347.1, 50.75, -346.3, 48.66, -345.35, 46.64, -344.27, 44.7, -343.06, 42.83, -341.73, 41.06, -340.28, 39.39, -338.71, -118.2, -181.12, -118.66, -180.63, -119.09, -180.11, -119.47, -179.56, -119.83, -178.98, -120.14, -178.39, -120.41, -177.77, -120.64, -177.14, -120.83, -176.5, -120.97, -175.84, -121.07, -175.17, -121.12, -174.5, -121.13, -173.83, -121.09, -173.16, -121.01, -172.49, -120.88, -171.83, -120.71, -171.18, -120.5, -170.55, -120.24, -169.93, -119.94, -169.32, -119.61, -168.74, -119.23, -168.18, -118.82, -167.65, -118.37, -167.15];
        const n2 = [134.91, 161.38, 134.32, 160.17, 135.43, 162.62, 135.86, 163.9, 136.2, 165.2, 136.46, 166.52, 136.62, 167.86, 136.7, 169.2, 136.68, 170.55, 136.58, 171.89, 136.38, 173.22, 136.1, 174.54, 135.72, 175.83, 135.27, 177.09, 134.72, 178.33, 134.1, 179.52, 133.39, 180.67, 132.62, 181.76, 131.76, 182.81, 130.85, 183.79, -31.8, 346.44, -33.47, 348, -35.24, 349.46, -37.1, 350.79, -39.05, 352, -41.07, 353.08, -43.16, 354.02, -45.3, 354.83, -47.49, 355.5, -49.72, 356.02, -51.98, 356.39, -54.26, 356.61, -56.55, 356.69, -58.84, 356.61, -61.12, 356.39, -63.38, 356.02, -65.61, 355.5, -67.8, 354.83, -69.94, 354.02, -72.03, 353.08, -74.05, 352, -75.99, 350.79, -77.86, 349.46, -79.63, 348, -81.3, 346.44, -243.31, 184.42, -244.23, 183.44, -245.08, 182.4, -245.86, 181.3, -246.56, 180.16, -247.19, 178.97, -247.73, 177.74, -248.19, 176.47, -248.56, 175.18, -248.85, 173.87, -249.04, 172.54, -249.15, 171.2, -249.17, 169.86, -249.09, 168.51, -248.93, 167.18, -248.68, 165.86, -248.34, 164.56, -247.91, 163.29, -247.4, 162.04, -246.8, 160.84, -246.13, 159.67, -245.38, 158.56, -244.55, 157.5, -243.66, 156.49, -242.7, 155.55, -241.68, 154.68, -240.61, 153.87, -239.48, 153.14, -238.3, 152.49, -237.09, 151.91, -235.84, 151.42, -234.55, 151.02, -233.25, 150.7, -231.92, 150.47, -230.59, 150.33, -229.24, 150.28, -157.31, 150.02, -156.63, 149.99, -155.95, 149.92, -155.28, 149.8, -154.62, 149.64, -153.97, 149.43, -153.34, 149.18, -152.73, 148.88, -152.13, 148.55, -151.56, 148.17, -151.02, 147.76, -150.51, 147.31, -150.03, 146.83, -149.58, 146.32, -149.17, 145.77, -148.8, 145.2, -148.46, 144.61, -148.17, 143.99, -147.92, 143.36, -147.72, 142.71, -147.56, 142.05, -147.44, 141.38, -147.37, 140.7, -147.35, 140.02, -147.35, -65.27, -147.27, -67.56, -147.05, -69.84, -146.68, -72.1, -146.16, -74.33, -145.49, -76.52, -144.68, -78.67, -143.74, -80.75, -142.66, -82.77, -141.45, -84.72, -140.12, -86.58, -138.66, -88.35, -137.1, -90.02, -135.43, -91.59, -133.66, -93.04, -131.79, -94.38, -129.85, -95.58, -127.83, -96.66, -125.74, -97.61, -123.6, -98.42, -121.41, -99.08, -119.18, -99.6, -116.92, -99.97, -114.64, -100.2, -112.35, -100.27, -94.35, -100.27, -93.69, -100.25, -93.04, -100.19, -92.4, -100.08, -91.76, -99.93, -91.13, -99.74, -90.52, -99.51, -89.92, -99.24, -89.35, -98.93, -88.79, -98.59, -88.26, -98.21, -87.75, -97.79, -87.28, -97.34, -86.83, -96.87, -86.41, -96.36, -86.03, -95.83, -85.69, -95.27, -85.38, -94.7, -85.11, -94.1, -84.88, -93.49, -84.69, -92.86, -84.54, -92.22, -84.43, -91.58, -84.37, -90.93, -84.35, -90.27, -84.37, -89.62, -84.43, -88.97, -84.54, -88.32, -84.69, -87.69, -84.88, -87.06, -85.11, -86.45, -85.38, -85.85, -85.69, -85.27, -86.03, -84.72, -86.41, -84.19, -86.83, -83.68, -87.28, -83.2, -87.75, -82.76, -88.26, -82.34, -88.79, -81.96, -89.35, -81.61, -89.92, -81.31, -90.52, -81.04, -91.13, -80.8, -91.76, -80.61, -92.4, -80.47, -93.04, -80.36, -93.69, -80.3, -94.35, -80.27, -112.35, -80.27, -113.33, -80.24, -114.31, -80.15, -115.27, -79.99, -116.23, -79.76, -117.17, -79.48, -118.09, -79.13, -118.98, -78.73, -119.85, -78.26, -120.68, -77.75, -121.48, -77.17, -122.24, -76.55, -122.95, -75.88, -123.63, -75.16, -124.25, -74.41, -124.82, -73.61, -125.34, -72.77, -125.8, -71.91, -126.21, -71.01, -126.55, -70.1, -126.84, -69.16, -127.06, -68.2, -127.22, -67.23, -127.32, -66.25, -127.35, -65.27, -127.35, 140.02, -127.42, 142.06, -127.63, 144.09, -127.97, 146.11, -128.46, 148.09, -129.07, 150.04, -129.82, 151.94, -130.7, 153.79, -131.7, 155.57, -132.81, 157.28, -134.05, 158.91, -135.39, 160.45, -136.83, 161.9, -138.37, 163.25, -139.99, 164.49, -141.7, 165.61, -143.48, 166.62, -145.32, 167.5, -147.22, 168.26, -149.17, 168.88, -151.15, 169.37, -153.16, 169.72, -155.2, 169.94, -157.24, 170.02, -229.17, 170.28, -67.16, 332.3, -66.44, 332.97, -65.68, 333.59, -64.88, 334.16, -64.05, 334.68, -63.18, 335.14, -62.29, 335.55, -61.37, 335.89, -60.43, 336.18, -59.48, 336.4, -58.51, 336.56, -57.53, 336.66, -56.55, 336.69, -55.57, 336.66, -54.59, 336.56, -53.62, 336.4, -52.67, 336.18, -51.73, 335.89, -50.81, 335.55, -49.92, 335.14, -49.05, 334.68, -48.22, 334.16, -47.42, 333.59, -46.66, 332.97, -45.94, 332.3, 116.7, 169.65, 33.44, 169.65, 32.76, 169.63, 32.08, 169.56, 31.4, 169.44, 30.74, 169.28, 30.09, 169.07, 29.45, 168.82, 28.84, 168.53, 28.24, 168.19, 27.67, 167.82, 27.13, 167.41, 26.61, 166.96, 26.13, 166.47, 25.68, 165.96, 25.27, 165.42, 24.89, 164.84, 24.56, 164.25, 24.27, 163.63, 24.02, 163, 23.81, 162.35, 23.65, 161.68, 23.53, 161.01, 23.46, 160.33, 23.44, 159.65, 23.46, 158.97, 23.53, 158.29, 23.65, 157.61, 23.81, 156.95, 24.02, 156.3, 24.27, 155.67, 24.56, 155.05, 24.89, 154.45, 25.27, 153.88, 25.68, 153.34, 26.13, 152.82, 26.61, 152.34, 27.13, 151.89, 27.67, 151.48, 28.24, 151.1, 28.84, 150.77, 29.45, 150.48, 30.09, 150.23, 30.74, 150.02, 31.4, 149.86, 32.08, 149.74, 32.76, 149.67, 33.44, 149.65, 116.7, 149.65, 118.05, 149.69, 119.39, 149.83, 120.72, 150.06, 122.02, 150.37, 123.31, 150.77, 124.56, 151.26, 125.78, 151.83, 126.96, 152.48, 128.09, 153.21, 129.17, 154.01, 130.2, 154.89, 131.16, 155.83, 132.06, 156.83, 132.88, 157.89, 133.64, 159.01];
        const n3 = [-800, -800, 800, -800, 800, 800, -800, 800];

        object3D.add(ResizeFpInGraphics.getShape(n, this.material));
        object3D.add(ResizeFpInGraphics.getShape(n2, this.material));
        object3D.add(ResizeFpInGraphics.getShape(n3, this.transparent));

        return object3D;
    }

    private static getShape(n: number[], material: Material) {
        const object3D = new Object3D();

        const points: Vector2[] = [];

        for (let i = 0; i < n.length; i += 2) {
            const p = new Vector2(n[i], n[i + 1]);
            points.push(p);
        }

        const geo = new ShapeGeometry(new Shape(points));
        const mesh = new Mesh(geo, material);

        object3D.add(mesh);
        object3D.rotation.x = Math.PI / 2;

        const container = new Object3D();
        container.add(object3D);
        container.position.y = 10;

        return container;
    }

    protected getMaterial(d: DesignState, m: MovableItem, data: IGraphicsData): Material[] {
        return [this.material];
    }
}