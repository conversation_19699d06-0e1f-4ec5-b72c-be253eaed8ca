import { BaseActionController } from "./BaseActionController";
import { StateService } from "../../../state/StateService";
import { IToolbarActionOptions } from "./IToolbarActionOptions";
import { pushState } from "../../../state/actions/pushState";

export class UndoController extends BaseActionController {

    public static $inject = ["StateService"];

    constructor(private state: StateService, options: IToolbarActionOptions) {
        super(options);
        this.defaultInitialize();
    }

    public execute(): void {
        const undo = this.state.undo();
        if (undo) {
            this.update();
            pushState(undo.state);
        }
    }

    public update(): void {
        this.element.classList.toggle("disabled", !this.state.canUndo);
    }
}