import { IApplicationSettings } from "../../../models/infrastructure/IApplicationSettings";
import { IApplicationState } from "../../../models/state/IApplicationState";
import { MovableItem } from "../../../models/state/MovableItem";
import { InteractionType, DotState } from "../../../models/common/Enums";
import { Scene } from "../../../models/scene/Scene";
import { isLinear } from "../../../logic/helpers/PositioningUtils";
import { Vector3 } from "three";
import { ISnapPoint } from "../../../models/positioning/ISnapPoint";
import { MovablesUtils } from "../../../logic/helpers/MovablesUtils";
import { BaseDotControl } from "./BaseDotControl";
import { Label } from "../Label";

export class HoyDotControl extends BaseDotControl {

    private off = 70;
    private offsetHoy = 150;

    constructor(private movable: MovableItem, private index: number, private absoluteSnap: ISnapPoint, private settings: IApplicationSettings, private scene: Scene) {
        super();

        this.interaction = {
            interactionType: InteractionType.ChooseLighting,
            movable: this.movable,
            lineIndex: index
        };
        this.attachControlToObject3D();

        this.dot = this.getDot(DotState.Default);
        this.object3D.add(this.dot);

        this.position();
        this.savePosition(movable);
        this.setCursor(DotState.Default);
    }

    private position(): void {
        const snaps = this.movable.snaps, m = this.movable, i = this.index, snap = snaps[i];
        let position: Vector3;

        if (isLinear(m)) {
            position = this.getPoint(m, snap);
            const move = new Vector3(snaps[i].side.x, snaps[i].side.y, snaps[i].side.z);
            const rotation = { rotationX: m.rotationX, rotationY: m.rotationY, rotationZ: m.rotationZ };

            const facingBottom = !!(move.y === -1 && !move.x && !move.z);

            const sign = 1; //i === 0 ? -1 : 1; // Ensures dots are on the same side when viewed on plan (outside for curves)

            if (facingBottom) {
                if (m.elevation % 2 === 0) {
                    rotation.rotationZ += 90 * sign;
                } else {
                    rotation.rotationX += 90 * sign;
                }
            } else {
                rotation.rotationY += 90 * sign;
            }

            this.rotate(rotation, move);
            move.multiplyScalar(this.offsetHoy);

            position.add(move);
        } else {
            const [s1, s2] = snaps;
            const v1 = new Vector3(s1.x, s1.y, s1.z);
            const v2 = new Vector3(s2.x, s2.y, s2.z);
            v1.add(v2).negate();
            const add = v1.clone().normalize().multiplyScalar(this.off * 2);
            v1.add(add);

            position = this.getPoint(m, v1);
        }

        this.object3D.position.set(position.x, position.y, position.z);
    }

    public update(s: IApplicationState): void {
        const a = s.view.activeItem;
        const notAllowed = (InteractionType.ResizeBuilding | InteractionType.ResizeHeight | InteractionType.DraggingItem | InteractionType.InsertingItem);

        if (a && (a.interactionType & notAllowed)) {
            this.object3D.visible = false;
            return;
        }

        this.object3D.visible = true;
        if (this.positionChanged(this.movable)) {
            this.position();
            this.savePosition(this.movable);
        }

        const currentState = this.getCurrentState();
        if (currentState !== this.lastState) {
            this.lastState = currentState;

            this.object3D.remove(this.dot);
            this.dot = this.getDot(currentState);
            this.object3D.add(this.dot);

            this.setCursor(currentState);
        }

        this.updateHighlight();

        const currentText = this.getCurrentText();
        if (currentText !== this.lastText) {
            this.lastText = currentText;

            this.object3D.remove(this.label);
            this.label = null;

            if (currentText) {
                this.label = Label(currentText);
                this.object3D.add(this.label);
                this.object3D.quaternion.copy(this.scene.camera.quaternion);
            }
        }

        if (this.label) {
            this.object3D.quaternion.copy(this.scene.camera.quaternion);
        }
    }

    private getCurrentText(): string {
        const supply = this.movable.data.powerSource.find(x => x.sideIndex === this.index);
        return supply ? `FEED POINT ${supply.powerSourceId}` : null;
    }

    private updateHighlight(): void {
        const m = this.movable;
        const isPowerSource = m.data.powerSource.some(x => x.sideIndex === this.index);
        const indexMatch = isPowerSource && (!m.highlightPart || MovablesUtils.parseIndex(m.highlightPart) === this.index);

        if ((this.hovered || (m.highlight && indexMatch) && !this.activeHighlight)) {
            this.showHighlight();
        } else if (!this.hovered && (!m.highlight || !indexMatch) && this.activeHighlight) {
            this.hideHighlight();
        }
    }

    private getCurrentState() {
        if (this.movable.data.powerSource.find(x => x.sideIndex === this.index)) {
            return DotState.Connected;
        }

        return DotState.Default;
    }
}