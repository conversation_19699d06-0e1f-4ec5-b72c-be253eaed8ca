import { Shape } from "three";

export class RoundedRectangleShape extends Shape {
    constructor(x: number, y: number, radius: number, height: number, width: number) {
        super();
        this.moveTo(x, y + radius);
        this.lineTo(x, y + height - radius);
        this.quadraticCurveTo(x, y + height, x + radius, y + height);
        this.lineTo(x + width - radius, y + height);
        this.quadraticCurveTo(x + width, y + height, x + width, y + height - radius);
        this.lineTo(x + width, y + radius);
        this.quadraticCurveTo(x + width, y, x + width - radius, y);
        this.lineTo(x + radius, y);
        this.quadraticCurveTo(x, y, x, y + radius);
    }
}