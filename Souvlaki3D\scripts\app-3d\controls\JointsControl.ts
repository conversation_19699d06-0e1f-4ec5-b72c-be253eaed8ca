import { IApplicationState } from "../../models/state/IApplicationState";
import { DesignState } from "../../models/state/DesignState";
import { IJoint } from "../../models/state/IJoint";
import { Scene } from "../../models/scene/Scene";
import { TextUtils } from "../utils/TextUtils";
import { CanvasControl } from "./CanvasControl";
import { Object3D, Mesh, CircleGeometry, Vector3, MeshBasicMaterial } from "three";
import { isNear } from "../../logic/helpers/IsNear";
import { getMovablePoints } from "../../logic/helpers/positioning/getMovablePoints";
import { Vec3 } from "@immugio/three-math-extensions";

export class JointsControl extends CanvasControl {

    public static $inject = ["scene"];

    private material = new MeshBasicMaterial({ color: "white", depthTest: false, transparent: true });
    private background = new MeshBasicMaterial({ color: "black", depthTest: false, transparent: true, opacity: 0.5 });
    private geo = new CircleGeometry(70, 32);

    private joints: IJoint[];

    constructor(private scene: Scene) {
        super();
    }

    private getConnections(d: DesignState, joints: IJoint[]) {

        const connections: IJoint[] = [];

        for (const j of joints || []) {
            const e1 = d.movableItems.find(x => x.id === j.elements[0]);
            const e2 = d.movableItems.find(x => x.id === j.elements[1]);
            if (e1 && e2) {
                const snaps1 = getMovablePoints(e1, e1.snaps, e1);
                const snaps2 = getMovablePoints(e2, e2.snaps, e2);
                for (const s1 of snaps1) {
                    for (const s2 of snaps2) {
                        if (isNear(s1, s2)) {
                            j.point = { x: s1.x, y: s1.y, z: s1.z };
                            connections.push(j);
                            break;
                        }
                    }
                }
            }
        }

        return connections;
    }

    public update(s: IApplicationState): void {

        if (this.joints && this.object3D.children.length) {
            this.object3D.children.forEach(x => x.quaternion.copy(this.scene.camera.quaternion));
            this.object3D.position.set(-s.view.offset.x, 0, -s.view.offset.y);
        }

        if (this.joints === s.view.joints) {
            return;
        }

        this.clearChildren();
        this.joints = s.view.joints;

        const connections = this.getConnections(s.design, s.view.joints);

        for (const j of connections) {
            const letter = this.getLetter(j.label);
            letter.position.copy(Vec3.fromPoint(j.point));
            this.object3D.add(letter);
            letter.quaternion.copy(this.scene.camera.quaternion);
        }
    }

    private getLetter(key: string) {

        const letter = TextUtils.getText(key, 70, this.material, 1, true);

        letter.geometry.computeBoundingBox();
        const size = letter.geometry.boundingBox.getSize(new Vector3());
        letter.position.x -= size.x / (key === "1" ? .8 : key.includes("1") ? 1.4 : 1.7);
        letter.position.y -= size.y / 2;
        letter.position.z -= size.z / 2;
        letter.renderOrder = 999;

        const circle = new Mesh(this.geo, this.background);

        const object = new Object3D();
        object.add(circle, letter);

        this.object3D.add(object);

        return object;
    }
}