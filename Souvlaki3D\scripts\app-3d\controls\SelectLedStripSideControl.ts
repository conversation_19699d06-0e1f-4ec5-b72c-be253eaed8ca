import { CanvasControl } from "./CanvasControl";
import { MovableItem } from "../../models/state/MovableItem";
import { DesignState } from "../../models/state/DesignState";
import { Container3D } from "../graphics/Container3D";
import { CanvasUtils } from "../utils/CanvasUtils";
import { InteractionType } from "../../models/common/Enums";
import { StripFromPoints } from "../graphics/StripFromPoints";
import { LedUtils } from "../../logic/helpers/LedUtils";
import { Object3D } from "three";
import { IPoint2D } from "../../models/geometry/IPoint2D";

export class SelectLedStripSideControl extends CanvasControl {

    private readonly minLength = 83.33333333333333;
    private readonly maxLength = 4500;

    constructor(d: DesignState, movable: MovableItem, offset: IPoint2D) {
        super();
        this.overCursor = "pointer";
        const mesh = this.createMesh(d, movable);
        this.object3D.add(mesh);
        this.position(movable, offset);
    }

    private createMesh(d: DesignState, m: MovableItem): Object3D {
        const strip = this.getStrips(d, m);
        return new Container3D(...strip);
    }

    private getStrips(d: DesignState, m: MovableItem): Object3D[] {
        const paths = LedUtils.getPathsConnectedToMovableAndTrimAttachments(m, d.movableItems).filter(x => x.getLength() >= this.minLength);
        return paths.map(p => {
            const interaction = { interactionType: InteractionType.LedStripIndex, lineIndex: p.sourceElementIndex, control: this, movable: m };
            return [
                StripFromPoints(m, p.getPoints(this.maxLength), ["blue", "green"][p.sourceElementIndex % 2], 20, interaction),
                StripFromPoints(m, p.getPoints(this.maxLength), "#fff", 80, interaction, 0), // The second transparent strip is used to make a larger clickable area
            ];
        }).flat();
    }

    private position(movable: MovableItem, offset: IPoint2D): void {
        this.object3D.position.set(movable.x - offset.x, movable.y, movable.z - offset.y);
        this.object3D.rotation.set(0, 0, 0);
        CanvasUtils.applyRotation(this.object3D, movable);
    }
}