# State Management System

## Overview

The Souvlaki 3D Configuration Engine uses a sophisticated state management system that handles:
- Current design configuration
- View state and camera settings
- Undo/redo history
- State comparison and diffing
- Real-time updates

## Core Components

### StateService

The `StateService` is the central state management system that maintains the application's state:

```typescript
class StateService<T = unknown> implements IApplicationState<T> {
    public design: DesignState;      // Current building configuration
    public prevDesign: DesignState;  // Previous building state
    public view: ViewState;          // Current view settings
    public prevView: ViewState;      // Previous view state
    public diff: StateDiff;          // Changes between states
}
```

### Design State

The `DesignState` represents the current configuration of the lighting system:

- Movable items (tracks, connectors, accessories)
- Room dimensions and properties
- Power configurations
- Validation state

### View State

The `ViewState` manages the visual representation:

- Camera position and orientation
- Display mode (perspective/orthographic)
- Selection state
- Active interactions
- UI state

## State Updates

### Update Flow

1. **State Modification**
   ```typescript
   public applyUpdate(): boolean {
       if (StateService.pendingUpdates.length) {
           const update = StateService.pendingUpdates.shift();
           
           if (update.undo) {
               this.saveHistory();
           }

           if (update.design) {
               this.design = typeof update.design === "function" 
                   ? update.design(this.design)
                   : Object.assign(this.design, update.design);
           }

           if (update.view) {
               this.view = typeof update.view === "function"
                   ? update.view(this.view)
                   : Object.assign(this.view, update.view);
           }

           return true;
       }
       return false;
   }
   ```

2. **State Comparison**
   ```typescript
   public compare(): void {
       if (!this.design || !this.view) {
           throw new Error("Design should be initialised");
       }
       
       if (!this.view.offset) {
           this.view.offset = this.design.offset();
       }
       
       this.comparer.compare(this);
   }
   ```

## History Management

### Undo/Redo System

The state service maintains undo and redo stacks for history management:

```typescript
private undoStack: UndoStep[];
private redoStack: UndoStep[];
```

### History Operations

1. **Saving History**
   ```typescript
   public saveHistory(): void {
       const clone = this.design.clone();
       clone.movableItems.forEach(x => x.selected = false);
       this.addUndo(new UndoStep(clone, this.view.displayMode));
       this.redoStack = [];
   }
   ```

2. **Undo Operation**
   ```typescript
   public undo(): UndoStep {
       if (this.undoStack.length === 0) return undefined;
       
       const lastUndo = this.undoStack.pop();
       
       if (this.redoStack.length === 0) {
           this.addRedo(new UndoStep(this.design, this.view.displayMode));
           this.addRedo(lastUndo);
       } else {
           this.addRedo(lastUndo);
       }
       
       this.design = lastUndo.state.clone();
       this.design.refresh = true;
       return new UndoStep(this.design, lastUndo.displayMode);
   }
   ```

3. **Redo Operation**
   ```typescript
   public redo(): UndoStep {
       if (this.redoStack.length === 0) return undefined;
       
       const lastRedo = this.redoStack.pop();
       this.addUndo(lastRedo);
       
       this.design = lastRedo.state.clone();
       this.design.refresh = true;
       return new UndoStep(this.design, lastRedo.displayMode);
   }
   ```

## State Initialization

### New State Setup
```typescript
public initialize(d: DesignState): void {
    this.undoStack = [];
    this.redoStack = [];
    this.initializeView();
    this.design = d ? d.clone() : undefined;
    this.prevDesign = undefined;
    this.prevView = undefined;
    this.diff = new StateDiff();
}
```

### View Initialization
```typescript
public initializeView(): void {
    this.view = new ViewState(settings.view).clone();
}
```

## Performance Optimizations

### State Diffing
- Only updates changed components
- Maintains previous state references
- Optimizes render cycles

### Memory Management
- Limits undo/redo stack size
- Cleans up unused state
- Optimizes cloning operations

## Best Practices

1. **State Updates**
   - Always use the state service for updates
   - Avoid direct state mutations
   - Maintain immutability

2. **History Management**
   - Save state before significant changes
   - Clear redo stack on new changes
   - Limit history stack size

3. **State Comparison**
   - Use efficient comparison methods
   - Implement custom equality checks
   - Optimize diffing algorithms

## Example Usage

### Basic State Update
```typescript
// Update design state
StateService.pendingUpdates.push({
    design: {
        // New design properties
    },
    undo: true  // Save to history
});

// Process updates
while (this.state.applyUpdate()) {
    this.update();
}
```

### State Comparison
```typescript
// Compare states and generate diff
this.state.compare();

// Apply updates based on diff
if (this.state.diff.someProperty) {
    // Handle property change
}
```

### History Management
```typescript
// Save current state
this.state.saveHistory();

// Perform undo
if (this.state.canUndo) {
    const previousState = this.state.undo();
    // Handle state restoration
}

// Perform redo
if (this.state.canRedo) {
    const nextState = this.state.redo();
    // Handle state restoration
}
