import { BaseActionController } from "./BaseActionController";
import { IToolbarActionOptions } from "./IToolbarActionOptions";
import { Events } from "../../../models/infrastructure/Events";
import { MessageHub } from "../../../infrastructure/MessageHub";

export class SaveController extends BaseActionController {

    constructor(options: IToolbarActionOptions) {
        super(options);
        this.defaultInitialize();
    }

    public execute(): void {
        MessageHub.broadcast(Events.save);
    }

}