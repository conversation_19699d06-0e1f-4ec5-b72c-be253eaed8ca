﻿import { Box3, Mesh, MeshBasicMaterial, Object3D, Vector3 } from "three";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { Size } from "../../models/geometry/Size";
import { InitialTransform } from "../../models/infrastructure/IApplicationSettings";

export class ModelLoadUtils {

    private static cached: { [key: string]: Object3D; } = {};

    public static load(url: string, size?: Size, transform?: InitialTransform): Promise<Object3D> {
        return new Promise<Object3D>((resolve, reject) => {

            if (!url) {
                reject();
                return;
            }

            if (this.cached[url]) {
                resolve(this.clone(this.cached[url]));
                return;
            }

            const gltfLoader = new GLTFLoader();
            gltfLoader.load(url, result => {
                let object3D = new Object3D();
                result.scene.children.forEach(x => object3D.add(x.clone(true)));
                if (size || transform) {
                    object3D = this.initialTransform(object3D, size, transform);
                }
                this.cached[url] = object3D;

                resolve(this.clone(object3D));
            }, null, () => resolve(null));
        });
    }

    private static clone(source: Object3D): Object3D {
        const clone = source.clone(true);
        clone.traverse(o => {
            if (o.type === "Mesh") {
                if ((o as Mesh).material) {
                    (o as Mesh).material = ((o as Mesh).material as MeshBasicMaterial).clone();
                }
            }
        });

        return clone;
    }

    private static initialTransform(o: Object3D, size: Size, transform: InitialTransform): Object3D {
        // Step 1 - initial transform based on settings
        const initial = new Object3D();
        initial.add(o);
        initial.rotation.x = transform?.rotateX ? Math.PI / 180 * transform.rotateX : 0;
        initial.rotation.y = transform?.rotateY ? Math.PI / 180 * transform.rotateY : 0;
        initial.rotation.z = transform?.rotateZ ? Math.PI / 180 * transform.rotateZ : 0;

        // Step 2 - fit size
        const scale = new Object3D();
        scale.add(initial);
        const box = new Box3().setFromObject(scale);
        const scaleSize = box.getSize(new Vector3());
        scale.scale.set(size.width / scaleSize.x, size.height / scaleSize.y, size.depth / scaleSize.z);

        // Step 3 - center on origin
        box.setFromObject(scale);
        const positionY = size.height / 2 - box.max.y;
        const positionX = size.width / 2 - box.max.x;
        const positionZ = size.depth / 2 - box.max.z;

        const container = new Object3D();
        container.add(scale);
        container.position.set(positionX, positionY, positionZ);

        // Step 4 - return object with no transform on it
        const final = new Object3D();
        final.add(container);
        return final;
    }
}