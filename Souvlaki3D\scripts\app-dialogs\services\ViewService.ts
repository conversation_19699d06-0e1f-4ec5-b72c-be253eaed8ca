import { Display } from "../../models/common/Enums";
import { CanvasUtils } from "../../app-3d/utils/CanvasUtils";
import { Events } from "../../models/infrastructure/Events";
import { StateService } from "../../state/StateService";
import { Scene } from "../../models/scene/Scene";
import { MessageHub } from "../../infrastructure/MessageHub";
import { BuildingLogic } from "../../logic/BuildingLogic";

export class ViewService {

    public static $inject = ["StateService", "scene", "BuildingLogic"];

    constructor(private state: StateService, private scene: Scene, private buildingLogic: BuildingLogic) {}

    public rotateView(phi: number, theta: number, display: Display): void {
        const steps = 30;
        let counter = 0;

        this.state.view.displayMode = this.state.view.displayMode | Display.Animating;

        const prevPhi = this.state.view.phi; // Animate view angles
        const prevTheta = this.state.view.theta;

        const prevCenter = this.scene.center.clone(); // Animate perspective camera center
        const newCenter = CanvasUtils.defaultSceneCenter(this.state.design.height);

        const prevOrtho = this.scene.cameraO.clone(); // Animate ortho camera window
        const newOrtho = this.scene.cameraO.clone();
        const box = CanvasUtils.getSceneBoxSize(this.scene.control.zoomObject);
        CanvasUtils.zoomExtendsOrtho(box, newOrtho, this.scene.renderer.domElement, this.state.view);

        const anim = () => {
            if (counter === steps) {
                this.state.view.phi = phi;
                this.state.view.theta = theta;
                this.state.view.displayMode = display;
                this.state.view.showPlane = !(this.state.view.displayMode & Display.PerspectiveView);
                MessageHub.broadcast(Events.updateViewMode);

            } else {

                this.state.view.phi = this.equalSteps(prevPhi, phi, steps, counter);
                this.state.view.theta = this.equalSteps(prevTheta, theta, steps, counter);

                this.scene.center.x = this.equalSteps(prevCenter.x, newCenter.x, steps, counter);
                this.scene.center.y = this.equalSteps(prevCenter.y, newCenter.y, steps, counter);
                this.scene.center.z = this.equalSteps(prevCenter.z, newCenter.z, steps, counter);

                this.scene.cameraO.left = this.equalSteps(prevOrtho.left, newOrtho.left, steps, counter);
                this.scene.cameraO.right = this.equalSteps(prevOrtho.right, newOrtho.right, steps, counter);
                this.scene.cameraO.top = this.equalSteps(prevOrtho.top, newOrtho.top, steps, counter);
                this.scene.cameraO.bottom = this.equalSteps(prevOrtho.bottom, newOrtho.bottom, steps, counter);

                counter++;
                requestAnimationFrame(() => anim());
            }

            CanvasUtils.setCamera(this.scene, this.state.view);
            this.state.view.closesElevations = this.buildingLogic.getClosesElevations(this.state.view.phi, this.state.view.theta);
        };

        anim();
    }

    private equalSteps(from: number, to: number, steps: number, counter: number): number {
        const diff = to - from;
        const inc = diff / steps;
        return from + counter * inc;
    }
}