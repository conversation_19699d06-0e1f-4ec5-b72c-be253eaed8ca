import { CanvasControl } from "../controls/CanvasControl";
import { Object3D } from "three";
import { MovableItem } from "../../models/state/MovableItem";
import { IPoint3D } from "../../models/geometry/IPoint3D";

export class SnapControl extends CanvasControl {

    constructor(position: IPoint3D, mesh: Object3D) {
        super();
        const size = 50;
        this.object3D.add(mesh);
        this.interaction = {
            movable: new MovableItem({
                x: position.x,
                y: position.y,
                z: position.z,
                width: size,
                depth: size,
                height: size
            })
        };

        this.object3D.position.set(position.x, position.y, position.z);
    }
}