import { Vec3 } from "@immugio/three-math-extensions";
import { DoubleSide, MeshBasicMaterial, Object3D, PlaneGeometry, Vector3 } from "three";
import { intersectObjects } from "../../utils/intersectObjects";
import { Mesh3D } from "../../objects/Mesh3D";
export class BaseDragger {
    context;
    constructor(context) {
        this.context = context;
        context.scene.add(this.root);
    }
    root = new Object3D();
    clickOffset;
    setupHorizontalDraggingPlane(y) {
        const geo = new PlaneGeometry(200000, 200000);
        geo.rotateX(-Math.PI / 2);
        const material = new MeshBasicMaterial({ visible: false, side: DoubleSide });
        const plane = new Mesh3D(geo, material).setPositionY(y);
        this.root.add(plane);
    }
    dispose() {
        if (this.root.parent) {
            this.root.parent.remove(this.root);
        }
    }
    onDrag(point) {
    }
}
export class HorizontalPlaneDragger extends BaseDragger {
    interaction;
    constructor(context, interaction) {
        super(context);
        this.interaction = interaction;
        this.setupHorizontalDraggingPlane(interaction.control.position.y);
    }
    onDrag(point) {
        const intersection = intersectObjects(this.context.canvas, this.context.camera, point, [this.root])?.[0];
        if (intersection) {
            const controlObject = this.interaction.control.root;
            const control = this.interaction.control;
            if (!this.clickOffset) {
                const worldPos = controlObject.getWorldPosition(new Vec3());
                const pos = controlObject.position.clone();
                this.clickOffset = worldPos.sub(pos);
            }
            controlObject.position.copy(intersection.point.sub(this.clickOffset));
            control.onDrag(point);
        }
    }
}
export class LineDragger extends BaseDragger {
    interaction;
    constructor(context, interaction) {
        super(context);
        this.interaction = interaction;
        this.setupHorizontalDraggingPlane(interaction.control.position.y); // TODO: This could be improved by setting the angle/orientation of the plane to match the line
    }
    onDrag(point) {
        const intersection = intersectObjects(this.context.canvas, this.context.camera, point, [this.root])?.[0];
        if (intersection) {
            const controlObject = this.interaction.control.root;
            const control = this.interaction.control;
            if (!this.clickOffset) {
                const worldPos = controlObject.getWorldPosition(new Vector3());
                this.interaction.options.line.setCenter(worldPos.clone());
                const pos = controlObject.position.clone();
                this.clickOffset = worldPos.sub(pos);
            }
            const pointOnLine = this.interaction.options.line.closestPointToPoint(intersection.point, true, new Vec3());
            controlObject.position.copy(pointOnLine.sub(this.clickOffset));
            control.onDrag(point);
        }
    }
}
