import { AnyContent, classes, dom } from "@immugio/jsx-and-html-utils";
import { isElementChildOf } from "@immugio/jsx-and-html-utils";

interface IProps {
    children?: AnyContent;
    className?: string;
    onLoaded?: (e: Snackbar) => void;
    onClose?: () => void;
    closeOnOutsideClick?: boolean;
}

export type Snackbar = HTMLElement & {
    close?: () => void;
}

export function Snackbar({ children, className, onLoaded, onClose, closeOnOutsideClick }: IProps): Snackbar {
    let modal: Snackbar;

    function loaded(e: Snackbar) {
        modal = e;
        modal.close = close;
        document.body.append(modal);
        modal.style.maxHeight = modal.scrollHeight + "px";
        setTimeout(() => modal.classList.add("show"), 100);
        onLoaded?.(e);

        const observer = new MutationObserver(() => modal.style.maxHeight = modal.scrollHeight + "px");
        observer.observe(modal, { childList: true, subtree: true });
    }

    function close() {
        modal.classList.remove("show");
        modal.style.maxHeight = null;
        setTimeout(() => modal.remove(), 1000);
        onClose?.();
    }

    setTimeout(function() {
        window.addEventListener("mousedown", e => {
            if (e.target instanceof Element) {
                const p = isElementChildOf(e.target, modal);
                if (closeOnOutsideClick && !p) {
                    close();
                }
                if (e.target instanceof HTMLElement && e.target.classList.contains("close")) {
                    close();
                }
            }
        });
    }, 500);

    return (
        <div ref={loaded} className={classes("snackbar", className)}>
            {children}
        </div>
    );
}