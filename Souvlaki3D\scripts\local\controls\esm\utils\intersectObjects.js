import { Raycaster } from "three";
import { BaseControl } from "../controls/BaseControl";
const raycaster = new Raycaster();
export function intersectObjects(canvas, camera, clickPoint, objectsToIntersect) {
    const rect = canvas.getBoundingClientRect(); // Recalculate the canvas position
    const canvasX = clickPoint.x - rect.left;
    const canvasY = clickPoint.y - rect.top;
    const x = (canvasX / canvas.clientWidth) * 2 - 1; // Translate client coords into viewport x,y
    const y = -(canvasY / canvas.clientHeight) * 2 + 1;
    raycaster.setFromCamera({ x, y }, camera); // Set the raycaster to the camera position and direction
    return raycaster.intersectObjects(objectsToIntersect, true);
}
export function intersectControls(canvas, camera, clickPoint, objectsToIntersect) {
    const intersections = intersectObjects(canvas, camera, clickPoint, objectsToIntersect);
    for (const intersection of intersections) {
        const control = findControl(intersection.object);
        if (control) {
            return control;
        }
    }
    return null;
}
export function findControl(object) {
    if (object?.interaction && object.interaction.control instanceof BaseControl) {
        return object.interaction.control;
    }
    if (object.parent) {
        return findControl(object.parent);
    }
    return null;
}
