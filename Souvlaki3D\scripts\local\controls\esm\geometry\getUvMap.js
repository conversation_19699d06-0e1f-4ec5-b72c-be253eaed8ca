import { Vec2 } from "@immugio/three-math-extensions";
export function getUvMap(textureSize, points, offset = null) {
    offset ??= new Vec2(0, 0);
    const minX = Math.min(...points.map(p => p.x)) + offset.x;
    const minY = Math.min(...points.map(p => p.y)) + offset.y;
    const yRatio = 1 / textureSize.height;
    const xRatio = 1 / textureSize.width;
    // Gives a number between 0 and 1, where 0 means 0% of width and 1 means 100% of width
    return points.map(p => [(p.x - minX) * xRatio, (p.y - minY) * yRatio]).flat();
}
