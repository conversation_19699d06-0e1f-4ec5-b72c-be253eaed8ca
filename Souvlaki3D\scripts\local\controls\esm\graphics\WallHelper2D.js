import { BufferGeometry, DoubleSide, Line, Mesh, MeshBasicMaterial } from "three";
import { Container3D } from "../objects/Container3D";
import { Line3D, Vec2 } from "@immugio/three-math-extensions";
import { getHorizontalFace } from "../geometry/getHorizontalFace";
export class WallHelper2D {
    static render(walls, material) {
        return walls.traverse((wall, previous, next) => {
            return this.getWall(wall, previous, next, material);
        });
    }
    static getWall(wall, previous, next, material) {
        const outlines = wall.getOnPlanContour(previous, next);
        const outlinesPoints = [outlines.leftLine.start, outlines.leftLine.end, outlines.rightLine.end, outlines.rightLine.start, outlines.leftLine.start].map(p => p.in3DSpace(wall.bottomY));
        const centerLinePoints = [wall.startPointOnPlan.in3DSpace(wall.bottomY), wall.endPointOnPlan.in3DSpace(wall.bottomY)];
        const geo = getHorizontalFace(new Line3D(outlinesPoints[0], outlinesPoints[1]), outlinesPoints[2], outlinesPoints, new Vec2(1, 1), 0);
        const infill = new Mesh(geo.build(), new MeshBasicMaterial({ side: DoubleSide, transparent: true, opacity: 0 }));
        return new Container3D(new Line(new BufferGeometry().setFromPoints(centerLinePoints), material), new Line(new BufferGeometry().setFromPoints(outlinesPoints), material), infill);
    }
}
