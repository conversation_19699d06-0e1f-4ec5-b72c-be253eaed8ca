import { BaseScene } from "./BaseScene";
import { WallEditControl } from "../controls/wallEdit/WallEditControl";
import { Vec3 } from "@immugio/three-math-extensions";
import { Toolbar } from "../controls/wallEdit/Toolbar";
import { dom } from "@immugio/jsx-and-html-utils";
import { SceneContext } from "../objects/SceneContext";
import { wallDataToWallSet } from "../building/wallDataToWallSet";
/**
 * This example shows how to use WallEditControl to drag walls.
 * This class would be implemented in the application to use the drag control
 */
export class WallDraggingExample extends BaseScene {
    constructor() {
        super();
        this.toggleWallControl(this.wallsData1);
        this.orbitControls.maxDistance = 20000;
        this.camera.position.set(0, 8000, 0);
    }
    elements = {
        toolbar: null,
        addWallButton: null,
    };
    wallEditControl;
    isAddingWall = false;
    wallThickness = 150;
    wallsData1 = [
        { id: "6d8df7f8-70cf-4858-bf6e-4b6d433c7d61", start: new Vec3(0, 0, 1800), end: new Vec3(3000, 0, 1800), thickness: 150 },
        { id: "6d8df7f8-70cf-4858-bf6e-4b6d433c7d62", start: new Vec3(3000, 0, 1800), end: new Vec3(3000, 3000, 1800), thickness: 150 },
        { id: "6d8df7f8-70cf-4858-bf6e-4b6d433c7d63", start: new Vec3(3000, 3000, 1800), end: new Vec3(6000, 3000, 1800), thickness: 150 },
        { id: "6d8df7f8-70cf-4858-bf6e-4b6d433c7d64", start: new Vec3(6000, 3000, 1800), end: new Vec3(6000, 12000, 1800), thickness: 150 },
        { id: "6d8df7f8-70cf-4858-bf6e-4b6d433c7d65", start: new Vec3(6000, 12000, 1800), end: new Vec3(0, 12000, 1800), thickness: 150 },
        { id: "6d8df7f8-70cf-4858-bf6e-4b6d433c7d66", start: new Vec3(0, 12000, 1800), end: new Vec3(0, 0, 1800), thickness: 150 }
    ];
    wallsData2 = [
        { id: "6d8df7f8-70cf-4858-bf6e-4b6d433c7d63", start: new Vec3(6000, 12000, 1800), end: new Vec3(0, 12000, 1800), thickness: 150 },
        { id: "6d8df7f8-70cf-4858-bf6e-4b6d433c7d64", start: new Vec3(0, 12000, 1800), end: new Vec3(0, 0, 1800), thickness: 150 }
    ];
    toggleWallControl(wallsData) {
        if (wallsData && !this.wallEditControl) {
            const sceneContext = new SceneContext(this.scene, this.camera, this.renderer);
            const controlOptions = {
                defaultWallThickness: this.wallThickness,
                defaultWallHeight: Math.max(...wallsData.map(w => [w.start.z, w.end.z]).flat()),
                disableOrbitControls: (disable) => {
                    this.orbitControls.enabled = !disable;
                },
                onWallClicked: (wallSet, wall) => {
                    if (confirm("Remove wall?")) {
                        this.wallEditControl.setWalls(wallSet.clone().removeWall(wall.id));
                    }
                },
                onChange: () => {
                    console.log("onChange fired by WallEditControl");
                },
                onStep: (walls) => {
                    console.log("onStep fired by WallEditControl", walls);
                },
                position: new Vec3(-Math.max(...wallsData.map(w => [w.start.x, w.end.x]).flat()) / 2, 0, -Math.max(...wallsData.map(w => [w.start.y, w.end.y]).flat()) / 2),
            };
            document.body.append(dom(Toolbar, { ref: e => this.elements.toolbar = e },
                dom("button", { ref: e => this.elements.addWallButton = e, onclick: () => this.toggleIsAddingWall() }, "Add wall"),
                dom("button", { onclick: () => this.setWalls(wallDataToWallSet(this.wallsData1)) }, "Set walls 1"),
                dom("button", { onclick: () => this.setWalls(wallDataToWallSet(this.wallsData2)) }, "Set walls 2")));
            this.wallEditControl = new WallEditControl(controlOptions, sceneContext, wallDataToWallSet(wallsData));
        }
        if (!wallsData && this.wallEditControl) {
            const newWallData = this.wallEditControl.getWallData();
            console.log("New wall data after edit", newWallData);
            this.wallEditControl.dispose();
            this.wallEditControl = null;
            this.elements.toolbar.remove();
        }
    }
    setWalls(walls) {
        if (this.isAddingWall) {
            this.toggleIsAddingWall();
        }
        this.wallEditControl.setWalls(walls);
    }
    toggleIsAddingWall() {
        this.isAddingWall = !this.isAddingWall;
        this.elements.addWallButton.innerText = this.isAddingWall ? "Stop adding walls" : "Add wall";
        this.wallEditControl.toggleIsAddingWall(this.isAddingWall);
    }
}
new WallDraggingExample().run();
