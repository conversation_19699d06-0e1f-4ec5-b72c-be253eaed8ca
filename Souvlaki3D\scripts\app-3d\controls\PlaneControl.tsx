﻿import { IApplicationState } from "../../models/state/IApplicationState";
import { Display, Attributes } from "../../models/common/Enums";
import { CanvasControl } from "./CanvasControl";
import { Color } from "three";
import { InfiniteGridHelper } from "@immugio/infinite-grid";
import { dom } from "@immugio/jsx-and-html-utils";

interface IPlaneControlOptions {
    color: string;
    descriptions?: { [key: number]: string };
}

export class PlaneControl extends CanvasControl {

    private lastMode: Display;
    private readonly element: HTMLDivElement;

    constructor(private options: IPlaneControlOptions) {
        super();
        this.object3D.name = Attributes.ZoomIgnore;
        this.element = document.body.appendChild(<div className="elevation-info"></div>);
    }

    private viewOptions = {
        [Display.FrontView] : {axes: "xyz"},
        [Display.SideView]  : {axes: "zyx"},
        [Display.BottomView]: {axes: "xzy"},
        [Display.TopView]   : {axes: "xzy"},
    };

    public update(s: IApplicationState): void {
        if (!s) {
            return;
        }

        if (s.view.showPlane && !(s.view.displayMode & Display.Animating)) {
            this.object3D.visible = true;
            this.updateGrid(s);
        } else {
            this.object3D.visible = false;
            return;
        }
    }

    private updateGrid(s: IApplicationState): void {
        if (s.view.displayMode === this.lastMode) {
            return;
        }
        this.lastMode = s.view.displayMode;

        const options = this.viewOptions[s.view.displayMode];
        if (options) {
            this.object3D.clear();
            const plane = new InfiniteGridHelper(100, 1000, new Color(this.options.color), 40000, options.axes);
            plane.renderOrder = -1;
            this.object3D.add(plane);
        }

        const description = this.options.descriptions?.[s.view.displayMode];
        this.element.innerHTML = description || "";
    }
}