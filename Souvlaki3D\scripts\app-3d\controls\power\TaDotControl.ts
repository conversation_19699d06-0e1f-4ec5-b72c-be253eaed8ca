import { IApplicationSettings } from "../../../models/infrastructure/IApplicationSettings";
import { IApplicationState } from "../../../models/state/IApplicationState";
import { MovableItem } from "../../../models/state/MovableItem";
import { InteractionType, DotState } from "../../../models/common/Enums";
import { Scene } from "../../../models/scene/Scene";
import { MovablesUtils } from "../../../logic/helpers/MovablesUtils";
import { Vector3 } from "three";
import { BaseDotControl } from "./BaseDotControl";
import { Label } from "../Label";

export class TaDotControl extends BaseDotControl {

    private moveTowards = 50;
    private off = 70;

    constructor(private movable: MovableItem, private index: number, private collidingAttachments: MovableItem[], private settings: IApplicationSettings, private scene: Scene) {
        super();

        this.interaction = {
            interactionType: InteractionType.ChooseLighting,
            movable: this.movable,
            lineIndex: index,
            partId: movable.parts && movable.parts[index]
        };
        this.attachControlToObject3D();

        this.dot = this.getDot(DotState.Default);
        this.object3D.add(this.dot);

        this.position();
        this.savePosition(movable);
        this.setCursor(DotState.Default);
    }

    private position(): void {
        const snaps = this.movable.snaps, m = this.movable, i = this.index;
        const position = this.getPoint(m, snaps[i]);

        const towards = new Vector3(snaps[i].side.x, snaps[i].side.y, snaps[i].side.z);
        this.rotate(m, towards);
        towards.negate().multiplyScalar(this.moveTowards);
        position.add(towards);

        const move = new Vector3(snaps[i].side.x, snaps[i].side.y, snaps[i].side.z);
        const rotation = { rotationX: m.rotationX, rotationY: m.rotationY, rotationZ: m.rotationZ };

        const facingBottom = !!(move.y === -1 && !move.x && !move.z);

        const sign = i === 0 ? -1 : 1; // Ensures dots are on the same side when viewed on plan (outside for curves)

        if (facingBottom) {
            if (m.elevation % 2 === 0) {
                rotation.rotationZ += 90 * sign;
            } else {
                rotation.rotationX += 90 * sign;
            }
        } else {
            rotation.rotationY += 90 * sign;
        }

        this.rotate(rotation, move);
        move.multiplyScalar(this.off);

        position.add(move);

        this.object3D.position.set(position.x, position.y, position.z);
    }

    public update(s: IApplicationState): void {
        const a = s.view.activeItem;
        const notAllowed = (InteractionType.ResizeBuilding | InteractionType.ResizeHeight | InteractionType.DraggingItem | InteractionType.InsertingItem);

        if (a && (a.interactionType & notAllowed)) {
            this.object3D.visible = false;
            return;
        }

        this.object3D.visible = true;
        if (this.positionChanged(this.movable)) {
            this.position();
            this.savePosition(this.movable);
        }

        const currentState = this.getCurrentState(s.view.selectedPowerSupply, s.design.movableItems);
        if (currentState !== this.lastState) {
            this.lastState = currentState;

            this.object3D.remove(this.dot);
            this.dot = this.getDot(currentState);
            this.object3D.add(this.dot);

            this.setCursor(currentState);
        }

        this.updateHighlight();

        const currentText = this.getCurrentText();
        if (currentText !== this.lastText) {
            this.lastText = currentText;

            this.object3D.remove(this.label);
            this.label = null;

            if (currentText) {
                this.label = Label(currentText);
                this.object3D.add(this.label);
                this.object3D.quaternion.copy(this.scene.camera.quaternion);
            }
        }

        if (this.label) {
            this.object3D.quaternion.copy(this.scene.camera.quaternion);
        }
    }

    private updateHighlight(): void {
        const m = this.movable;
        const isPowerSource = m.data.powerSource.some(x => x.sideIndex === this.index);
        const indexMatch = isPowerSource && (!m.highlightPart || MovablesUtils.parseIndex(m.highlightPart) === this.index);

        if ((this.hovered || (m.highlight && indexMatch) && !this.activeHighlight)) {
            this.showHighlight();
        } else if (!this.hovered && !m.highlight && this.activeHighlight) {
            this.hideHighlight();
        }
    }

    private getCurrentText(): string {
        const supply = this.movable.data.powerSource.find(x => x.sideIndex === this.index);
        return supply ? `POWER SUPPLY ${supply.powerSourceId}` : null;
    }

    protected getCurrentState(selectedPowerSupply: { movable: MovableItem, index: number }, movableItems: MovableItem[]): DotState {
        const currentSource = this.movable.data.powerSource.find(x => x.sideIndex === this.index);
        const multipart = MovablesUtils.isMultiPart(this.movable);

        if (selectedPowerSupply && selectedPowerSupply.movable === this.movable && selectedPowerSupply.index === this.index) {
            return DotState.Selected;
        }

        if (!multipart && this.movable.data.powerSource.length > 0 && !currentSource) {
            return DotState.Disabled;
        }

        if (currentSource) {
            return DotState.Connected;
        }

        if (!multipart && this.movable.data.powerFrom.length > 0) {
            return DotState.Disabled;
        }

        if (multipart && this.movable.data.powerFrom.length > 1) {
            return DotState.Disabled;
        }

        if (this.collidingAttachments.length) {
            return DotState.Colliding;
        }

        if (multipart && this.movable.data.powerFrom.length === 1) {
            const partId = `Part_0${this.index}`;
            for (const main of movableItems) {
                for (const ps of main.data.powerSource) {
                    if (ps.powerSourceId === this.movable.data.powerFrom[0]) {
                        if (ps.connected.some(x => x.id === this.movable.id && x.partId !== partId)) {
                            return DotState.Disabled;
                        }
                    }
                }
            }
        }

        return DotState.Default;
    }
}