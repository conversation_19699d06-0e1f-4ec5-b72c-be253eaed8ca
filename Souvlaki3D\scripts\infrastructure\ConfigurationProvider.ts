import { IApplicationSettings, IBuildingTemplate, IMovableTemplate } from "../models/infrastructure/IApplicationSettings";
import { Line3D, Vec3 } from "@immugio/three-math-extensions";
import { PolygonUtils } from "../logic/helpers/PolygonUtils";
import { MovablesUtils } from "../logic/helpers/MovablesUtils";
import { ElementShape, PlacementMode } from "../models/common/Enums";
import { Activator } from "./Activator";
import { IMovableServerInfo } from "../models/settings/IMovableServerInfo";
import { ModelLoadUtils } from "../app-3d/utils/ModelLoadUtils";
import { MeshUtils } from "../app-3d/utils/MeshUtils";
import { Box3, Vector3 } from "three";
import { Arc } from "../models/geometry/Arc";
import { IPoint3D } from "../models/geometry/IPoint3D";
import { axes } from "../logic/helpers/geometry/Axes";
import { Label } from "../models/state/Label";

export class ConfigurationProvider {

    public static initialize(settings: IApplicationSettings): void {
        const overrides = Activator.get<Partial<IApplicationSettings>>("overrides");
        settings.movables.push(...overrides.movables);

        const withoutMovables = { ...overrides };
        delete withoutMovables.movables;
        Object.assign(settings, withoutMovables);
        settings.app.normalizedConfiguratorCode = settings.app.configuratorCode.charAt(0).toUpperCase() + settings.app.configuratorCode.slice(1);
        settings.app.debug = location.href.includes("localhost") || location.href.includes("localhost") || !!localStorage.getItem("debug");

        this.mapBehaviors(settings);
        //this.applyServerDataToMovables(settings, window.movablesServerInfo);
        this.setRadiusOptions(settings);
        this.calculateNoSnap(settings);
        this.setTypology(settings);
        this.setSnapPointsRadiusAndArcCircle(settings);
        this.setCablePoints(settings);
        this.applyLocalStorage(settings);
    }

    private static applyServerDataToMovables(setting: IApplicationSettings, serverData: IMovableServerInfo[] = []): void {
        const missingItems: IMovableServerInfo[] = [];
        for (const serverItem of serverData) {
            const toUpdate = setting.movables.find(x => x.type === serverItem.type);
            if (toUpdate) {
                toUpdate.databaseId = serverItem.databaseId;
                toUpdate.data = serverItem.data;
                if (toUpdate.skin.graphics3D === "CadFileGraphics") {
                    toUpdate.skin.data.finishes = serverItem.filename ? [serverItem.filename] : toUpdate.skin.data.finishes;
                }
            } else {
                missingItems.push(serverItem);
            }
        }
        this.logMissingConfigurationTemplatesToConsole(missingItems, setting.app.normalizedConfiguratorCode);
        this.logDuplicateTypes(serverData);
    }

    private static applyLocalStorage(settings: IApplicationSettings): void {
        if (settings.buildingTemplates[0] && settings.buildingTemplates[0].movables.length === 0) {

            if (localStorage.design) {
                try {
                    const parsed: IBuildingTemplate = JSON.parse(localStorage.design);
                    const knownTypes = settings.movables.map(x => x.type);
                    parsed.movables = (parsed.movables || []).filter(x => knownTypes.includes(x.type));
                    parsed.movables.forEach(x => MovablesUtils.fixColors(x));
                    settings.buildingTemplates[0] = parsed;

                } catch (e) {
                    console.error("Could not parse localStorage.design", e);
                }
            }

            if (localStorage.labels) {
                try {
                    settings.view.labels = Label.fromData(JSON.parse(localStorage.labels));

                } catch (e) {
                    console.error("Could not parse localStorage.labels", e);
                }
            }
        }
    }

    private static mapBehaviors(settings: IApplicationSettings): void {
        for (const m of settings.movables) {
            m.behavior ??= {};
            m.behavior.typologies = m.behavior.typologies || [m.typology];
        }
    }

    private static setRadiusOptions(settings: IApplicationSettings): void {
        const regex = /_R(\d{3})_/;
        for (const m of settings.movables) {
            if (!m.radius) {
                const x = m.type.match(regex);
                if (x && x[1]) {
                    const n = parseInt(x[1]);
                    if (n.toString() === x[1]) {
                        m.radius = n; // Radius from item name

                        if (m.snaps.length > 1) { // Radius points from snap points
                            const s1 = m.snaps[0], v1 = Vec3.fromPoint(m.snaps[0].side);
                            v1.applyAxisAngle(axes.y, Math.PI / 2);

                            const s2 = m.snaps[1], v2 = Vec3.fromPoint(m.snaps[1].side);
                            v2.applyAxisAngle(axes.y, Math.PI / 2);

                            const l1 = new Line3D(new Vec3(s1.x + v1.x, s1.y + v1.y, s1.z + v1.z ), Vec3.fromPoint(s1));
                            const l2 = new Line3D(new Vec3(s2.x + v2.x, s2.y + v2.y, s2.z + v2.z ), Vec3.fromPoint(s2));
                            const center = Vec3.fromPoint(PolygonUtils.intersectLines(l1, l2));

                            const angle = v1.angleTo(center); // Angle between snap points

                            const end = Vec3.fromPoint(s1); // Rotate end point to be in the center of arch
                            end.sub(center);
                            end.applyAxisAngle(axes.y, -angle);
                            end.add(center);

                            m.radiusPoints = [{ x: end.x, y: end.y, z: end.z, side: null }, {
                                x: center.x,
                                y: center.y,
                                z: center.z,
                                side: null
                            }];
                        }
                    }
                }
            }
        }
    }

    private static setTypology(settings: IApplicationSettings): void {
        for (const x of settings.movables) {
            // Typology can be either explicitly set or determined from name
            // Ignore attachments
            if (!x.typology && x.placementMode !== PlacementMode.Attachment && x.type !== "Group") {

                if (x.type.includes("_")) {
                    const prefix = x.type.substr(0, x.type.indexOf("_"));
                    const suffix = x.type.substring(x.type.lastIndexOf("_"));
                    if (suffix.startsWith("_T")) {
                        x.typology = prefix + suffix;
                        continue;
                    }
                }

                console.error(`Typology could not be determined for ${x.type}`);
            }
        }
    }

    private static setSnapPointsRadiusAndArcCircle(settings: IApplicationSettings): void {
        const halfThickness = 10;
        for (const x of settings.movables) {
            if (x.shape === ElementShape.Curve) {
                if (!x.snapPointsRadius) {
                    x.snapPointsRadius = Math.max(x.width, x.depth, x.height) - halfThickness; // This is a very crude way of determining the radius of the arc circle, might need to be improved or the values set explicitly

                    if (x.snaps?.length === 2) {
                        const secondCoordProps: ("y" | "z") = x.snaps.every(s => s.y === 0) ? "z" : "y";
                        const planeCoordProps: ("y" | "z") = x.snaps.every(s => s.y === 0) ?  "y": "z";
                        const points2d = x.snaps.map(s => ({x: s.x, y: s[secondCoordProps]}));
                        const arc = Arc.fromEndPointsAndRadius(points2d[0], points2d[1], x.snapPointsRadius);
                        const arcPoints = arc.getPoints(10);
                        x.arcPoints = (arcPoints.map(p => ({x: p.x, [planeCoordProps]: 0, [secondCoordProps]: p.y})) as any as IPoint3D[])
                            .map(p => ({x: p.x + arc.center.x, y: p.y, z: p.z + arc.center.y}));
                    }
                }
            }
        }
    }

    private static setCablePoints(settings: IApplicationSettings): void {
        for (const x of settings.movables) {
            if (
                !x.behavior.suspensionCables?.length &&
                x.behavior.suspensionCablesCount > 0 &&
                x.snaps?.length
            ) {
                x.behavior.suspensionCables = x.snaps.map(sn => ({...sn}));
                if (x.behavior.suspensionCablesCount === 3) {
                    const center = PolygonUtils.midPoint3D(x.behavior.suspensionCables[0], x.behavior.suspensionCables[1]);
                    x.behavior.suspensionCables.splice(1, 0, {...center, side: null});
                }
            }
        }
    }

    private static logDuplicateTypes(serverInfos: IMovableServerInfo[]): void {
        const map = new Map<string, string[]>();
        for (const x of serverInfos) {
            const key = x.type;
            if (map.has(key)) {
                map.get(key).push(x.databaseId);
            } else {
                map.set(key, [x.databaseId]);
            }
        }

        map.forEach((value, key) => {
            if (value.length > 1) {
                console.error(`Duplicate type '${key}' found in the server configuration. Database ids: [${value.join(", ")}]`);
            }
        });
    }

    private static async logMissingConfigurationTemplatesToConsole(missingItems: IMovableServerInfo[], code: string): Promise<void> {
        if (missingItems.length) {
            console.warn("Add the following to the configuration (please wait, 3d files are being processed asynchronously):");
            const templates: IMovableTemplate[] = [];
            for (const item of missingItems) {
                const template = await this.getBlankTemplateFromData(item, code);
                if (template) {
                    templates.push(template);
                }
            }
            console.warn(JSON.stringify(templates, null, 3));
        }
    }

    private static async getBlankTemplateFromData(item: IMovableServerInfo, code: string): Promise<IMovableTemplate> {
        const typologyPattern = /(_T\d+)/;
        const typologyMatch = item.type.match(typologyPattern);

        const template: IMovableTemplate = {
            type: item.type,
            databaseId: item.databaseId,
            typology: [code, typologyMatch?.[0]].filter(x => x).join(""),
            shape: undefined,
            width: undefined,
            depth: undefined,
            height: undefined,
            radius: undefined,
            placementMode: PlacementMode.Ceiling,
            offset: 100,
            ceilingDistance: undefined,
            floorDistance: 2300,

            snaps: [],
            radiusPoints: [],
            holderPoints: [],
            childrenTypes: [],
            attachmentPoints: [],
            parentPlacement: [],

            behavior: {
                rotateAlgorithm: "Rotate15Degrees",
                placementOptions: [],
                suspensionCablesCount: undefined,
                maximumCeilingDistance: 2000,
            },
            skin: {
                graphics3D: "CadFileGraphics",
                data: {
                    finishes: [item.filename].filter(x => x),
                }
            },

            data: item.data,
        };

        await this.addDataFrom3dFile(template);

        return template;
    }

    private static calculateNoSnap(settings: IApplicationSettings): void {

        const allTypes = settings.movables.map(x => x.type).filter(f => f !== "Group");

        // 1) Element snap combination can be either whitelisted of blacklisted. In runtime, only blacklist is checked.
        // When whitelist is provided, it needs to be converted to blacklist here at app start.
        for (const x of settings.movables) {
            if (x.behavior?.snapsTo) {
                x.behavior.noSnap = allTypes.filter(t => !x.behavior.snapsTo.includes(t));
            }
        }

        // 2) Copy blacklists between opposite items. E.g. when item "A" blacklist items "B" then item "B" must blacklist item "A"
        for (const movable of settings.movables) {
            if (movable.behavior?.noSnap) {
                const allBlacklisted = settings.movables.filter(x => movable.behavior.noSnap.includes(x.type));

                for (const blacklisted of allBlacklisted) {
                    blacklisted.behavior = blacklisted.behavior || {};
                    blacklisted.behavior.noSnap = blacklisted.behavior.noSnap || [];

                    if (!blacklisted.behavior.noSnap.includes(movable.type)) {
                        blacklisted.behavior.noSnap.push(movable.type);
                    }
                }
            }
        }
    }

    private static async addDataFrom3dFile(template: IMovableTemplate): Promise<void> {
        const url = template.skin.data.finishes[0];
        if (url) {
            const model = await ModelLoadUtils.load(url);

            if (!model) {
                console.log(`File ${url} for template ${template.type} not found!`);
                return;
            }

            template.snaps = MeshUtils.getSnaps(model);
            template.holderPoints = MeshUtils.getFunctionPoints(model, "holder");
            template.attachmentPoints = MeshUtils.getFunctionPoints(model, "attachment");
            MeshUtils.removeHelperPoints(model);

            const box = new Box3();
            box.setFromObject(model);
            const size = box.getSize(new Vector3());
            template.width = Math.round(size.x);
            template.height = Math.round(size.y);
            template.depth = Math.round(size.z);
        }
    }
}