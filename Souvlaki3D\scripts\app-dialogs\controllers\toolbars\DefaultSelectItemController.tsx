import { BaseController } from "../BaseController";
import { MessageHub } from "../../../infrastructure/MessageHub";
import { StateService } from "../../../state/StateService";
import { Events } from "../../../models/infrastructure/Events";
import { ITapInfo } from "../../../models/menu/ITapInfo";

/**
 * This controller marks the clicked item as selected
 * The functionally can be customized by adding conditions to the onItemSelected function
 */
export class DefaultSelectItemController extends BaseController {

    public static $inject = ["StateService"];

    constructor(private state: StateService) {
        super();
        MessageHub.subscribe<ITapInfo>(Events.itemSelected, (info) => this.onItemSelected(info));
    }

    private onItemSelected(info: ITapInfo): void {
        if (this.canSelectItem(info)) {
            const m = info.interaction.movable;
            m.selected = this.state.view.ctrl
                ? !m.selected
                : true;
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected canSelectItem(info: ITapInfo): boolean {
        return true;
    }
}