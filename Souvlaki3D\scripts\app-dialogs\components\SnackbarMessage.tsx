import { Snackbar } from "./Snackbar";
import { dom } from "@immugio/jsx-and-html-utils";

interface IProps {
    text: string;
    value?: number | string;
    autoclose?: number;
    onClick?: (value: number | string) => void;
    onClose?: (value: number | string) => void;
}

export function SnackbarMessage({ value, text, autoclose, onClick, onClose }: IProps): HTMLElement {
    let snackbar: Snackbar;
    const showClose = !autoclose || autoclose > 3000;

    function loaded(element: Snackbar) {
        snackbar = element;
        if (autoclose) {
            setTimeout(() => snackbar.close(), autoclose);
        }
    }

    function onLinkClicked(e: MouseEvent) {
        e.preventDefault();
        snackbar.close();
        onClick?.(value);
    }

    return (
        <Snackbar onLoaded={loaded} onClose={() => onClose?.(value)}>
            <div className="text-center">
                {onClick
                    ? <a href="#" style={{color: "#fff"}} onclick={onLinkClicked}>{text || "Close"}</a>
                    : <span onclick={() => snackbar.close()}>{text || "Close"}</span>
                }
                {showClose && <button type="button" className="close">×</button>}
            </div>
        </Snackbar>
    );
}