import { Line3D, Vec2 } from "@immugio/three-math-extensions";
import { Vec3 } from "@immugio/three-math-extensions";
/**
 * Projects 3d points on the horizontal plane specified by a line and a point outside the line.
 */
export function project3DLinesOnHorizontalPlane(directionXLine, pointOutsideXLine, points) {
    const closest = directionXLine.closestPointToPoint(pointOutsideXLine, false, new Vec3());
    const directionYLine = Line3D.fromPoints(pointOutsideXLine, closest);
    return points.map(p => new Vec2(directionXLine.closestPointToPoint(p, false, new Vec3()).distanceTo(directionXLine.start), directionYLine.closestPointToPoint(p, false, new Vec3()).distanceTo(directionYLine.start)));
}
