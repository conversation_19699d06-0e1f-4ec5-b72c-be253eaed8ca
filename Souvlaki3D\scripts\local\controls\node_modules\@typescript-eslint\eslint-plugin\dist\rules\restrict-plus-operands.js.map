{"version": 3, "file": "restrict-plus-operands.js", "sourceRoot": "", "sources": ["../../src/rules/restrict-plus-operands.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA,+CAAiC;AACjC,8CAAgC;AAShC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,mFAAmF;YACrF,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,OAAO;YACpB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,UAAU,EACR,wEAAwE;YAC1E,UAAU,EACR,2GAA2G;YAC7G,UAAU,EAAE,iDAAiD;SAC9D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,wBAAwB,EAAE;wBACxB,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,wBAAwB,EAAE,KAAK;SAChC;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,wBAAwB,EAAE,CAAC;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAChD,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAIrD;;WAEG;QACH,SAAS,wBAAwB,CAAC,IAAa;YAC7C,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;gBAC1B,OAAO,QAAQ,CAAC;aACjB;YACD,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;gBAC1B,OAAO,QAAQ,CAAC;aACjB;YACD,mBAAmB;YACnB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,aAAa,EAAE;gBAC3C,OAAO,QAAQ,CAAC;aACjB;YACD,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;gBAClB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;gBAEvD,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aACxE;YAED,MAAM,UAAU,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAElD,IACE,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ,EACvB;gBACA,OAAO,UAAU,CAAC;aACnB;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED;;;WAGG;QACH,SAAS,WAAW,CAAC,IAAyB;YAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAEpE,OAAO,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC;QAED,SAAS,iBAAiB,CACxB,IAA+D;YAE/D,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE1C,IACE,QAAQ,KAAK,SAAS;gBACtB,SAAS,KAAK,SAAS;gBACvB,QAAQ,KAAK,SAAS,EACtB;gBACA,IAAI,QAAQ,KAAK,QAAQ,IAAI,SAAS,KAAK,QAAQ,EAAE;oBACnD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,YAAY;qBACxB,CAAC,CAAC;iBACJ;qBAAM,IAAI,QAAQ,KAAK,QAAQ,IAAI,SAAS,KAAK,QAAQ,EAAE;oBAC1D,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,YAAY;qBACxB,CAAC,CAAC;iBACJ;qBAAM;oBACL,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,YAAY;qBACxB,CAAC,CAAC;iBACJ;aACF;QACH,CAAC;QAED,OAAO;YACL,gCAAgC,EAAE,iBAAiB;YACnD,qCAAqC,CAAC,IAAI;gBACxC,IAAI,wBAAwB,EAAE;oBAC5B,iBAAiB,CAAC,IAAI,CAAC,CAAC;iBACzB;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}