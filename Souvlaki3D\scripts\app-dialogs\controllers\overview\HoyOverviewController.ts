import { MessageHub } from "../../../infrastructure/MessageHub";
import { StateService } from "../../../state/StateService";
import { OverviewBaseController } from "./OverviewBaseController";
import { HoyLabelsLogic } from "../../../logic/labels/HoyLabelsLogic";
import { IOverviewItem } from "../../../models/dialogs/IOverviewItem";

export class HoyOverviewController extends OverviewBaseController {

    public static $inject = ["MessageHub", "StateService", "HoyLabelsLogic"];

    constructor(messageHub: MessageHub, state: StateService, private hoyLabelsLogic: HoyLabelsLogic) {
        super(messageHub, state);
    }

    protected getListItems(): IOverviewItem[] {
        return this.hoyLabelsLogic.getList(this.state.design.movableItems);
    }

    protected setPowerStepLabels(): void {
        this.image.innerHTML = "";
        this.qty.innerHTML = "";
    }
}