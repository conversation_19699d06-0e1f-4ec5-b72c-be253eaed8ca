import { CanvasControl } from "../CanvasControl";
import { Mesh, Object3D, Vector3 } from "three";
import { IPoint3D } from "../../../models/geometry/IPoint3D";
import { IRotations } from "../../../models/geometry/IRotations";
import { DotState } from "../../../models/common/Enums";
import { MovableItem } from "../../../models/state/MovableItem";
import { CollidingDot, ConnectedDot, DefaultDot, DisabledDot, HighlightDot, SelectedDot } from "./Dots";
import { axes } from "../../../logic/helpers/geometry/Axes";

export abstract class BaseDotControl extends CanvasControl {

    protected lastPosition: IPoint3D & IRotations;
    protected lastState: DotState;
    protected lastText: string;

    protected dot: Object3D;
    protected label: Mesh;

    protected getPoint(m: MovableItem, p: IPoint3D): Vector3 {
        const position = new Vector3(p.x, p.y, p.z);
        this.rotate(m, position);

        position.x += m.x;
        position.y += m.y;
        position.z += m.z;

        return position;
    }

    protected rotate(m: IRotations, vector: Vector3): void {
        vector.applyAxisAngle(axes.y, m.rotationY * Math.PI / 180);
        vector.applyAxisAngle(axes.x, m.rotationX * Math.PI / 180);
        vector.applyAxisAngle(axes.z, m.rotationZ * Math.PI / 180);
    }

    protected savePosition(m: MovableItem): void {
        this.lastPosition = {
            x: m.x,
            y: m.y,
            z: m.z,
            rotationX: m.rotationX,
            rotationY: m.rotationY,
            rotationZ: m.rotationZ
        };
    }

    protected positionChanged(m: MovableItem): boolean {
        if (m && !this.lastPosition) {
            return true;
        }

        const match = (
            this.lastPosition.x === m.x &&
            this.lastPosition.y === m.y &&
            this.lastPosition.z === m.z &&
            this.lastPosition.rotationX === m.rotationX &&
            this.lastPosition.rotationY === m.rotationY &&
            this.lastPosition.rotationZ === m.rotationZ
        );

        return !match;
    }

    public getHighlight(): Object3D {
        if (this.lastState === DotState.Disabled) {
            return undefined;
        }

        if (!this.cachedHighlight) {
            this.cachedHighlight = HighlightDot(this.highlightMaterial);
        }

        return this.cachedHighlight;
    }

    protected getDot(dotState: DotState): Object3D {
        switch (dotState) {
            case DotState.NonInteractive:
            case DotState.Connected:
                return ConnectedDot();
            case DotState.Colliding:
                return CollidingDot();
            case DotState.Selected:
                return SelectedDot();
            case DotState.Disabled:
                return DisabledDot();
            case DotState.Default:
            default:
                return DefaultDot();
        }
    }

    protected setCursor(dotState: DotState): void {
        this.overCursor = dotState !== DotState.Disabled ? "pointer" : "default";
    }
}