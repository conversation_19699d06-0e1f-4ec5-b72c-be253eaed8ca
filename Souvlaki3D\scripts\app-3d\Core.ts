import { AvailableActions, Display, InteractionType, PlacementMode } from "../models/common/Enums";
import { Events } from "../models/infrastructure/Events";
import { DesignState } from "../models/state/DesignState";
import { IMenuItem } from "../models/menu/IMenuItem";
import { MovableItem } from "../models/state/MovableItem";
import { ITapInfo } from "../models/menu/ITapInfo";
import { IInteractionOptions } from "../models/positioning/IInteractionOptions";
import { Scene } from "../models/scene/Scene";
import { DimensionsLogic } from "../logic/DimensionsLogic";
import { MovablesLogic } from "../logic/MovablesLogic";
import { BuildingLogic } from "../logic/BuildingLogic";
import { ResizingLogic } from "../logic/ResizingLogic";
import { LabelsLogic } from "../logic/labels/LabelsLogic";
import { ZonesLogic } from "../logic/ZonesLogic";
import { PositioningUtils } from "../logic/helpers/PositioningUtils";
import { MovablesUtils } from "../logic/helpers/MovablesUtils";
import { PolygonUtils } from "../logic/helpers/PolygonUtils";
import { CanvasUtils } from "./utils/CanvasUtils";
import { CollisionUtils } from "../logic/helpers/CollisionUtils";
import { Pan } from "./utils/Pan";
import { StateService } from "../state/StateService";
import { MessageHub } from "../infrastructure/MessageHub";
import { AlertService } from "../app-dialogs/services/AlertService";
import { DebugUtils } from "./utils/DebugUtils";
import { Activator } from "../infrastructure/Activator";
import { IMovableAlgorithm } from "../logic/interfaces/IMovableAlgorithm";
import { getMovableInteractionType } from "../logic/helpers/movables/getMovableInteractionType";
import * as Hammer from "hammerjs";
import { shiftWallsToZero } from "../logic/helpers/building/shiftWallsToZero";
import { IController } from "../app-dialogs/controllers/BaseController";
import { Vec2 } from "@immugio/three-math-extensions";
import { IResizeCallback } from "./controls/IResizeCallback";
import { removeMovables } from "../logic/helpers/movables/removeMovables";
import { onVisibleChanged } from "../utils/onVisibleChanged";
import { settings } from "../configurations/setup_global";
import { Modal } from "../app-dialogs/services/Modal";
import { IInsertingEndedEvent } from "../models/events/IInsertingEndedEvent";

export class Core {

    constructor(
        private movablesLogic: MovablesLogic,
        private buildingLogic: BuildingLogic,
        private dimensionsLogic: DimensionsLogic,
        private zonesLogic: ZonesLogic,
        private labelsLogic: LabelsLogic,
        private alerts: AlertService,
        private state: StateService,
        private controllers: IController[],
        private messageHub: MessageHub,
        private scene: Scene
    ) {
        if (this.scene) {
            this.initEventsHandlers();
            const design = MovablesUtils.getDesignStateFromTemplate(settings.buildingTemplates[0]);
            messageHub.broadcast<DesignState>(Events.setState, design);
            this.run();
            messageHub.broadcast<DesignState>(Events.engineReady);
        }
    }

    private setState(d: DesignState): void {
        this.state.initialize(d);
        this.state.view.controlOptions = ResizingLogic.getResizeOptions(this.state.design, this.state.view);
        this.state.view.dimensions = this.dimensionsLogic.getDimensions(this.state.design, this.state.view);

        this.updateViewMode();

        this.scene.center = CanvasUtils.defaultSceneCenter(d.height);
        CanvasUtils.setCamera(this.scene, this.state.view);

        window.requestAnimationFrame(() => CanvasUtils.zoomExtends(this.scene, this.state.view, this.scene.control.zoomObject));
    }

    private pushState(d: DesignState): void {
        this.state.design = d;
        this.state.design.refresh = true;
        this.state.design.movableItems.forEach(x => x.refresh = true);

        this.state.view.offset = this.state.design.offset();
        this.state.view.controlOptions = ResizingLogic.getResizeOptions(this.state.design, this.state.view);
        this.state.view.dimensions = this.dimensionsLogic.getDimensions(this.state.design, this.state.view);
    }

    private startInserting(item: IMenuItem): void {
        const m = MovablesUtils.getInstance(item.type);

        m.data.kelvin = item.kelvin;
        m.data.colorIta = item.colorIta;
        m.data.beam = item.beam;
        m.data.control = item.control;
        m.data.jointFlag = item.jointFlag;
        m.data.power = item.power;
        m.ceilingDistance = item.distanceFromCeiling;
        m.data.attachment = item.attachment;
        m.data.pendantLength = item.pendantLength;

        m.active = true;

        const placements = [...(m.behavior.placementOptions || []), ...(m.behavior.singlePlacementOptions || [])];

        if (m.behavior.maximumCeilingDistance && m.floorDistance) { // Adjust for very high rooms
            const d = this.state.design;

            const ceilingHeight = d.height - d.baseHeight;
            const ceilingDistance = ceilingHeight - (m.floorDistance + m.height);

            if (ceilingDistance > m.behavior.maximumCeilingDistance) {
                m.floorDistance = ceilingHeight - m.behavior.maximumCeilingDistance - m.height;
            }
        }

        if (this.getPoints(m, placements, [m])) {
            this.state.saveHistory();
            this.addMovable(m);
            this.state.view.activeItem = { movable: m, interactionType: InteractionType.InsertingItem };
            this.state.view.dimensions = this.dimensionsLogic.getDimensions(this.state.design, this.state.view);
            const c = this.scene.cameraO;
            this.state.saveMousePosition(0, 0, c.left, c.right, c.top, c.bottom, this.scene.cameraO.zoom);
            this.messageHub.broadcast<IInteractionOptions>(Events.insertingStarted, this.state.view.activeItem);

        } else {
            Modal.showErrorModal(settings.messages.insertError1, settings.messages.insertError2);
        }
    }

    private startDragging() {
        this.state.view.activeItem.children = null;
        const movable = this.state.view.activeItem.movable;
        if (movable.behavior.onDrop) {
            Activator.get<IMovableAlgorithm>(movable.behavior.onDragStart).execute(this.state.design, movable);
        }
        movable.refresh = true;

        // Determine whether to drag group or single item
        let connected = this.movablesLogic.getConnectedItems(this.state.design.movableItems, movable);
        const detached = connected.filter(x => x.movable.detached).map(x => x.movable.id);
        const isAttachment = movable.placementMode === PlacementMode.Attachment;
        const children = this.state.design.movableItems.filter(x => x.parentId === movable.id);
        const isCorner = connected.some(x => x.movable.placementMode === PlacementMode.Corners);

        // TODO: Dragging square track should drag its children too but nothing else
        let single = false;
        if (connected.length === 1 || isAttachment || isCorner || (movable.detached && (!children.length && detached.length === 1))) {
            single = true;
            // TODO: This is a temporary solution that removes the children of the square track, needs to be improved so that the group of children is dragged together instead of removing them
            children.forEach(x => this.state.design.movableItems.splice(this.state.design.movableItems.indexOf(x), 1));

        } else if (movable.detached && (children.length || detached.length > 1)) {
            single = false;
            connected = connected.filter(x => detached.includes(x.movable.id) || detached.includes(x.movable.parentId));
        }

        if (single) {
            const b = movable.behavior;
            this.getPoints(movable, [...(b.placementOptions || []), ...(b.singlePlacementOptions || [])], [movable]);

        } else {
            const { group, placements, contains } = this.movablesLogic.createPositionGroup(this.state.design, connected);

            this.addMovable(group);
            this.scene.control.update(this.state);

            movable.active = false;

            const control = this.scene.control.movableControls.find(x => x.interaction.movable.id === group.id);
            this.state.view.activeItem = control.interaction;
            this.state.view.activeItem.children = contains;

            this.getPoints(group, placements, [group, ...connected.map(x => x.movable)]);
        }

        this.state.view.activeItem.interactionType = InteractionType.DraggingItem;
    }

    private getPoints(dragged: MovableItem, placements: PlacementMode[], draggedGroup: MovableItem[]): boolean {
        const snaps = PositioningUtils.getSnapOptions(this.state.design, draggedGroup);
        const isLarge = this.isLargeGroup(dragged, draggedGroup);

        this.state.view.zones = this.zonesLogic.getZones(this.state.design, this.state.view, dragged, placements, snaps, !isLarge);
        if (!this.state.view.zones.length) {
            return false;
        }

        this.state.view.snaps = snaps.free;
        this.state.view.inserts = snaps.taken;
        this.state.view.forbidden = snaps.forbidden;

        return true;
    }

    private isLargeGroup(dragged: MovableItem, draggedGroup: MovableItem[]): boolean {
        if (draggedGroup.length > 4) {
            return true;
        }

        const roomWidth = this.state.design.extWidth();
        const roomDepth = this.state.design.extDepth();

        return Math.max(dragged.width, dragged.depth) > Math.min(roomWidth, roomDepth);
    }

    private onResize(resizeCallback: IResizeCallback): void {
        const cancel = ResizingLogic.resize(this.state.design, this.state.view, resizeCallback.interaction, resizeCallback.diff);
        if (cancel) {
            this.onRelease(null, null);
        }

        this.state.view.dimensions = this.dimensionsLogic.getDimensions(this.state.design, this.state.view);
        MessageHub.broadcast(Events.dragResize, this.state.design);
    }

    private addMovable(m: MovableItem): void {
        this.state.design.movableItems.push(m);
        if (m.parentId) {
            const parent = this.state.design.movableItems.find(i => i.id === m.parentId);
            parent.refresh = true;
        }
    }

    private updateViewMode(): void {
        const v = this.state.view;

        if (v.displayMode & Display.PerspectiveView) {
            CanvasUtils.toPerspective(this.scene);

        } else {
            CanvasUtils.toOrtho(this.scene);
        }

        this.state.design.movableItems.forEach(mi => mi.refresh = true);
        this.state.view.controlOptions = ResizingLogic.getResizeOptions(this.state.design, this.state.view);
        v.closesElevations = this.buildingLogic.getClosesElevations(v.phi, v.theta);
        v.dimensions = this.dimensionsLogic.getDimensions(this.state.design, v);

        this.update();
        CanvasUtils.zoomExtends(this.scene, this.state.view, this.scene.control.zoomObject);
    }

    private initEventsHandlers(): void {
        this.messageHub.subscribe(Events.zoomIn, () => this.zoom(1));
        this.messageHub.subscribe(Events.zoomOut, () => this.zoom(-1));
        this.messageHub.subscribe<IMenuItem>(Events.startInserting, (menuItem) => this.startInserting(menuItem));
        this.messageHub.subscribe<DesignState>(Events.setState, (d) => this.setState(d));
        this.messageHub.subscribe<DesignState>(Events.pushState, d => this.pushState(d));
        this.messageHub.subscribe(Events.updateViewMode, () => this.updateViewMode());
        this.messageHub.subscribe<{ movables: MovableItem[], clockwise: boolean }>(Events.movableRotate, (e) => this.rotateItem(e.movables, e.clockwise));

        MessageHub.subscribe<IResizeCallback>(Events.dragResizing, resizeCallback => this.onResize(resizeCallback));

        onVisibleChanged(this.scene.renderer.domElement, () => this.setSceneSize());
        window.addEventListener("resize", () => this.setSceneSize());
        window.addEventListener("orientationchange", () => this.setSceneSize());

        window.addEventListener("keydown", (e) => this.state.view.ctrl = e.metaKey || e.ctrlKey);
        window.addEventListener("keyup", () => this.state.view.ctrl = false);

        this.scene.renderer.domElement.addEventListener("mousemove", e => this.onMouseMove(e));
        this.scene.renderer.domElement.addEventListener("mousedown", e => this.onRightClick(e));
        this.scene.renderer.domElement.addEventListener("contextmenu", e => e.preventDefault());
        window.addEventListener("mouseup", e => this.onRightMouseUp(e));

        this.scene.container.addEventListener("wheel", e => this.onMouseWheel(e));

        const touchDevice = !!(navigator.maxTouchPoints || "ontouchstart" in document.documentElement);

        const dragRoot = document.getElementById(settings.app.dragRootId);
        const mc = new Hammer.Manager(dragRoot, {
            recognizers: [
                [Hammer.Press, { enable: true, threshold: 10, time: 0 }],
                [Hammer.Pan, { enable: true, threshold: touchDevice ? 10 : 0 }]
            ]
        });

        mc.on("press", e => this.onTouch(e));
        mc.on("panstart", e => this.onTouch(e));
        mc.on("pressup", e => window.setTimeout(() => this.onRelease(e, Vec2.fromPoint(e.center)), 10));
        mc.on("pan", e => e.isFinal ? this.onRelease(e, Vec2.fromPoint(e.center)) : this.onDrag(e, Vec2.fromPoint(e.center)));
        mc.add(new Hammer.Pinch({ threshold: 0 })).recognizeWith(mc.get("pan"));
        mc.on("pinch", e => this.onZoom(e));
    }

    private setSceneSize(): void {
        CanvasUtils.setSceneSize(this.scene);
        CanvasUtils.zoomExtends(this.scene, this.state.view, this.scene.control.zoomObject);
    }

    private onMouseWheel(e: WheelEvent): void {
        e.preventDefault();
        const delta = Math.max(-1, Math.min(1, -e.deltaY));
        this.zoom(delta);
    }

    private zoom(delta: number): void {
        this.state.view.radius += delta < 0 ? settings.interaction.zoomStep : -settings.interaction.zoomStep;
        this.scene.cameraO.zoom += delta < 0 ? -0.05 : 0.05;

        CanvasUtils.limitMinMaxZoom(this.scene, this.state.view);
        CanvasUtils.setCamera(this.scene, this.state.view);
    }

    private rotateItem(movs: MovableItem[], clockwise: boolean): void {
        this.state.saveHistory();
        this.movablesLogic.rotate(this.state.design, movs, clockwise);
    }

    private onMouseMove(e: MouseEvent): void {
        e.preventDefault();

        if (!this.state.view.activeItem) {
            // Only set when there is no active item so that hover effect is not triggered while already interacting with an item
            this.state.view.rawMousePosition = { x: e.pageX, y: e.pageY };

            const c = this.state.view.hoveredItem;
            const nc = CanvasUtils.handleObjectHover(this.scene, this.state.view.rawMousePosition, c, this.state.view.allowedInteractions);
            this.state.view.hoveredItem = nc;
            if (c !== nc) {
                this.messageHub.broadcast(Events.hoverItem);
            }

        } else if (this.state.view.activeItem.interactionType === InteractionType.PanningView) {
            Pan.pan(e.pageX, e.pageY, this.scene.camera, this.scene.center);
        }
    }

    private onRightClick(e: MouseEvent): void {
        if (e.buttons === 2) {
            Pan.start(e.pageX, e.pageY, this.scene.renderer.domElement);
            this.state.view.activeItem = { interactionType: InteractionType.PanningView };
        }
    }

    private onRightMouseUp(e: MouseEvent): void {
        if (e.button === 2 && this.state.view.activeItem) {
            this.cancel();
        }
    }

    private onZoom(e: HammerInput): void {
        e.preventDefault();

        if (!this.state.view.onMouseDownPos) {
            const c = this.scene.cameraO;
            this.state.saveMousePosition(0, 0, c.left, c.right, c.top, c.bottom, this.scene.cameraO.zoom);
            Pan.start(e.center.x, e.center.y, this.scene.renderer.domElement);
        }

        Pan.pan(e.center.x, e.center.y, this.scene.camera, this.scene.center);

        this.state.view.activeItem = { interactionType: InteractionType.ZoomingView };

        this.state.view.radius = this.state.view.onMouseDownPos.radius / e.scale;
        this.scene.cameraO.zoom = this.state.view.onMouseDownPos.zoom * e.scale;

        CanvasUtils.limitMinMaxZoom(this.scene, this.state.view);
        CanvasUtils.setCamera(this.scene, this.state.view);
    }

    private onDrag(e: HammerInput | DragEvent, position: Vec2): void {
        if (!this.state.view.activeItem) return;
        e.preventDefault();

        this.state.view.rawMousePosition = position;

        const moveType = (InteractionType.Movable | InteractionType.DraggingItem | InteractionType.InsertingItem);
        if ((this.state.view.activeItem.interactionType & moveType) && (this.state.view.isActionAvailable(AvailableActions.Move, [this.state.view.activeItem.movable]))) { // Dragging things ....

            // Delayed dragging initialization - do not start dragging in onTouch - wait till this point, this is after onTouch and first onDrag
            if (this.state.view.activeItem.interactionType & InteractionType.Movable) {
                this.state.saveHistory();
                this.startDragging();
                this.messageHub.broadcast<IInteractionOptions>(Events.draggingStarted, this.state.view.activeItem);
                if (this.state.view.hoveredItem) {
                    this.state.view.hoveredItem = null;
                    this.messageHub.broadcast(Events.hoverItem);
                }

            } else { // Updates during dragging
                this.tryInsertRevert();
                this.tryInsert();
                this.tryReplace();
            }

        } else if (this.state.view.activeItem.interactionType === InteractionType.RotatingView && "deltaX" in e) {
            this.rotateView(e);
        }
    }

    private onTouch(e: HammerInput): void {
        e.preventDefault();

        DebugUtils.showFaceInfo(e);

        if (
            (this.state.view.activeItem && !this.state.view.ctrl) ||
            e.target !== this.scene.renderer.domElement
        ) return;

        const hovered = CanvasUtils.objectFromMouse(this.scene, e.center.x, e.center.y, this.state.view.allowedInteractions);
        const c = this.scene.cameraO;
        this.state.saveMousePosition(0, 0, c.left, c.right, c.top, c.bottom, this.scene.cameraO.zoom);

        if (this.state.view.ruler || this.state.view.rectangleSelect) {
            return;
        }

        if (!this.state.view.ctrl) {
            this.state.design.movableItems.forEach(it => it.selected = false);
        }

        if (!hovered || !hovered.interaction) { // No item - rotating view
            this.state.view.activeItem = { interactionType: InteractionType.RotatingView };
            this.messageHub.broadcast(Events.drawingInteraction, this.state.view.activeItem);
            return;
        }

        if (settings.app.debug) {
            console.log(hovered.interaction?.movable?.type, hovered.interaction);
        }

        this.state.view.activeItem = hovered.interaction;

        switch (hovered.interaction.interactionType) {
            case InteractionType.Attachment:
            case InteractionType.Track:
            case InteractionType.Movable:
                this.state.view.activeItem.movable.active = true;
                this.state.saveMousePosition(e.center.x, e.center.y, c.left, c.right, c.top, c.bottom, this.scene.cameraO.zoom);
                break;

            case InteractionType.ResizeBuilding:
            case InteractionType.ResizeHeight:
                if (hovered.interaction.interactionType === InteractionType.ResizeBuilding)
                    this.state.view.offset = this.state.design.offset();
                break;
        }

        this.messageHub.broadcast(Events.drawingInteraction, hovered.interaction);
    }

    private onRelease(e: Event | HammerInput, position: Vec2): void {
        e?.preventDefault();

        this.state.view.zones = undefined;
        this.state.view.rawMousePosition = undefined;

        if (!this.state.view.activeItem) return;
        const inserting = this.state.view.activeItem.interactionType === InteractionType.InsertingItem;

        switch (this.state.view.activeItem.interactionType) {
            case InteractionType.InsertingItem:
            case InteractionType.DraggingItem: {
                const m = this.state.view.activeItem.movable;
                this.state.design.movableItems.forEach(mi => mi.refresh = true);
                this.state.view.activeItem.interactionType = getMovableInteractionType(this.state.view.activeItem.movable);

                const intersection = CanvasUtils.objectFromMouse(this.scene, position.x, position.y, InteractionType.Building | InteractionType.Movable);
                if (!intersection) {
                    const children = (this.state.view.activeItem.children || []).map(x => x.movable); // Remove when cursor outside the drawing
                    const toRemove = [m].concat(children);
                    removeMovables(this.state.design, toRemove);
                }

                if (inserting) {
                    this.messageHub.broadcast(Events.requestHeight, m);
                    this.messageHub.broadcast<IInsertingEndedEvent>(Events.insertingEnded, {success: !!intersection, item: this.state.view.activeItem});
                }

                if (m.behavior.onDrop) {
                    Activator.get<IMovableAlgorithm>(m.behavior.onDrop).execute(this.state.design, m);
                }
                break;
            }

            case InteractionType.ResizeBuilding:
                this.state.design.walls = shiftWallsToZero(this.state.design.walls);
                this.state.design.movableItems.forEach(mi => mi.refresh = true);
                break;

            case InteractionType.Attachment:
            case InteractionType.Track: {
                const hovered = CanvasUtils.objectFromMouse(this.scene, position.x, position.y, this.state.view.allowedInteractions);
                if (hovered && hovered.interaction === this.state.view.activeItem) {
                    const info: ITapInfo = { ...position, interaction: this.state.view.activeItem };
                    this.messageHub.broadcast(Events.itemSelected, info);
                }
                break;
            }

            case InteractionType.ChooseLighting:
                this.messageHub.broadcast(Events.optionsSelected, this.state.view.activeItem);
                break;
        }

        this.cancel();
        MessageHub.broadcast(Events.requestDragEnd);
        this.trim();
    }

    private trim(): void {
        this.update();

        const remove = [
            ...CollisionUtils.outside(this.state.design, 1),
            ...this.movablesLogic.getOverSuspended(this.state.design)
        ];

        if (remove.length) {
            removeMovables(this.state.design, remove);
        }
    }

    private cancel(): void {
        if (this.state.view.activeItem.movable) this.state.view.activeItem.movable.active = false;
        if (this.state.view.activeItem.control) this.state.view.activeItem.control.mouseOut();
        this.state.design.refresh = true;
        this.state.view.controlOptions = ResizingLogic.getResizeOptions(this.state.design, this.state.view);
        this.state.view.activeItem = undefined;
        this.state.view.dimensions = this.dimensionsLogic.getDimensions(this.state.design, this.state.view);
        this.state.view.offset = this.state.design.offset();
        this.state.view.swapped = null;
        this.state.view.insertPoint = null;
        this.state.view.insertState = null;
        this.messageHub.broadcast(Events.drawingInteraction);
        this.state.view.onMouseDownPos = null;
        this.state.design.movableItems.forEach(it => it.detached = false);

        const group = this.state.design.movableItems.find(x => x.type === "Group");
        if (group) {
            removeMovables(this.state.design, [group]);
        }

        this.movablesLogic.suspendedToSmd(this.state.design);
    }

    private tryReplace(): void {
        if (this.state.view.insertState) {
            return;
        }

        const dragged = this.state.view.activeItem.movable;
        const itemToReplace = this.movablesLogic.canReplace(this.state.design, dragged);
        if (itemToReplace && !this.state.view.swapped) {
            this.movablesLogic.swap(this.state.design, itemToReplace, dragged);
            this.state.view.swapped = [itemToReplace, dragged];
            this.state.view.activeItem.movable.active = false;
            this.state.view.activeItem = null;

            this.scene.control.update(this.state);
            const control = this.scene.control.movableControls.find(x => x.interaction.movable.id === itemToReplace.id);
            this.state.view.activeItem = control.interaction;
            this.startDragging();

        } else if (this.state.view.activeItem && this.state.view.swapped) {
            const dist = PolygonUtils.pointsDistance(this.state.view.swapped[0], this.state.view.swapped[1]);
            if (dist > 200 && dist < 400) {
                this.state.view.swapped = null;
            }
        }
    }

    private tryInsertRevert() {
        if (!this.state.view.insertState) {
            return;
        }

        const m = this.state.view.activeItem.movable;
        const max = Math.max(m.width, m.depth, m.height);

        const distance = PolygonUtils.pointsDistance(m, this.state.view.insertPoint);
        if (distance > max) {
            this.movablesLogic.copyPositions(this.state.design, this.state.view.insertState);
            this.restartDragging(false);

            this.state.view.insertState = null;
            this.state.view.insertPoint = null;
        }
    }

    private tryInsert(): void {
        if (this.state.view.activeItem.children || this.state.view.insertState) {
            return;
        }

        const dragged = this.state.view.activeItem.movable;
        const insertPoints = this.movablesLogic.canInsert(this.state.view.inserts, dragged);
        if (insertPoints) {
            this.state.view.insertState = this.state.design.movableItems.filter(x => x.id !== dragged.id).map(x => x.clone());
            this.state.view.insertPoint = insertPoints[0];
            this.movablesLogic.insert(this.state.design, dragged, insertPoints);

            if (CollisionUtils.outside(this.state.design, .001).length) {
                this.movablesLogic.copyPositions(this.state.design, this.state.view.insertState);
                this.state.view.insertState = null;
                this.state.view.insertPoint = null;

            } else {
                this.restartDragging(true);
            }
        }
    }

    private restartDragging(detached: boolean): void {
        const active = this.state.view.activeItem;
        this.state.view.activeItem = null;
        this.scene.control.update(this.state);
        this.state.view.activeItem = active;
        this.state.view.activeItem.movable.detached = detached;
        this.startDragging();
    }

    private rotateView(e: HammerInput): void {
        if (this.scene.inPerspectiveMode) {
            this.state.view.theta = -(e.deltaX / 2) + this.state.view.onMouseDownPos.theta;
            this.state.view.phi = (e.deltaY / 2) + this.state.view.onMouseDownPos.phi;

            if (this.state.view.phi < -90) {
                this.state.view.phi = -90;
            }
            if (this.state.view.phi > 90) {
                this.state.view.phi = 90;
            }

            this.state.view.closesElevations = this.buildingLogic.getClosesElevations(this.state.view.phi, this.state.view.theta);
            this.state.view.displayMode = Display.PerspectiveView;

            // Rotation axis for camera target
            const axis = this.scene.camera.position.clone().cross(this.scene.center).normalize();

            // Angle between the camera target and camera and
            const angle = this.scene.camera.position.angleTo(this.scene.center);

            // Position camera based on the new view angle
            CanvasUtils.setCamera(this.scene, this.state.view);

            // After the camera has been positioned also move the camera target, this will make the scene rotate around the bulding center instead the scene center
            if (Math.abs(this.scene.center.x + this.scene.center.z) > Number.EPSILON) {

                const newAngle = this.scene.camera.position.angleTo(this.scene.center);
                const diff = (angle - newAngle);
                const y = this.scene.center.y;
                this.scene.center.applyAxisAngle(axis, diff);

                if (e.direction === 2 || e.direction === 4) { // When dragging left or right fix the camera height
                    this.scene.center.y = y;
                }

                this.scene.camera.lookAt(this.scene.center);
            }
        }
    }

    private update(): void {
        this.state.compare();

        this.scene.control.update(this.state); // Update 3D
        this.controllers.forEach(x => x.update?.(this.state)); // Update Html

        this.state.setPreviousState();
    }

    private run(): void {
        while (this.state.applyUpdate()) {
            this.update();
        }

        this.update();

        requestAnimationFrame(() => this.run());
    }
}