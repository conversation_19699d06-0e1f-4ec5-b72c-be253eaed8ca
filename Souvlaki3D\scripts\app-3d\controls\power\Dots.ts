import { Material, SphereGeometry } from "three";
import { MaterialUtils } from "../../utils/MaterialUtils";
import { Mesh3D } from "../../graphics/Mesh3D";

const size = 35;
const hoverGeo = new SphereGeometry(size + 2, 32);
const outerGeo = new SphereGeometry(size, 32);
const inner = new SphereGeometry(size * .6, 32);

export function HighlightDot(material: Material): Mesh3D {
    return new Mesh3D(
        hoverGeo,
        material
    );
}

export const ConnectedDot = (): Mesh3D => DotWithInnerDot("powerActiveOuter", "powerConnected");
export const SelectedDot = (): Mesh3D => DotWithInnerDot("powerActiveOuter", "powerActive");
export const DefaultDot = (): Mesh3D => Dot("powerDefault");
export const DisabledDot = (): Mesh3D => Dot("powerDisabled");
export const CollidingDot = (): Mesh3D => Dot("powerColliding");

function Dot(key: string): Mesh3D {
    return new Mesh3D(
        outerGeo,
        MaterialUtils.getMaterial(key)
    ).setRenderOrder(-1);
}

function DotWithInnerDot(outerMaterial: string, innerMaterial: string): Mesh3D {
    return new Mesh3D(
        outerGeo,
        MaterialUtils.getMaterial(outerMaterial)
    ).add(
        new Mesh3D(
            inner,
            MaterialUtils.getMaterial(innerMaterial)
        )
    ).setRenderOrder(-1);
}