import { dom } from "@immugio/jsx-and-html-utils";
import { ILabelInfo } from "../../models/dialogs/IOverviewItem";
import { ContextActions } from "../../models/infrastructure/Events";
import { ContextItem } from "../../models/menu/ContextItem";

export type ContextMenu = HTMLElement;

interface Props {
    actions: ContextItem[];
    onClick: (action: ContextItem) => void;
    summary?: { description: string, labels: ILabelInfo[] };
    position: { x: number, y: number };
}

export function ContextMenu({ actions, onClick, summary, position }: Props): ContextMenu {
    const root = (
        <div className="context-menu">
            {actions.map(item => (
                <div dataset={{ tooltipAction: ContextActions[item.action] }} title={item.description} className={`tooltip-item ${ContextActions[item.action]}`} onclick={() => onClick(item)}/>
            ))}
            {!!summary && <div className="context-summary">
                <p>{summary.description}</p>
                <p>{summary.labels.map(label =>
                    <span className="prop-label" title={label.title} style={{ backgroundColor: label.color || "#FFF" }}>{label.text}</span>)}
                </p>
            </div>}
        </div>
    );

    document.body.appendChild(root);

    const width = root.offsetWidth;
    const top = position.y - root.offsetHeight - 30;

    let left = position.x - width / 2;
    if (left + width > window.innerWidth) {
        left = window.innerWidth - width;
    }

    root.style.left = left + "px";
    root.style.top = top + "px";

    return root;
}