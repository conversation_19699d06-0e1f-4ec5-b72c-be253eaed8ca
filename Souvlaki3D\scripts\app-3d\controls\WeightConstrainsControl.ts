import { CanvasControl } from "./CanvasControl";
import { IInteractionOptions } from "../../models/positioning/IInteractionOptions";
import { Events } from "../../models/infrastructure/Events";
import { MessageHub } from "../../infrastructure/MessageHub";
import { IApplicationState } from "../../models/state/IApplicationState";
import { PlacementMode } from "../../models/common/Enums";
import { Mesh, SphereGeometry } from "three";
import { getMaterial } from "../utils/MaterialUtils";
import { WeightConstrainsHelper } from "../../logic/helpers/positioning/WeightConstrainsHelper";

export class WeightConstrainsControl extends CanvasControl {

    public static $inject = ["StateService", "MessageHub"];

    constructor(private state: IApplicationState, messageHub: MessageHub) {
        super();
        messageHub.subscribe<IInteractionOptions>(Events.drawingInteraction, () => this.onDrawingInteraction());
        messageHub.subscribe<IInteractionOptions>([Events.draggingStarted, Events.insertingStarted], o => this.showWeightConstrains(o));
    }

    private showWeightConstrains(o: IInteractionOptions) {
        if (o.movable.placementMode !== PlacementMode.Attachment) {
            return;
        }
        const weightChunks = WeightConstrainsHelper.getWeightChunks(this.state.design.movableItems, o.movable);

        this.object3D.position.set(-this.state.view.offset.x, 0, -this.state.view.offset.y);
        if (this.object3D.children.length) {
            this.clearChildren();
        }

        for (const weightChunk of weightChunks) {
            const mesh = new Mesh(
                new SphereGeometry(20, 16, 16),
                getMaterial(weightChunk.allowed ? "underweight" : "overweight")
            );
            mesh.renderOrder = 10;
            mesh.position.copy(weightChunk.line.center);
            this.object3D.add(mesh);
        }
    }

    private onDrawingInteraction() {
        this.clearChildren();
    }
}