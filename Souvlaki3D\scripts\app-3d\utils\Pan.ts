import { Camera, OrthographicCamera, PerspectiveCamera, Vector3 } from "three";

export class Pan {

    private static lastPos: { x: number, y: number };
    private static element: HTMLElement;
    private static panOffset = new Vector3();
    private static parallel: boolean = true;

    public static start(x: number, y: number, element: HTMLElement): void {
        this.lastPos = { x, y };
        this.element = element;
    }

    public static pan(x: number, y: number, camera: Camera, target: Vector3): void {
        const deltaX = x - this.lastPos.x;
        const deltaY = y - this.lastPos.y;
        this.lastPos = { x, y };

        const offset = new Vector3();

        this.panOffset.set(0, 0, 0);

        if (camera instanceof PerspectiveCamera) {
            const position = camera.position;
            offset.copy(position).sub(target);
            let targetDistance = offset.length();

            // Half of the fov is center to top of screen
            targetDistance *= Math.tan((camera.fov / 2) * Math.PI / 180.0);

            this.panLeft(2 * deltaX * targetDistance / this.element.clientHeight, camera);
            this.panUp(2 * deltaY * targetDistance / this.element.clientHeight, camera);

            camera.position.add(this.panOffset);
            target.add(this.panOffset);

        } else if (camera instanceof OrthographicCamera) {
            const xM = deltaX * (camera.right - camera.left) / camera.zoom / this.element.clientWidth;
            const yM = deltaY * (camera.top - camera.bottom) / camera.zoom / this.element.clientHeight;

            camera.left -= xM;
            camera.right -= xM;

            camera.top += yM;
            camera.bottom += yM;

            camera.updateProjectionMatrix();
        }
    }

    private static panLeft(distance: number, camera: Camera) {
        const v = new Vector3();
        v.setFromMatrixColumn(camera.matrix, 0); // get X column of objectMatrix
        v.multiplyScalar(-distance);

        this.panOffset.add(v);
    }

    private static panUp(distance: number, camera: Camera) {
        const v = new Vector3();

        if (this.parallel) {
            v.setFromMatrixColumn(camera.matrix, 1);

        } else {
            v.setFromMatrixColumn(camera.matrix, 0);
            v.crossVectors(camera.up, v);
        }
        v.multiplyScalar(distance);

        this.panOffset.add(v);
    }
}