﻿import { Scene } from "../../models/scene/Scene";
import { IIntersectedObject } from "../../models/scene/IIntersectedObject";
import { InteractionType, Attributes, Display } from "../../models/common/Enums";
import { IInteractionOptions } from "../../models/positioning/IInteractionOptions";
import { ViewState } from "../../models/state/ViewState";
import { IPoint2D } from "../../models/geometry/IPoint2D";
import { IPoint3D } from "../../models/geometry/IPoint3D";
import { toRad } from "../../logic/helpers/PositioningUtils";
import { SceneControl } from "../controls/SceneControl";
import { CanvasControl } from "../controls/CanvasControl";
import { StateService } from "../../state/StateService";
import { axes } from "../../logic/helpers/geometry/Axes";
import { RGBELoader } from "three/examples/jsm/loaders/RGBELoader";
import { RoomEnvironment } from "three/examples/jsm/environments/RoomEnvironment";
import {
    Raycaster,
    Vector3,
    WebGLRenderer,
    PerspectiveCamera,
    OrthographicCamera,
    Object3D,
    Intersection,
    Box3,
    Sphere,
    Scene as ThreeScene,
    ColorManagement,
    sRGBEncoding,
    ACESFilmicToneMapping,
    EquirectangularReflectionMapping,
    FloatType,
    Texture,
    PMREMGenerator
} from "three";
import { autogenerated, EnvironmentMapKeys, environmentMaps } from "../../models/state/EnvironmentOptions";
import { settings } from "../../configurations/setup_global";

export class CanvasUtils {

    private static raycaster = new Raycaster();
    private static environment: Texture;

    public static getEnvironment(renderer: WebGLRenderer, envMapKey: EnvironmentMapKeys): Texture {
        if (this.environment?.name === envMapKey) {
            return this.environment;
        }

        if (envMapKey === autogenerated) {
            const environment = new RoomEnvironment();
            const pmremGenerator = new PMREMGenerator( renderer );
            this.environment = pmremGenerator.fromScene( environment ).texture;
            this.environment.name = envMapKey;
            return this.environment;
        }

        const loader = new RGBELoader().setDataType(FloatType);
        this.environment = loader.load(environmentMaps[envMapKey], texture => {
            texture.mapping = EquirectangularReflectionMapping;
            texture.name = envMapKey;
        });

        return this.environment;
    }

    public static createScene(container: HTMLElement, width: number, height: number, canvasId: string, control: boolean, scene: Scene, envMapKey: EnvironmentMapKeys): Scene {
        ColorManagement["enabled"] = true;

        scene.container = container;
        scene.center = new Vector3();

        try {
            scene.renderer = new WebGLRenderer({ antialias: true, alpha: true });
        }
        catch (ex) {
            return undefined;
        }

        scene.renderer.outputEncoding = sRGBEncoding;
        scene.renderer.toneMapping = ACESFilmicToneMapping;

        scene.renderer.setPixelRatio(window.devicePixelRatio);

        scene.renderer.setClearColor(settings.view.backgroundColor, settings.view.backgroundAlpha);
        scene.renderer.domElement.id = canvasId;
        scene.renderer.setSize(width || scene.container.clientWidth, height || scene.container.clientHeight);
        scene.container.insertAdjacentElement("afterbegin", scene.renderer.domElement);

        scene.cameraP = new PerspectiveCamera(45, (width || scene.container.clientWidth) / (height || scene.container.clientHeight), 100, 50000);
        scene.cameraO = new OrthographicCamera(-1, 1, 1, -1, 0, 300000);

        this.toPerspective(scene);

        if (control) {
            scene.control = new SceneControl(settings);
            scene.control.object3D.environment = this.getEnvironment(scene.renderer,  envMapKey);
            scene.control.object3D.add(scene.cameraP);
            scene.control.object3D.add(scene.cameraO);
            scene.center =  this.defaultSceneCenter(settings.sizes.totalDefaultHeight);
            this.setSceneSize(scene);
        }

        return scene;
    }

    public static setSceneSize(scene: Scene): void {
        const width = scene.container.clientWidth;
        const height = scene.container.clientHeight;

        scene.renderer.setSize(width, height);
        scene.cameraP.aspect = width / height;
        scene.cameraP.updateProjectionMatrix();

        const ratio = 5;
        scene.cameraO.left = width * -ratio;
        scene.cameraO.right = width * ratio;
        scene.cameraO.top = height * ratio;
        scene.cameraO.bottom = height * -ratio;
        scene.cameraO.updateProjectionMatrix();
        scene.cameraO.zoom = 1;

        if (scene.container.offsetTop === 0) { // Wrong value on IE11, use bounding rec
            const rec = scene.container.getBoundingClientRect();
            scene.offset = { x: rec.left, y: rec.top };
        } else {
            scene.offset = { x: scene.container.offsetLeft, y: scene.container.offsetTop };
        }
    }

    public static objectFromMouse(scene: Scene, pagex: number, pagey: number, allowed: InteractionType): IIntersectedObject {
        const intersects = this.intersectObjects(scene, pagex, pagey);

        for (const intersected of intersects) {

            const normal = (intersected && intersected.face) ? intersected.face.normal : null;

            const obj = this.findObjectFromIntersected(intersected, intersected.point, normal, allowed);

            if (obj !== null) return obj;
        }

        return null;
    }

    public static intersectObjects(scene: Scene, pageX: number, pageY: number, objects?: Object3D[]): Intersection[] {
        const eltX = pageX - scene.offset.x; // Translate page coords to element coords
        const eltY = pageY - scene.offset.y;

        const x = (eltX / scene.renderer.domElement.clientWidth) * 2 - 1; // Translate client coords into viewport x,y
        const y = - (eltY / scene.renderer.domElement.clientHeight) * 2 + 1;

        this.raycaster.setFromCamera({ x, y }, scene.camera);

        return this.raycaster.intersectObjects(objects || scene.control.object3D.children, true);
    }

    private static findObjectFromIntersected(intersection: any, point: Vector3, normal: Vector3, allowed: InteractionType): IIntersectedObject {
        if (intersection.face && intersection.face.interaction) {
            return { interaction: intersection.face.interaction, normal: normal, point: point };
        }

        const object = intersection.object ? intersection.object : intersection;

        if (object && object.interaction && (object.interaction.interactionType & allowed)) {
            return object.interaction.interactionType !== InteractionType.Highlight
                ? { interaction: object.interaction, normal: normal, point: point }
                : null;

        } else {
            return object.parent ? this.findObjectFromIntersected(object.parent, point, normal, allowed) : null;
        }
    }

    public static limitMinMaxZoom(scene: Scene, v: ViewState): void {
        if (v.radius > settings.interaction.maxDistPerspective) v.radius = settings.interaction.maxDistPerspective;
        if (v.radius < settings.interaction.minDistPerspective) v.radius = settings.interaction.minDistPerspective;

        if (scene.cameraO.zoom > settings.interaction.maxZoomOrtho) scene.cameraO.zoom = settings.interaction.maxZoomOrtho;
        if (scene.cameraO.zoom < settings.interaction.minZoomOrtho) scene.cameraO.zoom = settings.interaction.minZoomOrtho;
    }

    public static setCamera(scene: Scene, v: { theta: number, phi: number, radius: number, directionalLightVector?: IPoint3D }): void {
        const degree = Math.PI / 180;

        const position = new Vector3(
            v.radius * Math.sin(v.theta * degree) * Math.cos(v.phi * degree),
            v.radius * Math.sin(v.phi * degree),
            v.radius * Math.cos(v.theta * degree) * Math.cos(v.phi * degree)
        );

        scene.cameraP.position.copy(position).add(scene.center);
        scene.cameraP.lookAt(scene.center);

        scene.cameraO.position.copy(position);
        if (Math.abs(scene.cameraO.position.length()) < 4000) {
            // When the orthographic camera gets too close, some elements might not be visible
            scene.cameraO.position.multiplyScalar(2);
        }
        scene.cameraO.lookAt(new Vector3());
        scene.cameraO.updateProjectionMatrix();

        if (v.directionalLightVector) {
            v.directionalLightVector = { x: scene.cameraP.position.x, y: scene.cameraP.position.y, z: scene.cameraP.position.z };
        }
    }

    public static toOrtho(scene: Scene): void {
        scene.camera = scene.cameraO;
        scene.inPerspectiveMode = false;
    }

    public static toPerspective(scene: Scene): void {
        scene.camera = scene.cameraP;
        scene.inPerspectiveMode = true;
    }

    public static getSceneBoxSize(o: Object3D): Box3 {
        const c = o.clone();
        let r = c.getObjectByName(Attributes.ZoomIgnore);
        while (r && r.parent) {
            r.parent.remove(r);
            r = c.getObjectByName(Attributes.ZoomIgnore);
        }

        return new Box3().setFromObject(c);
    }

    public static zoomExtends(scene: Scene, v: ViewState, o: Object3D, scaleRatio?: number): void {
        const box = this.getSceneBoxSize(o),
            radius = box.getBoundingSphere(new Sphere()).radius,
            fov = scene.cameraP.fov * (Math.PI / 180);

        const size = radius / (scaleRatio || settings.view.scaleRatio);

        v.radius = Math.abs(size / Math.sin(fov / 2)); // Camera distance

        if (scene.cameraP.aspect < 1) {
            v.radius /= scene.cameraP.aspect / .97;
        }

        this.setCamera(scene, v); // Set perspective camera to the given radius
        this.zoomExtendsOrtho(box, scene.cameraO, scene.renderer.domElement, v);
    }

    public static zoomExtendsOrtho(box: Box3, cameraO: OrthographicCamera, element: HTMLCanvasElement, v: ViewState): void {
        box.min.add(cameraO.position);
        box.min.applyAxisAngle(axes.x, toRad(v.phi));
        box.min.applyAxisAngle(axes.y, toRad(v.theta));

        box.max.add(cameraO.position);
        box.max.applyAxisAngle(axes.x, toRad(v.phi));
        box.max.applyAxisAngle(axes.y, toRad(v.theta));

        box = new Box3().setFromPoints([box.min, box.max]);

        const boxSize = box.getSize(new Vector3());

        if (boxSize.x / boxSize.y > element.width / element.height) { // Fit width
            cameraO.left = box.min.x;
            cameraO.right = box.max.x;

            const ratio = (element.height / element.width);
            const offCenter = box.max.y + box.min.y;
            cameraO.top = (boxSize.x) * ratio / 2 + offCenter / 2;
            cameraO.bottom = (-boxSize.x) * ratio / 2 + offCenter / 2;
        }
        else { // Fit height
            cameraO.top = box.max.y;
            cameraO.bottom = box.min.y;

            const ratio = (element.width / element.height);
            cameraO.right = boxSize.y * ratio / 2;
            cameraO.left = -boxSize.y * ratio / 2;
        }

        cameraO.zoom = .95;
        cameraO.updateProjectionMatrix();
        cameraO.updateMatrix();
    }

    public static handleObjectHover(scene: Scene, point: IPoint2D, oldObj: IInteractionOptions, allowedInteractions: InteractionType): IInteractionOptions {
        if (!point) return undefined;

        const intersect = this.objectFromMouse(scene, point.x, point.y, allowedInteractions);

        const currentObject = intersect && intersect.interaction;

        if (currentObject !== oldObj) {

            if (oldObj) {

                scene.renderer.domElement.style.cursor = "auto";
                oldObj.control.mouseOut();
            }

            if (currentObject) {

                if (currentObject.control.overCursor) {
                    scene.renderer.domElement.style.cursor = currentObject.control.overCursor;
                }

                currentObject.control.mouseOver(currentObject);
            }
        }

        return currentObject;
    }

    public static defaultSceneCenter(height: number): Vector3 {
        return new Vector3(0, height / 2, 0);
    }

    public static mergeControls(group: CanvasControl, children: CanvasControl[]): void {
        for (const child of children) {
            group.object3D.attach(child.object3D);
        }
    }

    public static splitControls(children: CanvasControl[], scene: SceneControl): void {
        for (const child of children) {
            scene.object3D.attach(child.object3D);
        }
    }

    public static applyRotation(o: Object3D, p: { rotationX: number, rotationY: number, rotationZ?: number }): void {
        o.rotateZ((p.rotationZ || 0) * Math.PI / 180);
        o.rotateX((p.rotationX || 0) * Math.PI / 180);
        o.rotateY((p.rotationY || 0) * Math.PI / 180);
    }

    public static applyReverseRotation(o: Object3D, p: { rotationX: number, rotationY: number, rotationZ: number }): void {
        o.rotateY((-p.rotationY || 0) * Math.PI / 180);
        o.rotateX((-p.rotationX || 0) * Math.PI / 180);
        o.rotateZ((-p.rotationZ || 0) * Math.PI / 180);
    }

    public static screenshot(defaultScene: Scene, state: StateService, width?: number, height?: number, filename?: string): string {
        let box = this.getSceneBoxSize(defaultScene.control.zoomObject);
        box.min.applyAxisAngle(axes.x, toRad(state.view.phi));
        box.min.applyAxisAngle(axes.y, toRad(state.view.theta));
        box.max.applyAxisAngle(axes.x, toRad(state.view.phi));
        box.max.applyAxisAngle(axes.y, toRad(state.view.theta));
        box = new Box3().setFromPoints([box.min, box.max]);
        const boxSize = box.getSize(new Vector3());
        const ratio = boxSize.y / boxSize.x;

        if (!width && !height) {
            width = defaultScene.container.clientWidth;
            height = defaultScene.container.clientHeight;

        } else if (width && !height) { // Fit width
            height = defaultScene.inPerspectiveMode ? width : width * ratio;

        } else if (!width && height) {
            width = defaultScene.inPerspectiveMode ? height : height / ratio;
        }

        state.view.isExporting = true;
        state.compare();
        defaultScene.control.update(state);

        const shotScene = new Scene();
        shotScene.center = this.defaultSceneCenter(settings.sizes.totalDefaultHeight);
        shotScene.renderer = new WebGLRenderer({ antialias: true, preserveDrawingBuffer: true, alpha: true });
        shotScene.renderer.setClearColor(settings.view.backgroundColorExport, settings.view.backgroundAlpha);
        shotScene.renderer.setSize(width, height);

        shotScene.cameraP = new PerspectiveCamera(45, width / height, 100, 100000);
        shotScene.cameraO = new OrthographicCamera(-1, 1, 1, -1, 0, 300000);
        if (state.view.displayMode & Display.PerspectiveView) {
            this.toPerspective(shotScene);

        } else {
            this.toOrtho(shotScene);
        }

        this.zoomExtends(shotScene, state.view, defaultScene.control.zoomObject, 1);

        shotScene.renderer.render(defaultScene.control.object3D as ThreeScene, shotScene.camera);
        const imgData = shotScene.renderer.domElement.toDataURL();
        shotScene.renderer.forceContextLoss();
        shotScene.renderer.dispose();

        state.view.isExporting = false;
        defaultScene.control.update(state);

        if (filename) {
            this.download(filename, imgData);
        }

        return imgData;
    }

    private static download(filename: string, data: string) {
        const image = document.createElement("img");
        image.src = data;

        const link = document.createElement("a");
        link.href = image.src;
        link.download = filename;
        link.style.display = "none";
        const evt = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": true
        });

        document.body.appendChild(link);
        link.dispatchEvent(evt);
        document.body.removeChild(link);
    }
}