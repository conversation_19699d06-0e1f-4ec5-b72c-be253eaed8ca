{"name": "@jest/environment", "version": "29.5.0", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-environment"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/fake-timers": "^29.5.0", "@jest/types": "^29.5.0", "@types/node": "*", "jest-mock": "^29.5.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c"}