import { AmbientLight, Color, CylinderGeometry, DirectionalLight, Mesh, MeshPhongMaterial, PerspectiveCamera, Scene, WebGLRenderer } from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
export class BaseScene {
    renderer;
    camera;
    scene;
    orbitControls;
    constructor() {
        this.setUpMinimalScene();
        this.setUpControls(this.camera, this.renderer);
        this.addDefaultLights();
    }
    setUpMinimalScene() {
        this.scene = new Scene();
        this.scene.background = new Color(0xcccccc);
        this.renderer = new WebGLRenderer({ antialias: true });
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        document.body.appendChild(this.renderer.domElement);
        this.camera = new PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1, 100000);
        this.camera.position.set(400, 200, 0);
        window.addEventListener("resize", () => this.onWindowResize(this.camera, this.renderer));
    }
    onWindowResize(camera, renderer) {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    }
    setUpControls(camera, renderer) {
        this.orbitControls = new OrbitControls(camera, renderer.domElement);
        this.orbitControls.enableDamping = true;
        this.orbitControls.dampingFactor = 0.05;
        this.orbitControls.screenSpacePanning = false;
        this.orbitControls.minDistance = 100;
        this.orbitControls.maxDistance = 500;
        this.orbitControls.maxPolarAngle = Math.PI / 2;
    }
    getPlaceholderContent() {
        const geometry = new CylinderGeometry(0, 10, 30, 4, 1);
        const material = new MeshPhongMaterial({ color: 0xffffff, flatShading: true });
        for (let i = 0; i < 500; i++) {
            const mesh = new Mesh(geometry, material);
            mesh.position.x = Math.random() * 1600 - 800;
            mesh.position.y = 0;
            mesh.position.z = Math.random() * 1600 - 800;
            mesh.updateMatrix();
            mesh.matrixAutoUpdate = false;
            this.scene.add(mesh);
        }
    }
    addDefaultLights() {
        const dirLight1 = new DirectionalLight(0xffffff);
        dirLight1.position.set(1, 1, 1);
        this.scene.add(dirLight1);
        const dirLight2 = new DirectionalLight(0x002288);
        dirLight2.position.set(-1, -1, -1);
        this.scene.add(dirLight2);
        const ambientLight = new AmbientLight(0x222222);
        this.scene.add(ambientLight);
    }
    run() {
        this.loop();
        requestAnimationFrame(() => this.run());
        this.orbitControls.update(); // only required if controls.enableDamping = true, or if controls.autoRotate = true
        this.renderer.render(this.scene, this.camera);
    }
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    loop() {
    }
}
