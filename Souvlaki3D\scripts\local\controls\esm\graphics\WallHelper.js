import { Line3D } from "@immugio/three-math-extensions";
import { GeometryBuilder } from "../geometry/GeometryBuilder";
import { getVerticalFace } from "../geometry/getVerticalFace";
import { getHorizontalFace } from "../geometry/getHorizontalFace";
import { Mesh3D } from "../objects/Mesh3D";
import { axes } from "../utils/axes";
import { WallOpening } from "../core/WallOpening";
export class WallHelper {
    static render(walls, slopes, baseHeight, wallTopMaterial, wallTransparentMaterial) {
        return walls.traverse((wall, previous, next) => {
            return WallHelper.getWall(wall, previous, next, slopes, baseHeight, wallTopMaterial, wallTransparentMaterial);
        });
    }
    static getWall(wall, previous, next, slopes, baseHeight, wallTopMaterial, wallTransparentMaterial) {
        const builder = new GeometryBuilder();
        const { left, right } = wall.getContour(slopes, previous, next);
        const leftFaceContour = this.getVerticalFacePoints(left);
        const leftFaceOpenings = WallOpening.fromMovables(left.bottom[0], left.bottom.at(-1), wall.cutoutItems, slopes);
        const leftFaceIndexes = builder.addPart(getVerticalFace(leftFaceContour, leftFaceOpenings.map(x => x.outline), wall.textureOffset, wall.leftMaterial.textureSize, 0, true));
        const rightFaceContour = this.getVerticalFacePoints(right);
        const rightFaceOpenings = WallOpening.fromMovables(right.bottom[0], right.bottom.at(-1), wall.cutoutItems, slopes);
        const rightFaceIndexes = builder.addPart(getVerticalFace(rightFaceContour, rightFaceOpenings.map(x => x.outline), wall.textureOffset, wall.rightMaterial.textureSize, 1, false));
        builder.addPart(...this.getHorizontalFaceFromOutlines(wallTopMaterial.textureSize, left.top, right.top, false));
        builder.addPart(...this.getHorizontalFaceFromOutlines(wallTopMaterial.textureSize, left.bottom, right.bottom, true));
        const rightFacingVector = left.bottom.at(-1).clone().sub(left.bottom[0]).normalize().applyAxisAngle(axes.y, -Math.PI / 2).multiplyScalar(wall.thickness);
        builder.addPart(...this.getOpeningTopFaces(leftFaceOpenings, rightFacingVector, wall.rightMaterial.textureSize));
        builder.addPart(...this.getOpeningBottomFaces(leftFaceOpenings, rightFacingVector, wall.rightMaterial.textureSize, baseHeight));
        builder.addPart(...this.getOpeningSideFaces(leftFaceOpenings, rightFacingVector, wall.rightMaterial.textureSize));
        builder.addPart(getVerticalFace([
            right.bottom[0],
            right.top[0],
            left.top[0],
            left.bottom[0],
        ], [], null, wall.rightMaterial.textureSize, 1, true));
        builder.addPart(getVerticalFace([
            right.bottom.at(-1),
            right.top.at(-1),
            left.top.at(-1),
            left.bottom.at(-1),
        ], [], null, wall.rightMaterial.textureSize, 1, false));
        const material = wall.isTransparent ? Array(4).fill(wallTransparentMaterial) : [wall.leftMaterial, wall.rightMaterial, wall.revealsMaterial, wallTopMaterial];
        const mesh = new Mesh3D(builder.build(), material);
        if (wall.isInteractive) {
            mesh.interaction = [
                ...leftFaceIndexes.addedFaceIndexes.map(x => ({ ...wall.getInteractionAtIndex(0), faceIndex: x })),
                ...rightFaceIndexes.addedFaceIndexes.map(x => ({ ...wall.getInteractionAtIndex(1), faceIndex: x })),
            ];
        }
        return mesh;
    }
    static getHorizontalFaceFromOutlines(textureSize, outerOutline, innerOutline, reverseFace) {
        const result = [];
        for (let i = 0; i < outerOutline.length - 1; i++) {
            const corners = [
                outerOutline[i],
                outerOutline[i + 1],
                innerOutline[i + 1],
                innerOutline[i]
            ];
            result.push(getHorizontalFace(new Line3D(outerOutline[i], outerOutline[i + 1]), innerOutline[i], corners, textureSize, 3, reverseFace));
        }
        return result;
    }
    static getOpeningSideFaces(externalOpenings, insideFacingVector, textureSize) {
        // Both distance and parallel tolerance for the lines to be considered overlapping
        const tolerance = 0.1;
        // All side lines, two for each opening
        const allSideLines = externalOpenings.map(x => x.getVerticalLines(1)).flat();
        // Only take lines that are on the edges of windows set, i.e. there is no reveal between two windows
        const revealSideLines = allSideLines.filter(line => allSideLines.every(otherLine => line === otherLine || !line.equals(otherLine, tolerance)));
        // When there is a small window beside a tall one, only the difference between the two should have a reveal
        const grouped = revealSideLines.map(line => ({ line, overlaps: revealSideLines.filter(otherLine => line !== otherLine && line.overlaps(otherLine, tolerance)).map(x => x.clone()) }));
        const clipped = grouped.map(g => g.line.clipLines(g.overlaps, tolerance, tolerance).filter(x => x.length > 1)).flat();
        const joinedSideLines = Line3D.joinLines(clipped, Number.EPSILON);
        return joinedSideLines.map(sideLine => {
            const points = [sideLine.start, sideLine.end, sideLine.end.clone().add(insideFacingVector), sideLine.start.clone().add(insideFacingVector)];
            const topLine = new Line3D(sideLine.start.clone().add(insideFacingVector), sideLine.start); // Use the top line for the initial direction so that the texture is oriented correctly
            return getHorizontalFace(topLine, sideLine.end, points, textureSize, 2);
        });
    }
    static getOpeningTopFaces(externalOpenings, insideFacingVector, textureSize) {
        const lines = externalOpenings.map(x => x.topLines);
        return this.getOpeningsFaces(lines.flat(), insideFacingVector, textureSize);
    }
    static getOpeningBottomFaces(externalOpenings, insideFacingVector, textureSize, baseHeight) {
        const lines = externalOpenings.map(x => x.bottomLine).filter(x => x.start.y > baseHeight);
        return this.getOpeningsFaces(lines, insideFacingVector, textureSize);
    }
    static getOpeningsFaces(lines, insideFacingVector, textureSize) {
        const joined = Line3D.joinLines(lines, Number.EPSILON);
        return joined.map(x => {
            const points = [x.start, x.end, x.end.clone().add(insideFacingVector), x.start.clone().add(insideFacingVector)];
            return getHorizontalFace(x, x.start.clone().add(insideFacingVector), points, textureSize, 2);
        });
    }
    static getVerticalFacePoints(face) {
        return [
            ...face.bottom,
            ...face.top.slice().reverse()
        ];
    }
}
