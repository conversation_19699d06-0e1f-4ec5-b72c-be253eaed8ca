{"version": 3, "file": "return-await.js", "sourceRoot": "", "sources": ["../../src/rules/return-await.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,8EAI+C;AAC/C,iDAAmC;AACnC,+CAAiC;AACjC,8CAAgC;AAWhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,iDAAiD;YAC9D,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,KAAK;YAClB,oBAAoB,EAAE,IAAI;YAC1B,eAAe,EAAE,iBAAiB;SACnC;QACD,OAAO,EAAE,MAAM;QACf,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE;YACR,eAAe,EACb,kEAAkE;YACpE,sBAAsB,EACpB,8DAA8D;YAChE,oBAAoB,EAClB,2DAA2D;SAC9D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,CAAC,cAAc,EAAE,QAAQ,EAAE,OAAO,CAAC;aAC1C;SACF;KACF;IACD,cAAc,EAAE,CAAC,cAAc,CAAC;IAEhC,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,IAAI,SAAS,GAAqB,IAAI,CAAC;QAEvC,SAAS,aAAa,CAAC,IAAkB;YACvC,SAAS,GAAG;gBACV,QAAQ,EAAE,IAAI,CAAC,KAAK;aACrB,CAAC;QACJ,CAAC;QAED,SAAS,UAAU,CAAC,IAAa;YAC/B,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;YAE3B,OAAO,QAAQ,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;gBAC/C,IACE,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC;oBAChC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,EAC/B;oBACA,OAAO,IAAI,CAAC;iBACb;gBAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;aAC5B;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,gCAAgC;QAEhC,SAAS,WAAW,CAClB,KAAyB,EACzB,IAAyB;YAEzB,qDAAqD;YACrD,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;gBAC1D,OAAO,IAAI,CAAC;aACb;YAED,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACvE,gDAAgD;YAChD,wBAAwB,CAAC,IAAI,CAAC,UAAU,EAAE;gBACxC,OAAO,IAAI,CAAC;aACb;YAED,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChC,wEAAwE;YACxE,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CAAC,UAAU,EAAE;gBACrD,eAAe,EAAE,IAAI;aACtB,CAAC,CAAC;YACH,IAAI,SAAS,EAAE;gBACb,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aAC5B;YAED,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,SAAS,WAAW,CAClB,KAAyB,EACzB,IAAyB;YAEzB,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAChD,CAAC;QAED,SAAS,IAAI,CAAC,IAAyB,EAAE,UAAmB;YAC1D,IAAI,KAAc,CAAC;YAEnB,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEtD,IAAI,OAAO,EAAE;gBACX,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;aAClC;iBAAM;gBACL,KAAK,GAAG,UAAU,CAAC;aACpB;YAED,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;YAErE,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE;gBAC3B,OAAO;aACR;YAED,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;gBAC1B,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EAAE,iBAAiB;oBAC5B,IAAI;oBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;iBACvC,CAAC,CAAC;gBACH,OAAO;aACR;YAED,IAAI,MAAM,KAAK,QAAQ,EAAE;gBACvB,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE;oBAC1B,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,sBAAsB;wBACjC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;qBACvC,CAAC,CAAC;iBACJ;gBAED,OAAO;aACR;YAED,IAAI,MAAM,KAAK,OAAO,EAAE;gBACtB,IAAI,OAAO,EAAE;oBACX,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,wBAAwB;wBACnC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;qBACvC,CAAC,CAAC;iBACJ;gBAED,OAAO;aACR;YAED,IAAI,MAAM,KAAK,cAAc,EAAE;gBAC7B,MAAM,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;gBAC5C,IAAI,OAAO,IAAI,CAAC,YAAY,EAAE;oBAC5B,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,wBAAwB;wBACnC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;qBACvC,CAAC,CAAC;iBACJ;qBAAM,IAAI,CAAC,OAAO,IAAI,YAAY,EAAE;oBACnC,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,sBAAsB;wBACjC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;qBACvC,CAAC,CAAC;iBACJ;gBAED,OAAO;aACR;QACH,CAAC;QAED,SAAS,yBAAyB,CAChC,IAAyB;YAEzB,IAAI,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,qBAAqB,EAAE;gBACtD,OAAO;oBACL,GAAG,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC;oBAC5C,GAAG,yBAAyB,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC9C,CAAC;aACH;YACD,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;QAED,OAAO;YACL,mBAAmB,EAAE,aAAa;YAClC,kBAAkB,EAAE,aAAa;YACjC,uBAAuB,EAAE,aAAa;YAEtC,4CAA4C,CAC1C,IAAsC;gBAEtC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBACpD,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBAClD,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBAC9D,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC;iBACJ;YACH,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACvD,OAAO;iBACR;gBACD,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACtD,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC9D,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}