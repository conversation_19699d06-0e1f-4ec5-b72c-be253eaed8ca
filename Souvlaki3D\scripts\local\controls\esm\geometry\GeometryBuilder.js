import { createGeometry } from "./createGeometry";
export class GeometryBase {
    position = [];
    faces = [];
    uv = [];
}
export class GeometryPart extends GeometryBase {
    materialIndex;
    constructor(position, faces, uv, materialIndex) {
        super();
        this.position = position;
        this.faces = faces;
        this.uv = uv;
        this.materialIndex = materialIndex;
    }
    build() {
        return createGeometry(this.position, this.uv, this.faces);
    }
}
export class GeometryBuilder extends GeometryBase {
    groups = [];
    get nextFaceIndex() {
        return this.faces.length;
    }
    get nextPositionIndex() {
        return this.position.length / 3;
    }
    build() {
        return createGeometry(this.position, this.uv, this.faces, this.groups);
    }
    addPart(...parts) {
        const initialFaceIndex = this.nextFaceIndex;
        for (const part of parts) {
            const verticesLength = this.nextPositionIndex;
            const startFaceIndex = this.nextFaceIndex;
            this.position.push(...part.position);
            this.uv.push(...part.uv);
            this.faces.push(...part.faces.map(face => face + verticesLength));
            this.groups.push({ start: startFaceIndex, count: part.faces.length, materialIndex: part.materialIndex });
        }
        const addedFaceIndexes = [];
        for (let i = initialFaceIndex; i < this.nextFaceIndex; i += 3) {
            addedFaceIndexes.push(i / 3);
        }
        return { addedFaceIndexes };
    }
}
