package net.ghezzi.rtl.web;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.io.Files;

import jxl.Workbook;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import net.ghezzi.conf.AssembleResult;
import net.ghezzi.conf.ConfiguratorDistintaBase;
import net.ghezzi.conf.ConfiguratorDistintaRiga;
import net.ghezzi.conf.ConfiguratorTimeoutException;
import net.ghezzi.conf.GlobalValues;
import net.ghezzi.conf.GraphManager;
import net.ghezzi.conf.RelativeConnections;
import net.ghezzi.conf.TraversalBase;
import net.ghezzi.conf.TraversalSvkClientPower;
import net.ghezzi.conf.entity.ProductConfiguration;
import net.ghezzi.conf.repository.ProductConfigurationDao;
import net.ghezzi.rtl.components.SvkSession;
import net.ghezzi.rtl.core.SvkConfiguration;
import net.ghezzi.rtl.persistence.entity.Article;
import net.ghezzi.rtl.persistence.entity.UserProfile;
import net.ghezzi.rtl.persistence.repository.ArticleDao;
import net.yadaframework.components.YadaNotify;
import net.yadaframework.components.YadaUtil;
import net.yadaframework.components.YadaWebUtil;

@Controller
public class ConfiguratorController {
	private Logger log = LoggerFactory.getLogger(this.getClass());

	@Autowired private ArticleDao articleDao;
	@Autowired private SvkConfiguration config;
	@Autowired private YadaUtil yadaUtil;
	@Autowired private YadaWebUtil yadaWebUtil;
	@Autowired private GraphManager graphManager;
	@Autowired private SvkSession svkSession;
	@Autowired private YadaNotify yadaNotify;
	@Autowired private ProductConfigurationDao productConfigurationDao;
	@Autowired private MessageSource messageSource;

	// XLS Generation
	private static final int START_ROW = 1;
	private static final int LIMIT_ROW = 60000;

	// model id of tracks/tubulars
	private final String[] strutElements = {"A05558", "A05563", "A05569", "A2724", "A05544", "A05596", "A05564", "A05648", "A05649", "A05650"}; 
	private final Set<String> wideIconSet = new HashSet<>(Arrays.asList(strutElements));
	// model id of joints
	private final String[] jointList = {"A2614", "A2620", "A2619"}; 
	private final Set<String> jointSet = new HashSet<>(Arrays.asList(jointList));
	private final String terminalJoint = "A2614"; // Giunto di testa
	// model id of led tracks
	private final String[] ledTrackList = {"A05544", "A05596", "A05564"}; 
	private final Set<String> ledTrackSet = new HashSet<>(Arrays.asList(ledTrackList));
	
	// model id of attachments
	// Se un attachment non viene inserito, verrà trattato come elemento standalone e verrà quindi falsato il calcolo del power
	private final String farettoSospensioneSemisferico = "A05675";
	private final String farettoSospensioneConico = "A05676";
	private final String[] attachmentList = {
		"A05667", "A05666",	// FARETTO BINARIO CON VETRO
		farettoSospensioneSemisferico, farettoSospensioneConico, // FARETTO SOSPENSIONE BINARIO CON VETRO
		"A2618", 			// FARETTO SNODATO PER BINARIO
		"A2874"				// Speaker
	};
	private final Set<String> attachmentSet = new HashSet<>(Arrays.asList(attachmentList));
	
	// Suspended attachments can have the cable at different lengths, chosen by the user
	private final int[] suspendedAttachmentLengths = new int[] {20, 120, 120}; // min, max, default
	private final Map<String, int[]> suspendedAttachments = Map.of(
			farettoSospensioneSemisferico, suspendedAttachmentLengths,
			farettoSospensioneConico, suspendedAttachmentLengths
    );

	private final Set<String> strutAndJointSet;
	
	// model id of sort order. Unspecified items are not shown as icons in the sidebar
	private final String[] iconSortOrder = {
		"A05558", "A05563", "A05569", 	// BINARIO  Ø25
		"A05544", "A05596", "A05564", 	// BINARIO  LED Ø25
		"A05648", "A05649", "A05650", 	// TUBO LUMINOSO
		"A2620",						// Giunto centrale 
		"A2619", 						// Giunto angolare
		"A2614", 						// Giunto di testa
		"A2618", "A05667", "A05666",	// FARETTO con e senza vetro 
		"A05675", "A05676",				// FARETTO sospensione 
		"A2874"							// Speaker
		}; 
	private final List<String> inverseIconSortOrder;
	
	private final String[] rods = {"200", "400", "600", "800", "1000", "1200"}; // mm
	
	public final String daliGatewayModel = "A2631"; // Was white and black, now is in all colors
	
	public ConfiguratorController() {
		inverseIconSortOrder = Arrays.asList(iconSortOrder);
		Collections.reverse(inverseIconSortOrder); // Revert the sort order to be used in ArticleDao.findAllIcons()
		strutAndJointSet = new HashSet<>(Arrays.asList(strutElements));
		strutAndJointSet.addAll(jointSet);
	}
	
	@RequestMapping("/footerPdf")
	public String footerPdf(Model model, Locale locale) {
		return "/footerPdf";
	}
	
	/**
	 * Mostra la pagina della distinta base che deve essere trasformata in pdf.
	 * Non viene vista dagli utenti ma solo dallo script di generazione pdf.
	 * Esegue nuovamente tutto l'attraversamento del grafo.
	 * @param model
	 * @return
	 */
	@RequestMapping("/pdfsource/{productConfigurationId}/{priceArea}")
	public String pdfsource(@PathVariable("productConfigurationId") Long productConfigurationId, @PathVariable("priceArea") String priceArea, Model model, Locale locale) {
		// This is "unprotected" because puppeteer is not logged in and must have access to all configurations
		ProductConfiguration productConfiguration = productConfigurationDao.findProductConfigurationById(productConfigurationId);
		
		// ProductConfiguration productConfiguration = productConfigurationDao.findProductConfigurationById(productConfigurationId);
		if (productConfiguration==null) {
			model.addAttribute("errorCode", "800");
			return "/pdfError";
		}
		// E' come generare la finalPage ma l'html è diverso
		GlobalValues globalValues = parseGlobalValues(productConfiguration.getJsonGlobalValues());
		if (globalValues==null) {
			model.addAttribute("errorCode", "802");
			return "/pdfError";
		}
		ConfiguratorDistintaBase configuratorDistintaBase = new ConfiguratorDistintaBase(globalValues, priceArea, articleDao);
		String jsonConnections = productConfiguration.getJsonConnections();
		String jsonGlobalValues = productConfiguration.getJsonGlobalValues();
		try {
			TraversalBase traversalBase = prepareFinalPage(configuratorDistintaBase, jsonConnections, jsonGlobalValues, model);
		} catch (IOException e) {
			model.addAttribute("errorCode", "801");
			return "/pdfError";
		}
		commonFinalPage(productConfiguration, configuratorDistintaBase, model, locale);
		return "/bomPdf";
	}

	/**
	 * Creates the pdf and either sends it back to the response or returns it
	 * @param pdfData
	 * @param configurator
	 * @param response null to return the File, otherwise the pdf is sent back to the response
	 * @return the file when response is null
	 * @throws IOException 
	 */
	private File makeAndDownloadServersidePdf(ProductConfiguration productConfiguration, Locale locale, HttpServletResponse response) throws IOException {
		File theFile = makeExportFile(productConfiguration, "pdf");
		theFile.delete(); // Just in case
		log.debug("Creazione PDF per {}", theFile);
		Map<String, String> params = new HashMap<>();
		String shellCommandKey = "config/shell/pdf-chrome";
		// Creo la url che visualizza la pagina da convertire: "http://xxxx.xx.xx/xx/pdfsource/{productConfigurationId}"
		String webappAddress = config.getServerAddress();
		UserProfile currentUser = svkSession.getCurrentUserProfile();
		String priceArea = currentUser==null?config.getPriceAreaDefault():currentUser.getPriceArea();
		if (priceArea==null) {
			priceArea = config.getPriceAreaDefault();
		}
		String pdfSourceUrl = yadaWebUtil.makeUrl(webappAddress, locale.getLanguage(), "/pdfsource/", ""+productConfiguration.getId(), priceArea);
		String pdfFooterUrl = yadaWebUtil.makeUrl(webappAddress, locale.getLanguage(), "/footerPdf");
		log.debug("URL: "+pdfSourceUrl);
		params.put("BASEPATH", config.getBasePathString());
		params.put("URL", pdfSourceUrl);
		params.put("URLPDFFOOTER", pdfFooterUrl);
		// Per evitare problemi di concorrenza, prima creo un file temporaneo e poi lo sposto nella posizione corretta.
		// Il file temporaneo lo faccio comunque dentro al folder dei pdf in modo che se rimane viene cancellato dopo X giorni dallo script di pulizia
		File tempFile = new File(theFile.getParent(), "tmp-" + theFile.getName());
		params.put("FILENAMEOUT", tempFile.getAbsolutePath());
		yadaUtil.shellExec(shellCommandKey, params, null);
		Files.move(tempFile, theFile); // Sposto nella posizione finale in maniera atomica
		if (response!=null) {
			yadaWebUtil.downloadFile(theFile.toPath(), true, MediaType.APPLICATION_PDF_VALUE, theFile.getName(), response);
		}
		return theFile;
	}
	
	/**
	 * Scarica la distinta base in pdf
	 * @param jsonConnections
	 * @param jsonPowerOptions only sent for server-side power supply choice
	 * @param productConfigurationId can be null
	 * @param configurator
	 * @param model
	 * @return
	 */
	@RequestMapping(value="/pdfDownload/{productConfigurationId}", produces = MediaType.APPLICATION_PDF_VALUE)
	public String pdfDownload(@PathVariable("productConfigurationId") Long productConfigurationId, Model model, Locale locale, HttpServletResponse response, RedirectAttributes redirectAttributes) {
		if (productConfigurationId==null) {
			log.error("Missing configuration ID - configuration not saved?");
			yadaNotify.title("Server Error", redirectAttributes, locale).error().messageKey("error.system.internal").add();
			return yadaWebUtil.redirectString("/configurator", locale);
		}
		ProductConfiguration productConfiguration = productConfigurationDao.findProductConfiguration(productConfigurationId, svkSession);
		// ProductConfiguration productConfiguration = productConfigurationDao.findProductConfigurationById(productConfigurationId);
		if (productConfiguration==null) {
			log.error("ProductConfiguration not found on DB for ID={}", productConfigurationId);
			yadaNotify.title("Server Error", redirectAttributes, locale).error().messageKey("error.system.internal").add();
			return yadaWebUtil.redirectString("/configurator", locale);
		}
		// Call the shell command that makes the pdf
		try {
			makeAndDownloadServersidePdf(productConfiguration, locale, response);
			return null; // Nothing to show, the page stays the same but the pdf has been downloaded
		} catch (Exception e) {
			log.error("PDF exception", e);
			yadaNotify.title("Server Error", redirectAttributes).error().messageKey("error.system.internal").add();
			return yadaWebUtil.redirectString("/configurator", locale);
		}
	}

	private void commonFinalPage(@NotNull ProductConfiguration productConfiguration, ConfiguratorDistintaBase distintaBase, Model model, Locale locale) {
		Calendar now = new GregorianCalendar(locale);
		DateFormat formatter = new SimpleDateFormat("MMMM'&nbsp;'yyyy", locale);
		model.addAttribute("date", formatter.format(now.getTime()));
		model.addAttribute("distintaBase", distintaBase);
//		model.addAttribute("topView", productConfiguration.getTopView());
//		model.addAttribute("frontView", productConfiguration.getFrontView());
//		model.addAttribute("sideView", productConfiguration.getSideView());
//		model.addAttribute("perspectiveView", productConfiguration.getPerspectiveView());
		model.addAttribute("productConfiguration", productConfiguration);
	}

	/**
	 * Crea un file su cui salvare il pdf oppure il CSV o eventualmente altro. Va tutto nel folder "generatedCachePath" configurato.
	 * @param productConfiguraton
	 * @param extensionNoDot ad esempio "pdf"
	 * @return
	 */
	public File makeExportFile(ProductConfiguration productConfiguraton, String extensionNoDot) {
		String name = null;
		Long id = 0l;
		if (productConfiguraton==null) {
			id = (long) yadaUtil.getRandom(0, 9999);
		} else {
			id = productConfiguraton.getId();
			name = productConfiguraton.getName();
		}
		if (name!=null) {
			name += "-";
		}
		String outputFilename = String.format("Souvlaki%s-%s.%s", name==null?"":"-"+name, id, extensionNoDot);
		return new File(config.getGeneratedCacheFolder(), YadaUtil.reduceToSafeFilename(outputFilename));
	}

	/**
	 * Memorizza su disco la distinta base in formato XLS
	 * @param configuratorDistintaBase
	 * @param productConfiguration
	 * @param configurator
	 * @throws IOException
	 * @throws WriteException
	 */
	private File saveXls(ProductConfiguration productConfiguration, ConfiguratorDistintaBase configuratorDistintaBase, Locale locale) throws Exception {
		String name = "Souvlaki";
		File xlsFile = makeExportFile(productConfiguration, "xls");
		xlsFile.delete(); // Just in case
		// Client requested that decimal separator always be comma in any language
		Locale commaForDecimals = Locale.ITALIAN; // https://trello.com/c/N6K6NiIT/106-ita-xls-export

		WritableWorkbook workbookXls = null;
		
		String labelCode = messageSource.getMessage("label.bom.code", null, locale);
		String labelQuantity = messageSource.getMessage("label.bom.quantity", null, locale);
		String labelLabel = messageSource.getMessage("label.bom.label", null, locale);
		String labelDescription = messageSource.getMessage("label.bom.description", null, locale);
		String labelPower = messageSource.getMessage("label.bom.power", null, locale);
		String labelUnitPrice = messageSource.getMessage("label.bom.unitPrice", null, locale).replace("&nbsp;", " ");
		String labelTotalRowPrice = messageSource.getMessage("label.bom.totalRowPrice", null, locale).replace("&nbsp;", " ");
		String labelTotalBomPrice = messageSource.getMessage("label.bom.totalBomPrice", null, locale).replace("&nbsp;", " ");
		String labelMultiplier = messageSource.getMessage("powerstep.multiplier", null, locale).replace("&nbsp;", " ");
		
		try {
			workbookXls = Workbook.createWorkbook(xlsFile);
			int fogliNum = 0;
			WritableSheet foglioXls = workbookXls.createSheet(name, fogliNum);

			int colId = 0;

			int row = START_ROW;

			WritableCellFormat mergeFormat = new WritableCellFormat();
		    mergeFormat.setVerticalAlignment(VerticalAlignment.CENTRE);

			if(row == START_ROW || row > LIMIT_ROW) {
				row = START_ROW;
				colId = 0;
				if(fogliNum>0){
					//log.debug("Creazione XLS per {}", theFile);
					foglioXls = workbookXls.createSheet(name +" (" + fogliNum+1 + ")", fogliNum);
				}
				fogliNum++;

				// header file xls
				foglioXls.addCell(new Label(colId++, 0, labelCode));
				foglioXls.addCell(new Label(colId++, 0, labelQuantity));
				foglioXls.addCell(new Label(colId++, 0, labelLabel));
				foglioXls.addCell(new Label(colId++, 0, labelDescription));
				// foglioXls.addCell(new Label(colId++, 0, "Lumen"));
				foglioXls.addCell(new Label(colId++, 0, labelPower));
				foglioXls.addCell(new Label(colId++, 0, labelUnitPrice));
				foglioXls.addCell(new Label(colId++, 0, labelTotalRowPrice));
			}

			for(ConfiguratorDistintaRiga riga : configuratorDistintaBase.getRigheList()) {
				colId = 0;
				foglioXls.addCell(new Label(colId++, row, riga.getSku(), mergeFormat));
				foglioXls.addCell(new Label(colId++, row, riga.getQuantity().toString(), mergeFormat));
				foglioXls.addCell(new Label(colId++, row, riga.getLabel(), mergeFormat));
				foglioXls.addCell(new Label(colId++, row, riga.getDescription(), mergeFormat));
				// foglioXls.addCell(new Label(colId++, row, riga.getLumen().toString(), mergeFormat));
				foglioXls.addCell(new Label(colId++, row, riga.getPowerString(commaForDecimals), mergeFormat));
				foglioXls.addCell(new Label(colId++, row, riga.getUnitPriceString(commaForDecimals), mergeFormat));
				foglioXls.addCell(new Label(colId++, row, riga.getTotalRowPrice().toString(commaForDecimals), mergeFormat));
				row++;
			}
			colId = 0;
			foglioXls.addCell(new Label(colId++, row, labelMultiplier, mergeFormat));
			foglioXls.addCell(new Label(colId++, row, configuratorDistintaBase.getGlobalValues().getMultiplier().toString(), mergeFormat));
			row++;
			if (configuratorDistintaBase.getTotalPrice().isPositive()) {
				colId = 0;
				foglioXls.addCell(new Label(colId++, row, labelTotalBomPrice, mergeFormat));
				foglioXls.addCell(new Label(colId++, row, configuratorDistintaBase.getTotalPrice().toString(commaForDecimals), mergeFormat));
			}
			workbookXls.write();
			return xlsFile;
		} catch (Exception e) {
			log.error("Impossibile scrivere il file XLS della distinta base");
			xlsFile.delete();
			throw e;
		} finally {
			if (workbookXls!=null) {
				workbookXls.close();
			}
		}
	}
	
	/**
	 * Memorizza su disco la distinta base in formato CSV
	 * @param configuratorDistintaBase
	 * @throws IOException
	 */
	private File saveCsv(ProductConfiguration productConfiguration, ConfiguratorDistintaBase configuratorDistintaBase, Locale locale) throws IOException {
		// se productConfiguration ha già il nome di un file CSV, cancellarlo da disco
		File theFile = makeExportFile(productConfiguration, "csv");
		theFile.delete(); // Just in case
		// Client requested that decimal separator always be comma in any language
		Locale commaForDecimals = Locale.ITALIAN; // https://trello.com/c/N6K6NiIT/106-comma-in-xls-export

		log.debug("Creazione CSV per {}", theFile);
		try (Writer writer = new BufferedWriter(new FileWriter(theFile))) {
			// Righe della distinta base
			String labelCode = messageSource.getMessage("label.bom.code", null, locale);
			String labelQuantity = messageSource.getMessage("label.bom.quantity", null, locale);
			String labelLabel = messageSource.getMessage("label.bom.label", null, locale);
			String labelDescription = messageSource.getMessage("label.bom.description", null, locale);
			String labelPower = messageSource.getMessage("label.bom.power", null, locale);
			String labelUnitPrice = messageSource.getMessage("label.bom.unitPrice", null, locale).replace("&nbsp;", " ");
			String labelTotalRowPrice = messageSource.getMessage("label.bom.totalRowPrice", null, locale).replace("&nbsp;", " ");
			String labelMultiplier = messageSource.getMessage("powerstep.multiplier", null, locale).replace("&nbsp;", " ");
			String labelTotalBomPrice = messageSource.getMessage("label.bom.totalBomPrice", null, locale).replace("&nbsp;", " ");
			writer.write(String.format("%s; %s; %s; %s; %s; %s; %s;\n", labelCode, labelQuantity, labelLabel, labelDescription, labelPower, labelUnitPrice, labelTotalRowPrice));
			for (ConfiguratorDistintaRiga riga : configuratorDistintaBase.getRigheList()) {
				writer.write(riga.getSku() + "; " +
				riga.getQuantity()  + "; " +
				riga.getLabel()  + "; " +
				riga.getDescription() + "; " +
				// riga.getLumen() + "; " +
				riga.getPowerString(commaForDecimals)  + "; " +
				riga.getUnitPriceString(commaForDecimals)  + "; " +
				riga.getTotalRowPrice().toString(commaForDecimals) + ";\n" );
			}
			writer.write(labelMultiplier + "; " + configuratorDistintaBase.getGlobalValues().getMultiplier().toString() + ";\n" );
			if (configuratorDistintaBase.getTotalPrice().isPositive()) {
				writer.write(labelTotalBomPrice + "; " + configuratorDistintaBase.getTotalPrice().toString(commaForDecimals) + ";\n" );
			}
			writer.close();
			return theFile;
		} catch (IOException e) {
			theFile.delete();
			throw e;
		}
	}
	

	/**
	 * Returns the HTML of the Bill of Materials
	 * @param topView
	 * @param frontView
	 * @param sideView
	 * @param perspectiveView
	 * @param model
	 * @return
	 */
	@RequestMapping("/showbom") // Ajax
	public String showBom(String productConfigurationId, String topView, String frontView, String sideView, String perspectiveView, Model model, Locale locale) {
		// Need to use a String productConfigurationId because Long gives a null pointer when the input is null
		Long productConfigurationIdLong = null;
		if (productConfigurationId!=null && !"null".equals(productConfigurationId) && !"undefined".equals(productConfigurationId)) {
			try {
				productConfigurationIdLong = Long.parseLong(productConfigurationId);
			} catch (NumberFormatException e) {
				log.debug("Invalid productConfigurationId (ignored): {}", productConfigurationId);
				// Keep going
			}
		}
		// Get the distinta base from session
		ConfiguratorDistintaBase configuratorDistintaBase = svkSession.getConfiguratorDistintaBase();
		if (configuratorDistintaBase==null) {
			log.error("Missing configuratorDistintaBase in session (should never happen)");
			return yadaNotify.title("Server Error", model, locale).error().messageKey("error.configuration.internal").add();
		}
		// We either update an existing configuration or create a new one. Doesn't matter if the specified productConfigurationId doesn't exist,
		ProductConfiguration productConfiguration = productConfigurationDao.findProductConfiguration(productConfigurationIdLong, svkSession);
		if (productConfiguration==null) {
			productConfiguration = new ProductConfiguration(svkSession.getCurrentUserProfile());
		}
		productConfiguration.setJsonConnections(svkSession.getJsonConnections());
		productConfiguration.setJsonGlobalValues(svkSession.getJsonGlobalValues());
		// Save images to temporary files
		productConfiguration.setTopView(makeImageViewUrl(topView, "topView", productConfiguration));
		productConfiguration.setFrontView(makeImageViewUrl(frontView, "frontView", productConfiguration));
		productConfiguration.setSideView(makeImageViewUrl(sideView, "sideView", productConfiguration));
		productConfiguration.setPerspectiveView(makeImageViewUrl(perspectiveView, "perspectiveView", productConfiguration));
		//
		productConfiguration = productConfigurationDao.save(productConfiguration);
		svkSession.clearConfiguratorData(); // Clear cached data from session (free up some memory)
		// Pre-export
		File csvFile = null;
		File xlsFile = null;
		try {
			csvFile = saveCsv(productConfiguration, configuratorDistintaBase, locale);
		} catch (Exception e) {
			log.error("Impossibile creare file csv", e);
			return yadaNotify.title("Server Error", model, locale).error().messageKey("error.configuration.csv").add();
		}
		try {
			xlsFile = saveXls(productConfiguration, configuratorDistintaBase, locale);
		} catch (Exception e) {
			log.error("Impossibile creare file xls", e);
			return yadaNotify.title("Server Error", model, locale).error().messageKey("error.configuration.xls").add();
		}
		model.addAttribute("csvFilename", csvFile.getName());
		model.addAttribute("xlsFilename", xlsFile.getName());
		commonFinalPage(productConfiguration, configuratorDistintaBase, model, locale);
		return "/bomTable :: fragment";			
	}
	
	/**
	 * Saves a screenshot to disk in order to generate the PDF later
	 * @param base64Image
	 * @param imageType
	 * @param productConfiguration
	 * @return the absolute url of the image
	 */
	private String makeImageViewUrl(String base64Image, String imageType, ProductConfiguration productConfiguration) {
		String filenameNoExtension = String.format("%s-%s-%s", imageType, productConfiguration.getId(), yadaUtil.getRandomText(10));
		File fileNoExtension = new File(config.getGeneratedCacheFolder(), YadaUtil.reduceToSafeFilename(filenameNoExtension));
		File savedImage = yadaWebUtil.saveBase64Image(base64Image, fileNoExtension);
		String imageUrl = config.getGeneratedCacheFullDirName() + "/" + savedImage.getName();
		return imageUrl;
	}
	
	/**
	 * Walks the configuration graph to set labels, assign missing elements and create the final parts list.
	 * @param jsonConnections
	 * @param jsonPowerOptions
	 * @param productConfigurationId must be not null
	 * @param configurator
	 * @param model
	 * @return a json with calculated labels and error message
	 */
	@RequestMapping(path = "/calcbom", produces = MediaType.APPLICATION_JSON_VALUE) // Ajax
	@ResponseBody
	public AssembleResult calcBom(String jsonConnections, String jsonGlobalValues, Model model, Locale locale) {
		AssembleResult result = new AssembleResult(); // Used for errors
		
		//
		// Error checks
		//
//		boolean disabled = false; // Mettere a true per disabilitare la final page e mostrare il messaggio
//		if (disabled) {
//			//return yadaNotify.title("System Maintenance", model).info().message("The article list is currently disabled for maintenance. Please save your configuration and come back later.").add();
//			result.setErrorTitle("System Maintenance");
//			result.setErrorMessage("The article list is currently disabled for maintenance. Please save your configuration and come back later.");
//			return result;
//		}
		if ("".equals(jsonConnections) || "[]".equals(jsonConnections)) {
			result.setErrorTitle("Configuration Error");
			result.setErrorMessage(messageSource.getMessage("error.configuration.empty", null, locale));
			return result;
		}
		GlobalValues globalValues = parseGlobalValues(jsonGlobalValues);
		if (globalValues==null) {
			// return yadaNotify.title("Server Error", model).error().message("Received data is invalid - please try again").add();
			result.setErrorTitle("Server Error");
			result.setErrorMessage("Received data is invalid - please try again.");
			return result;
		}
		
		//
		// Assembling
		//
		UserProfile currentUser = svkSession.getCurrentUserProfile();
		String priceArea = currentUser==null?config.getPriceAreaDefault():currentUser.getPriceArea();
		ConfiguratorDistintaBase configuratorDistintaBase = new ConfiguratorDistintaBase(globalValues, priceArea, articleDao);
		try {
			// Create the parts table
			TraversalBase traversalBase = prepareFinalPage(configuratorDistintaBase, jsonConnections, jsonGlobalValues, model);
			svkSession.setConfiguratorData(jsonConnections, jsonGlobalValues, configuratorDistintaBase); // Cache for showBom()
			return traversalBase.getLabelFactory().getAssembleResult();
		} catch (Exception e) {
			log.error("Invalid json received: {}", jsonConnections, e);
			// return yadaNotify.title("Server Error", model).error().message("Received data is invalid - please try again").add();
			result.setErrorTitle("Server Error");
			result.setErrorMessage("Received data is invalid - please try again.");
			return result;
		}
	}

	private GlobalValues parseGlobalValues(String jsonGlobalValues) {
		GlobalValues globalValues = new GlobalValues();
		if (jsonGlobalValues!=null) {
			try {
				ObjectMapper objectMapper = new ObjectMapper();
				globalValues = objectMapper.readValue(jsonGlobalValues, GlobalValues.class);
			} catch (Exception e) {
				log.error("Invalid json received: {}", jsonGlobalValues, e);
				return null;
			}
		}
		return globalValues;
	}

	/**
	 * Genera la distinta base facendo un attraversamento della configurazione.
	 * @param jsonConnections
	 * @param jsonPowerOptions only sent for server-side power supply choice
	 * @param productConfigurationId
	 * @param rodSize 
	 * @param model
	 * @return ConfiguratorDistintaBase
	 * @throws IOException
	 */
	private TraversalBase prepareFinalPage(ConfiguratorDistintaBase distintaBase, String jsonConnections, String jsonGlobalValues, Model model) throws IOException {
		List<RelativeConnections> connectionList = null;
//		PowerOptions powerOptions = null;
		ObjectMapper objectMapper = new ObjectMapper();
		connectionList = objectMapper.readValue(jsonConnections, new TypeReference<List<RelativeConnections>>(){});
		GlobalValues globalValues = new GlobalValues();
		if (jsonGlobalValues!=null) {
			globalValues = objectMapper.readValue(jsonGlobalValues, GlobalValues.class);
		}

		////
		//// Fill the bill of materials
		////
		TraversalSvkClientPower traversalSvk = new TraversalSvkClientPower(distintaBase, globalValues);
		try {
			yadaWebUtil.autowireAndInitialize(traversalSvk); // Add the DAO etc.
			graphManager.process(connectionList, distintaBase, traversalSvk);
		} catch (ConfiguratorTimeoutException e) {
			log.error("ConfiguratorTimeoutException when preparing final page (ignored)");
			log.error("jsonGlobalValues dump: {}", jsonGlobalValues);
			log.error("jsonConnections dump: {}", jsonConnections);
			// Keep going
		}
		return traversalSvk;
	}

	/**
	 * Returns a json string with configuration and database definitions for all models
	 * @param model
	 * @param locale
	 * @return
	 */
	@RequestMapping(value="/applicationData", produces=MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public Map<String, Object> applicationData(Model model, Locale locale) {
		Map<String, Object> result = yadaUtil.makeJsonObject();
		result.put("powerSupplyWatts", config.getPowerSupplyWatts());
		// result.put("gatewayWatts", articleDao.getGatewayWatts(idDaliGateway));
		Map<String, Object> models = models();
		// Also add the gateway
		Article gateway = articleDao.findByInternalId(daliGatewayModel);
		addJson(gateway, models);
		//
		result.put("models", models);
		Map<String, Object> dictionary = yadaUtil.makeJsonObject(result, "dictionary");
		// The dictionary is an association between FE message and BE value. The FE message is actually just a key. The BE value is translated as needed.
		yadaUtil.setJsonAttribute(dictionary, "Compatible Target Missing", messageSource.getMessage("fe.target.missing", null, locale));
		yadaUtil.setJsonAttribute(dictionary, "This item can not be inserted into the current drawing", messageSource.getMessage("fe.insert.failed", null, locale));
		yadaUtil.setJsonAttribute(dictionary, "Click on starting point", messageSource.getMessage("fe.click.start", null, locale));
		yadaUtil.setJsonAttribute(dictionary, "Cannot flip due to the ceiling distance", messageSource.getMessage("fe.flip.impossible", null, locale));
		yadaUtil.setJsonAttribute(dictionary, "Invalid configuration", messageSource.getMessage("fe.configuration.invalid", null, locale));
		yadaUtil.setJsonAttribute(dictionary, "The configuration is empty", messageSource.getMessage("fe.configuration.empty", null, locale));
		yadaUtil.setJsonAttribute(dictionary, "Configuration is incomplete", messageSource.getMessage("fe.configuration.incomplete", null, locale));
		yadaUtil.setJsonAttribute(dictionary, "The elements must all be connected to each other without interruption of continuity", messageSource.getMessage("fe.configuration.split", null, locale));
		// The next one should not be used anymore
		yadaUtil.setJsonAttribute(dictionary, "No free space found to place a gateway", messageSource.getMessage("fe.configuration.noplaceforgateway", null, locale));
		yadaUtil.setJsonAttribute(dictionary, "Multiplier required", messageSource.getMessage("fe.multiplier.title", null, locale));
		yadaUtil.setJsonAttribute(dictionary, "Please specify the number of configurations needed", messageSource.getMessage("fe.multiplier.message", null, locale));
		return result;
	}

	/* Colors re-enabled on 11 mar 2024
	private boolean keepBlackOnlyLogged=false;
	// This has to be removed when the client can show colors again
	private void keepBlackOnly(List<String> allColors) {
		if (!keepBlackOnlyLogged) {
			log.warn("!!!                                       !!!");
			log.warn("!!! Only BLACK color is currently enabled !!!");
			log.warn("!!!                                       !!!");
			keepBlackOnlyLogged=true;
		}
		// Client request: show only black elements
		// "Inoltre per motivi produttivi, dobbiamo sospendere i colorati quindi  ti chiedo, 
		//  fino a nuova comunicazione, di nascondere tutte le varianti colore. Dovrà essere tutto SOLO NERO."
		allColors.removeIf(item -> !item.equals("NERO")); // Remove anything different from "NERO"
	}
	*/
	
	/**
	 * Returns a json string with database definitions for all models
	 * @param model
	 * @param locale
	 * @return
	 */
	public Map<String, Object> models() {
		Map<String, Object> result = yadaUtil.makeJsonObject();
		List<Article> icons = articleDao.findAllIcons(inverseIconSortOrder, inverseIconSortOrder);
		for (Article article : icons) {
			addJson(article, result);
		}
		return result;
	}
	
	private void addJson(Article article, Map<String, Object> result) {
		String modelId = article.getModelId();
		Map<String, Object> data = yadaUtil.makeJsonObject(result, modelId);
		List<String> allColors = articleDao.findAllColors(modelId, null);
		// keepBlackOnly(allColors);
		List<String> allKelvins = articleDao.findAllKelvins(modelId);
		List<String> allDrivers = articleDao.findAllDrivers(modelId);
		List<String> allBeams = articleDao.findAllBeams(modelId);
		data.put("colorIta", allColors);
		data.put("kelvin", allKelvins);
		data.put("control", allDrivers);
		data.put("beam", allBeams);
		data.put("power", article.getPower()); // Serve che venga mandato al BE e rimaniamo compatibili con la gestione power lato client. 
		data.put("jointFlag", jointSet.contains(modelId));
		data.put("attachment", attachmentSet.contains(modelId));
		if (suspendedAttachments.containsKey(modelId)) {
			data.put("pendantLengthMinMaxDef", suspendedAttachments.get(modelId));
			data.put("pendantLength", "any"); // Added with "any" value so it can be handled automatically in SvkMovablesController.checkProperty()
		}
		String modelUrl = yadaWebUtil.makeUrl(config.getContentUrl(), "/configurator/svk/gltf/", modelId + ".gltf");
		data.put("modelUrl", modelUrl);
	}
	
	
	/**
	 * Returns the relative url of the model icon for the sidebar.
	 * Used by Thymeleaf in @{}
	 * @param article
	 * @return
	 */
	public String getSidebarIconUrl(Article article) {
		StringBuilder result = new StringBuilder(config.getContentUrl());
		result.append("/configurator/svk/icons/").append(article.getModelId()).append(".png");
		return result.toString();
	}
	
	/**
	 * Returns the relative url of the gltf model.
	 * Used by Thymeleaf in @{}
	 * @param article
	 * @return
	 */
	public String getModelUrl(Article article) {
		StringBuilder result = new StringBuilder(config.getContentUrl());
		result.append("/configurator/svk/gltf/").append(article.getModelId()).append(".gltf");
		return result.toString();
	}
	
	/**
	 * Returns the relative url of the thumbnail image for the BOM.
	 * Used by Thymeleaf in @{}
	 * @param bomRow
	 * @return
	 */
	public String getThumbnailUrl(ConfiguratorDistintaRiga bomRow) {
		return yadaWebUtil.makeUrl(config.getContentUrl(), "/configurator/svk/thumbnails/", bomRow.getThumbnail());
	}
	
	/**
	 * Returns the relative url of the thumbnail image.
	 * Used by Thymeleaf in @{}
	 * @param internalId
	 * @return
	 */
	public String getThumbnailUrl(String internalId) {
		Article article = articleDao.findByInternalId(internalId);
		if (article==null) {
			log.error("Can't find Article {} for thumbnail - using blank", internalId);
			return yadaWebUtil.makeUrl(config.getContentUrl(), "/configurator/svk/thumbnails/unset.jpg");
		}
		return yadaWebUtil.makeUrl(config.getContentUrl(), "/configurator/svk/thumbnails/", article.getThumbnail());
	}

	/**
	 * Returns true if the Article is a track with indirect light.
	 * @return
	 */
	public boolean isLedTrack(String modelId) {
		return ledTrackSet.contains(modelId);
	}
	
	/**
	 * Returns true if the Article should have a wide icon.
	 * Used by Thymeleaf
	 * @param article
	 * @return
	 */
	public boolean isWideIcon(Article article) {
		return wideIconSet.contains(article.getModelId());
	}
	
	/**
	 * Returns true if the Article is a terminal joint.
	 * Used by Thymeleaf
	 * @param article
	 * @return
	 */
	public Boolean isTerminalJoint(Article article) {
		return terminalJoint.equals(article.getModelId())?true:null;
	}
	
	@RequestMapping("/configurator/{productConfigurationId}")
	public String loadConfiguration(@PathVariable Long productConfigurationId, Model model, Locale locale) {
		ProductConfiguration productConfiguration = productConfigurationDao.findProductConfiguration(productConfigurationId, svkSession);
		if (productConfiguration!=null) {
			model.addAttribute("productConfiguration", productConfiguration);
		}
		return configurator(model, locale);
	}
	
	@RequestMapping("/configurator")
	public String configurator(Model model, Locale locale) {
		// Prepare data for sidebar properties
		List<String> allColors = articleDao.findAllColors(null, null);
		List<String> structureColors = articleDao.findAllColors(null, strutAndJointSet);
		List<String> attachmentColors = articleDao.findAllColors(null, attachmentSet);
		// keepBlackOnly(allColors);
		List<String> allKelvins = articleDao.findAllKelvins(null);
		List<String> allDrivers = articleDao.findAllDrivers(null);
		List<String> allBeams = articleDao.findAllBeams(null);
		int[] suspendedMinMaxDef = findSuspendedMinMaxDef();
		model.addAttribute("allColors", allColors);
		model.addAttribute("structureColors", structureColors);
		model.addAttribute("attachmentColors", attachmentColors);
		model.addAttribute("allKelvins", allKelvins);
		model.addAttribute("allDrivers", allDrivers);
		model.addAttribute("allBeams", allBeams);
		model.addAttribute("rods", rods);
		model.addAttribute("cableMin", suspendedMinMaxDef[0]);
		model.addAttribute("cableMax", suspendedMinMaxDef[1]);
		model.addAttribute("cableDefault", suspendedMinMaxDef[2]);
		// Prepare data for sidebar icons
		List<Article> structureIcons = articleDao.findAllIcons(strutAndJointSet, inverseIconSortOrder);
		List<Article> attachmentIcons = articleDao.findAllIcons(attachmentSet, inverseIconSortOrder);
		model.addAttribute("structureIcons", structureIcons);
		model.addAttribute("attachmentIcons", attachmentIcons);
		return "/configurator";
	}

	/**
	 * Returns the lowest min length, highest max length, the most common default in all the suspended attachments
	 * @return
	 */
	private int[] findSuspendedMinMaxDef() {
		// Implementation from ChatGPT
		int minMin = Integer.MAX_VALUE;
		int maxMax = Integer.MIN_VALUE;
		Map<Integer, Integer> defaultValueFrequency = new HashMap<>();

		for (int[] values : suspendedAttachments.values()) {
			minMin = Math.min(minMin, values[0]);
			maxMax = Math.max(maxMax, values[1]);
			int defaultValue = values[2];
			defaultValueFrequency.put(defaultValue, defaultValueFrequency.getOrDefault(defaultValue, 0) + 1);
		}

		// Find the most common default value
		int mostCommonDefaultValue = 0;
		int highestFrequency = 0;
		for (Map.Entry<Integer, Integer> entry : defaultValueFrequency.entrySet()) {
			int defaultValue = entry.getKey();
			int frequency = entry.getValue();
			if (frequency > highestFrequency) {
				mostCommonDefaultValue = defaultValue;
				highestFrequency = frequency;
			}
		}
		return new int[] {minMin, maxMax, mostCommonDefaultValue};
	}

}
