import { CanvasControl } from "./CanvasControl";
import { IApplicationState } from "../../models/state/IApplicationState";
import { Activator } from "../../infrastructure/Activator";
import { MovableGraphics } from "../graphics/MovableGraphics";
import { ResizeControl } from "./ResizeControl";
import { settings } from "../../configurations/setup_global";

export class ResizeBuildingControl extends CanvasControl {

    private resizeControls: ResizeControl[] = [];

    public update(s: IApplicationState): void {
        if (s.diff.controlOptionsChanged || s.diff.isNewDesign) {

            const refresh = s.diff.isNewDesign || this.resizeControls.length !== s.view.controlOptions.length || !s.view.controlOptions.every((c, i) => c.interactionType === this.resizeControls[i].interaction.interactionType);

            if (refresh) { // The number of controls and/or their type have changed - need to recreate
                for (let k = this.resizeControls.length - 1; k >= 0; k--) {
                    this.object3D.remove(this.resizeControls[k].object3D);
                    this.resizeControls.pop();
                }

                for (const option of s.view.controlOptions) {
                    const g = Activator.getUnique<MovableGraphics>(settings.sizes.resizeFpGraphics);
                    const control = new ResizeControl(g);
                    control.init(option);
                    this.resizeControls.push(control);
                    this.object3D.add(control.object3D);
                }

            } else { // The number of controls and their type is the same - just need to update
                for (let j = 0, l = s.view.controlOptions.length; j < l; j++) {
                    this.resizeControls[j].init(s.view.controlOptions[j]);
                }
            }
        }

        for (const ctrl of this.resizeControls) {
            ctrl.update(s);
        }
    }
}