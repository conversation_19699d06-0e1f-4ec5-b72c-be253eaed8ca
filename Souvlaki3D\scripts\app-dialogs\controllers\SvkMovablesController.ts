import { ISvkServerData } from "./SvkController";
import { IApplicationState } from "../../models/state/IApplicationState";
import { IMenuItem } from "../../models/menu/IMenuItem";
import { startInserting } from "../../state/actions/startInserting";
import { settings } from "../../configurations/setup_global";
import { DraggableIcon } from "../components/DraggableIcon";

export class SvkMovablesController {

    public static $inject = ["StateService"];

    private propertyValues = {}; // selectId: selected option
    private menuItem: IMenuItem;
    private icon: DraggableIcon;

    constructor(private state: IApplicationState<ISvkServerData>) {
        document.querySelectorAll(".palette .icon img").forEach((img) => img.addEventListener("mousedown", (e: MouseEvent) => this.startDragging(e)));
        document.getElementById(settings.app.containerId).addEventListener("mouseenter", () => this.startInserting());
        document.addEventListener("mouseup", () => this.finishDragging());
        document.addEventListener("mousemove", (e: MouseEvent) => this.icon?.setPosition(e));
    }

    private startDragging(event: MouseEvent): void {
        if (this.menuItem) {
            return;
        }

        this.icon = DraggableIcon.fromEvent(event);

        this.resetFlashProperty();
        const modelId = $(event.target).parent().attr("data-type");

        const distanceFromCeiling = $<HTMLInputElement>("option:selected", $("#rodSize"))[0].value;
        const availableDbModelProperties = this.state.applicationData.models[modelId];
        // This will be filled with the properties to set on the MovableItem
        const filteredProperties = {
            type: modelId,
            modelUrl: availableDbModelProperties.modelUrl,
            distanceFromCeiling: parseInt(distanceFromCeiling),
            jointFlag: availableDbModelProperties.jointFlag,
            attachment: availableDbModelProperties.attachment,
            power: availableDbModelProperties.power
        };
        if (availableDbModelProperties) {
            $("#sidebar .elementProperties select:visible, #sidebar .elementProperties input:visible").each((index, element) => {
                this.checkProperty($(element), availableDbModelProperties, filteredProperties);
            });
        }
        this.menuItem = filteredProperties;
    }

    private startInserting(): void {
        if (this.menuItem) {
            startInserting(this.menuItem);
        }

        if (this.icon) {
            this.icon.remove();
            this.icon = null;
        }
    }

    private finishDragging(): void {
        if (this.menuItem) {
            this.resetFlashProperty();
            $("#sidebar .elementProperties select").each((index: number, select: HTMLInputElement) => {
                const savedValue = this.propertyValues[select.id];
                if (savedValue !== undefined) {
                    select.value = savedValue; // Only restore if value was saved
                }
            });
            this.propertyValues = {};
            this.menuItem = null;
        }

        if (this.icon) {
            this.icon.remove();
            this.icon = null;
        }
    }

    private resetFlashProperty(): void {
        $("#sidebar .elementProperties select").removeClass("flash ok noChoice shouldChoose notUsed");
        $("#sidebar .elementProperties select, #sidebar .elementProperties input").removeAttr("disabled");
    }

    private checkProperty($selectOrInput: JQuery, availableDbModelProperties, filteredProperties): void {
        this.savePropertyValue($selectOrInput[0] as HTMLInputElement); // To be used at drag end
        const propertyName = $selectOrInput.attr("name"); // beam
        if (propertyName == null) {
            return; // Maybe the select has been removed
        }
        const availableValues = availableDbModelProperties[propertyName]; // ["18°", "24°", "36°", "60°"]
        if (availableValues == null || availableValues.length == 0) {
            // This property can't be set on this model: just ignore it.
            this.flashPropertyNotUsed($selectOrInput);
            return;
        }
        const chosenValue = $selectOrInput.val();
        // const chosenValue = $<HTMLInputElement>("option:selected", $selectOrInput)[0].value;
        if (availableValues.includes(chosenValue) || availableValues=="any") {
            // The chosen property is ok
            filteredProperties[propertyName] = chosenValue;
            this.flashPropertyOk($selectOrInput);
        } else if (availableValues.length == 1) {
            // The chosen property is not valid for this element and there is only one alternative
            filteredProperties[propertyName] = availableValues[0];
            this.flashPropertyNoChoice($selectOrInput, filteredProperties[propertyName]);
        } else {
            // The chosen property is not valid and there are more choices possible
            filteredProperties[propertyName] = availableValues[0];
            this.flashPropertyShouldChoose($selectOrInput, filteredProperties[propertyName]);
        }
    }

    private flashPropertyNotUsed($selectOrInput: JQuery): void {
        // $selectOrInput.addClass("flash notUsed");
        $selectOrInput.attr("disabled", "true");
    }

    private flashPropertyShouldChoose($select: JQuery, forcedValue): void {
        $select.addClass("flash shouldChoose");
        $("option", $select).removeAttr("selected");
        $("option[value='" + forcedValue + "']", $select).attr("selected", "selected");
    }

    private flashPropertyNoChoice($select: JQuery, forcedValue): void {
        $select.addClass("flash noChoice");
        $("option", $select).removeAttr("selected");
        $("option[value='" + forcedValue + "']", $select).attr("selected", "selected");
    }

    private flashPropertyOk($select: JQuery): void {
        $select.addClass("flash ok");
    }

    private savePropertyValue(select: HTMLInputElement): void {
        const selectId = select.id; // elemcolor
        this.propertyValues[selectId] = select.value;
    }
}
