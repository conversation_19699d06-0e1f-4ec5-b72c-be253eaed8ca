import { IApplicationSettings } from "../../../models/infrastructure/IApplicationSettings";
import { IApplicationState } from "../../../models/state/IApplicationState";
import { MovableItem } from "../../../models/state/MovableItem";
import { CanvasControl } from "../CanvasControl";
import { Scene } from "../../../models/scene/Scene";
import { AvailableActions, InteractionType } from "../../../models/common/Enums";
import { PositioningUtils } from "../../../logic/helpers/PositioningUtils";
import { MessageHub } from "../../../infrastructure/MessageHub";
import { Events } from "../../../models/infrastructure/Events";
import { IInteractionOptions } from "../../../models/positioning/IInteractionOptions";
import { StateService } from "../../../state/StateService";
import { pushState } from "../../../state/actions/pushState";
import { getRelativeConnections } from "../../../logic/helpers/movables/getRelativeConnections";
import { DesignState } from "../../../models/state/DesignState";
import { Vector3 } from "three";
import { ISvkServerData, SvkController } from "../../../app-dialogs/controllers/SvkController";
import { IPowerSourceUpdatedEvent } from "../../../models/events/IPowerSourceUpdatedEvent";
import { PolygonUtils } from "../../../logic/helpers/PolygonUtils";
import { Vec3 } from "@immugio/three-math-extensions";
import { MovablesUtils } from "../../../logic/helpers/MovablesUtils";
import { SvkDotControl } from "./SvkDotControl";
import { areConnectionsClockwise, isClosedConfiguration, makeOrderedConnections } from "../../../logic/helpers/movables/makeOrderedConnections";
import { getPrimaryPSU } from "../../../logic/helpers/movables/getPrimaryPSU";
import { isDesignPowered } from "../../../logic/helpers/power/isDesignPowered";
import { IRelativeConnections } from "../../../models/positioning/IRelativeConnections";

export class SvkPowerControl extends CanvasControl {

    public static $inject = ["settings", "scene", "StateService"]

    constructor(private settings: IApplicationSettings, private scene: Scene, private state: StateService<ISvkServerData>) {
        super();
        MessageHub.subscribe<IInteractionOptions>(Events.drawingInteraction, e => this.selectPowerSource(e));
        //MessageHub.subscribe(Events.requestPSUPlacement, () => this.autoPlacePSUs());
        MessageHub.subscribe<boolean>(Events.requestGatewayUpdate, e => this.updateGateway(e));
    }

    private gatewayPower = 0;
    private gatewayType = 'A2631'; // This is the model, not the id
    private movables: MovableItem[] = [];
    private addGatewayOnPSUSelection = false;

    public update(s: IApplicationState): void {
        if (!s.view.isActionAvailable(AvailableActions.Power)) {
            this.movables = [];
            if (this.children.length > 0) {
                this.clearChildren();
            }
            return;
        }
        
        this.object3D.position.x = -s.view.offset.x;
        this.object3D.position.z = -s.view.offset.y;

        const movables = s.design.movableItems.slice();
        if (this.changed(movables)) {
            this.gatewayPower = this.state.applicationData.models[this.gatewayType].power;
            this.clearChildren();
            this.movables = movables;
            this.updatePowerSources();
            this.setControls(s.design);
        }

        super.update(s);
    }

    private selectPowerSource(e: IInteractionOptions): void {
        if (e?.interactionType !== InteractionType.ChooseLighting || !(e.control as SvkDotControl).interactive) {
            return;
        }

        const clone = this.state.design.clone();
        const selected = clone.movableItems.find(x => x.id === e.movable.id);
        const isPowerSource = selected && selected.data.powerSource?.length;
        const primaryPowerSource = isPowerSource && selected.data.powerSource[0].powerSourceId == 1;

        // The updatePowerSources function will take care of the rest (events, placing other PSUs)
        if (primaryPowerSource) { // Toggle primary power source
            selected.data.powerSource.length = 0;
            this.removeGateways(clone);
        } else {
            const primaryPSU = getPrimaryPSU(clone);
            if(primaryPSU) primaryPSU.data.powerSource.length = 0;

            selected.data.powerSource = [
                this.makePowerSource({})
            ];
        }

        pushState(clone);
    }

    private updatePowerSources() {
        const design = this.state.design;
        const primaryPSU = design.movableItems.find(x => x.data.powerSource?.length && x.data.powerSource[0].powerSourceId == 1);
        let updateEvent: IPowerSourceUpdatedEvent = {powerSupplies: []};

        if(primaryPSU) {
            for (const m of design.movableItems) {
                m.data.powerSource = m === primaryPSU ? [
                    this.makePowerSource({})
                ] : [];
    
                m.data.powerFrom = [];
            }
    
            updateEvent = this.calculatePowerSources(design, primaryPSU);

            if(this.addGatewayOnPSUSelection) {
                this.removeGateways(design);
                this.addGateways(design);
            }
        } else {
            for (const m of design.movableItems) {
                m.data.powerSource = [];
                m.data.powerFrom = [];
            }
        }

        MessageHub.broadcast<IPowerSourceUpdatedEvent>(Events.powerSourceUpdated, updateEvent);
    }

    /**
     * If the design is not powered, selects a joint as a power source
     * And calculates required power sources
     */
    private autoPlacePSUs() {
        const clone = this.state.design.clone();
        if(isDesignPowered(clone)) return;

        // Select a joint as power source
        const isClosed = getRelativeConnections(clone).filter(x => !x.movable.parentId).every(x => x.connected.length === 2);
        const noJoints = isClosed ? 2 : 1;
        const joint = clone.movableItems.find(x => x.behavior.canHavePowerSource === true && x.snaps && x.snaps.length == noJoints);
        if(!joint) return;

        joint.data.powerSource = [
            this.makePowerSource({})
        ];

        const changedEvent = this.calculatePowerSources(clone, joint);
        
        if(this.addGatewayOnPSUSelection) {
            this.addGateways(clone);
        }

        pushState(clone);
        MessageHub.broadcast<IPowerSourceUpdatedEvent>(Events.powerSourceUpdated, changedEvent);
    }

    private setControls(d: DesignState): void {
        const movablesThatCanHavePowerSource = d.movableItems.filter(x => x.behavior.canHavePowerSource === true && x.snaps?.length);
        const relativeConnectionsWithoutParent = getRelativeConnections(d).filter(x => !x.movable.parentId);
        const isClosed = relativeConnectionsWithoutParent.every(x => x.connected.length === 2);
        const snapsLen = isClosed ? 2 : 1;
        const movables = movablesThatCanHavePowerSource.filter(x => (x.snaps.length === snapsLen || x.data.powerSource?.length))

        const snaps = PositioningUtils.getMovableSnaps(movables);

        for (const sm of snaps) {
            const interactive = isClosed ? true : sm.movable.snaps.length == 1;
            const dot = new SvkDotControl(sm.movable, 0, [], this.settings, this.scene, interactive);
            this.addChild(dot);
        }
    }

    private changed(movables: MovableItem[]): boolean {
        if (this.movables.length !== movables.length) {
            return true;
        }

        for (let i = 0; i < movables.length; i++) {
            const m = movables[i];
            const c = this.movables[i];
            if (m !== c) {
                return true;
            }
        }

        return false;
    }

    /**
     * orderedConnections must be the first level movables (i.e. not have a parent)
     */
    private calculateTotalPowerDraw(d: DesignState, orderedConnections: MovableItem[]) {
        let totalDraw = 0;

        for (let i = 0; i < orderedConnections.length; i++) {
            const movable = orderedConnections[i];
            let consumers: MovableItem[] = [];
            
            if(!movable.behavior.canHavePowerSource) {
                consumers = d.movableItems.filter(x => x.parentId == movable.id && parseFloat(x.data?.power as any));

                if(parseFloat(movable.data?.power as any)) {
                    consumers.push(movable);
                }

                totalDraw += consumers.reduce((total, item) => total + parseFloat(item.data?.power as any), 0);      
            }
        }

        return totalDraw;
    }

    private balanceArrays(groups: number[][], indexes: number[][], max: number) {
        const calcSum = (arr: number[]) =>  arr.reduce((a, c) => a + c, 0);
        
        const sums: number[] = [];
        groups.forEach((v) => sums.push(calcSum(v)))
        //console.log("Starting sum", sums);
        
        // 4 iterations
        for(let m=0; m<4; m++) {
            
            for(let i=1; i<groups.length; i++) {
                const diff = sums[i] - sums[i-1];
                
                if(diff < 0) {
                    const grp = groups[i-1];
                    const idx = indexes[i-1];
                    
                    while(grp.length) {
                        // Take last number from the prev arr and try to add it to new group
                        const n = grp[grp.length - 1];
                        const j = idx[idx.length - 1];
                        
                        const newPrevSum = sums[i-1] - n;
                        const newSum = sums[i] + n;
                        const newDiff = newSum - newPrevSum;
                        
                        if(newSum <= max && newDiff <= 0) {
                            indexes[i].unshift(j);
                            groups[i].unshift(n);
                            grp.pop();
                            idx.pop();
                            sums[i] = newSum;
                            sums[i-1] = newPrevSum;
                        } else {
                            break;
                        }
                    }
                }
            }
        }
        
        //console.log("Resulting sum", sums);
        return [groups, sums];
    }

    /**
     * Calculates minimum number of PSUs needed for the configuration
     */
    private calculatePSUGroups(d: DesignState, orderedConnections: MovableItem[], maxPSUWatts: number) {
        const psuConnectedBars = [[]]; // Contains sub-array of bar indexes connected to each PSU
        const psuBarLoads = [[]]; // Contains sub-array of a bar total consumption connected to each PSU
        let currentLoad = 0;

        for (let i = 0; i < orderedConnections.length; i++) {
            const movable = orderedConnections[i];

            // Found a bar
            if(!movable.behavior.canHavePowerSource) {
                const consumers = d.movableItems.filter(x => x.parentId == movable.id && parseFloat(x.data?.power as any));

                if(parseFloat(movable.data?.power as any)) {
                    consumers.push(movable);
                }

                const barLoad = consumers.reduce((total, item) => total + parseFloat(item.data?.power as any), 0);
                currentLoad += barLoad;

                // If the power draw exceeds the PSU output, create a new PSU
                if(currentLoad > maxPSUWatts){
                    currentLoad = barLoad;
                    psuConnectedBars.push([]);
                    psuBarLoads.push([]);
                }

                if(barLoad > 0) {
                    psuConnectedBars[psuConnectedBars.length-1].push(i);
                    psuBarLoads[psuBarLoads.length-1].push(barLoad);
                }
            }
        }

        return [psuConnectedBars, psuBarLoads];
    }
    

    /**
     * Calculate the power sources and place them
     */
    private calculatePowerSources(d: DesignState, selectedPSU: MovableItem): IPowerSourceUpdatedEvent {
        const relativeConnections = getRelativeConnections(d);
        const relativeConnectionsWithoutParent = relativeConnections.filter(x => !x.movable.parentId);

        const orderedConnections = makeOrderedConnections(relativeConnectionsWithoutParent, selectedPSU);
        const totalDraw = this.calculateTotalPowerDraw(d, orderedConnections);

        this.makeConnectionsCW(relativeConnectionsWithoutParent, orderedConnections, selectedPSU);

        // powerSupplyWatts is an array from highest to lowest watts: [216, 185, 130]
        // Find the position that holds a value big enough:
        var maxPSUWattsIndex = 0;
        const powerSupplyWatts = this.state.applicationData.powerSupplyWatts;
        for (let i = powerSupplyWatts.length - 1; i >= 0; i--) {
            if (powerSupplyWatts[i] > totalDraw) {
                maxPSUWattsIndex = i;
                break;
            }
        }

        // Use powerSupplyWatts[1] if the total power consumption is <= the value of powerSupplyWatts[1] 
        // const maxPSUWattsIndex = totalDraw > this.state.applicationData.powerSupplyWatts[1] ? 0 : 1;
        const maxPSUWatts = this.state.applicationData.powerSupplyWatts[maxPSUWattsIndex]; // 216 || 130

        const [barIndexes, barLoads] = this.calculatePSUGroups(d, orderedConnections, maxPSUWatts);
        console.log(barLoads);
        
        this.balanceArrays(barLoads, barIndexes, maxPSUWatts);
        const psuIndexes = barIndexes.map((group) => group[0] - 1)
        // First PSU is always at index zero
        psuIndexes[0] = 0;
        
        const selectedPSUs: MovableItem[] = psuIndexes.map(i => orderedConnections[i]);

        selectedPSU.data.powerSource[0].effective = maxPSUWatts;

        psuIndexes.forEach((psu, i) => {
            let current = orderedConnections[psu];

            if(!current.behavior.canHavePowerSource){
                console.warn('Node is not a joint!', psu);
                return;
            }

            if(!current.data.powerSource.length){
                current.data.powerSource = [
                    this.makePowerSource({effective: maxPSUWatts, powerSourceId: i+1})
                ]
            }

            const bars = barIndexes[i];
            bars.forEach(j=>{
                const bar = orderedConnections[j];
                // loop over the bars and add all the consumers to current PSU
                let consumers = d.movableItems.filter(x => x.parentId == bar.id && parseFloat(x.data?.power as any));

                if(parseFloat(bar.data?.power as any)) {
                    consumers.push(bar);
                }

                consumers.forEach(x => {
                    current.data.powerSource[0].connected.push({ id: x.id });
                    x.data.powerFrom = [current.data.powerSource[0].powerSourceId]
                });
            })
        });
        
        return {powerSupplies: selectedPSUs};
    }

    private makeConnectionsCW(relativeConnectionsWithoutParent: IRelativeConnections[], orderedConnections: MovableItem[], selectedPSU: MovableItem) {
        if (isClosedConfiguration(relativeConnectionsWithoutParent) &&
            !areConnectionsClockwise(orderedConnections)) {
            orderedConnections.reverse();

            const selectedIndex = orderedConnections.indexOf(selectedPSU);
            if (selectedIndex > 0) {
                orderedConnections.splice(selectedIndex, 1);
                orderedConnections.unshift(selectedPSU);
            }
        }
    }

    private makePowerSource(options: object) {
        const defaultPSU = {
            nominal: null,
            effective: null,
            installation: null,
            productCode: [],
            powerSupplyIcon: null,
            connected: [],
            typology: "false",
            sideIndex: 0,
            powerSourceId: 1,
            appControlled: null
        }

        return Object.assign(defaultPSU, options);
    }

    private addGateways(design: DesignState) {
        const movables = design.movableItems;
        const psus = movables.filter(x => x.data.powerSource?.length);
        if(!psus.length) return;

        const relativeConnections = getRelativeConnections(design);
        const relativeConnectionsWithoutParent = relativeConnections.filter(x => !x.movable.parentId);
        const primaryPSU = getPrimaryPSU(design);
        const orderedConnections = makeOrderedConnections(relativeConnectionsWithoutParent, primaryPSU);

        this.makeConnectionsCW(relativeConnectionsWithoutParent, orderedConnections, primaryPSU);

        const gatewayTemplate = MovablesUtils.getInstance(this.gatewayType);
        const placements = [];

        // Find all the free spots on tracks for gateways
        for (let i = 0; i < psus.length; i++) {
            const psu = psus[i];
            const childrenIds = psu.data.powerSource[0].connected;  
            const daliChild = movables.find(x => childrenIds.find(c => x.id == c.id) && x.data.control == 'Dali')

            // Check if the connected consumers have dali driver
            if(!daliChild) continue;
            
            // Find the index of the movable next to the PSU in the connections
            let k = orderedConnections.findIndex( x => x == psu );
            k++;
            let foundFreeSpot = false;
            for (let j = k; j < orderedConnections.length; j++) {
                const movable = orderedConnections[j];

                // Reached the next psu
                if (movable.data.powerSource?.length)
                    break;

                // Check if the movable accepts gateway element as child
                if(!movable.childrenTypes.includes(this.gatewayType))
                    continue;
        
                const queryPos = this.findFreeSpaceForGateway(movable, psu, gatewayTemplate.width);

                if(queryPos) {
                    foundFreeSpot = true;
                    placements.push({position: queryPos, parent: movable, psu});
                    break;
                }
            }

            if(!foundFreeSpot) {
                // Abort
                // MessageHub.broadcast(Events.gatewayPlacementFailed);
                // return;

                let position = {x:0, y:0, z:0};

                const movable = orderedConnections[k];

                // Check if the movable accepts gateway element as child
                if(movable.childrenTypes.includes(this.gatewayType)) {
                    let point1 = PolygonUtils.rotatePoint(movable.snaps[0], movable).add(movable as any);
                    let point2 = PolygonUtils.rotatePoint(movable.snaps[1], movable).add(movable as any);
    
                    if( point1.distanceToSquared(psu as any) > point2.distanceToSquared(psu as any) ) {
                        const temp = point1;
                        point1 = point2;
                        point2 = temp;
                    }

                    const dir = new Vector3().subVectors(point2, point1).normalize();
                    position = point1.clone().addScaledVector(dir, gatewayTemplate.width * 0.5);
                }

                placements.push({position, parent: movable, psu});
            }
        }

        // Place all the gateways
        placements.forEach(placement=>{
            const gateway = MovablesUtils.getInstance(this.gatewayType);
            gateway.data.colorIta = SvkController.globalValues?.structureColor || 'NERO';
            gateway.data.power = this.gatewayPower;
            gateway.parentId = placement.parent.id;
            design.movableItems.push(gateway);

            const attachmentCenter = PolygonUtils.midPoint3D(gateway.attachmentPoints[0], gateway.attachmentPoints.at(-1));
            const shiftVec = PolygonUtils.rotatePoint(attachmentCenter, placement.parent);

            gateway.x = placement.position.x - shiftVec.x;
            gateway.y = placement.position.y - shiftVec.y;
            gateway.z = placement.position.z - shiftVec.z;

            gateway.rotationY = placement.parent.rotationY;
            gateway.rotationX = placement.parent.rotationX;
            gateway.rotationZ = placement.parent.rotationZ;

            // Prevent triggering update
            this.movables.push(gateway);

            // Add to PSU
            placement.psu.data.powerSource[0].connected.push({ id: gateway.id });
            gateway.data.powerFrom = [placement.psu.data.powerSource[0].powerSourceId]
        });
    }

    private findFreeSpaceForGateway(parent: MovableItem, psu: MovableItem, gatewaySize: number) {
        let point1 = PolygonUtils.rotatePoint(parent.snaps[0], parent).add(parent as any);
        let point2 = PolygonUtils.rotatePoint(parent.snaps[1], parent).add(parent as any);

        if( point1.distanceToSquared(psu as any) > point2.distanceToSquared(psu as any) ) {
            const temp = point1;
            point1 = point2;
            point2 = temp;
        }

        const placementVec = new Vector3().subVectors(point2, point1);
        let trackLen = placementVec.length();
        const placementDir = placementVec.clone().multiplyScalar(1/(trackLen || 1));

        // Add 5cm margin to each side of the track
        point1.addScaledVector(placementDir, 50);
        point2.addScaledVector(placementDir, -50);
        placementVec.multiplyScalar(Math.max(1 - ( 100 / (trackLen || 1)), 0))
        trackLen = Math.max(0, trackLen - 100);

        const children = this.state.design.movableItems.filter( m => m.parentId == parent.id );

        // No children to check against
        if(children.length < 1) {
            return gatewaySize <= trackLen ? point1.clone().addScaledVector(placementDir, gatewaySize * 0.5) : null;
        }

        const projVec = new Vec3()

        const sortedChildren = children.map( child => {
            // Find the position of the child along the track
            const position = projVec
                .subVectors(child as any, point1)
                .projectOnVector(placementVec).length()
            return {position, child}
        })
        // Sort the children based on distance
        .sort((a,b)=> a.position - b.position); 

        // Add the first and last points as well
        sortedChildren.unshift({position: 0, child: null})
        sortedChildren.push({position: trackLen, child: null})

        let pos = -1;

        for (let i = 1; i < sortedChildren.length; i++) {
            const element1 = sortedChildren[i-1];
            const element2 = sortedChildren[i];
            const child1Size = element1.child ? 
                PolygonUtils.pointsDistance(element1.child.currentAttachmentPoints[0], element1.child.currentAttachmentPoints.at(-1)) * 0.5 : 0;
            const child2Size = element2.child ? 
                PolygonUtils.pointsDistance(element2.child.currentAttachmentPoints[0], element2.child.currentAttachmentPoints.at(-1)) * 0.5 : 0;

            const dist = (element2.position - element1.position) - (child1Size + child2Size);

            if(dist < 1 || dist < gatewaySize) continue;

            // There is a space to fit the gateway
            pos = element1.position + child1Size;
            break;
        }

        return pos < 0 ? null : point1.clone().addScaledVector(placementDir, pos + gatewaySize * 0.5);
    }   

    private removeGateways(design: DesignState) {
        design.movableItems = design.movableItems.filter(m => m.type != this.gatewayType);
        this.movables = this.movables.filter(m => m.type != this.gatewayType);
    }

    private updateGateway(gateway: boolean): void {
        this.addGatewayOnPSUSelection = gateway; 

        if(gateway) {
            this.addGateways(this.state.design);
        } else {
            this.removeGateways(this.state.design);
        }
    }  
}