﻿import { IGraphicsData, IApplicationSettings } from "../../models/infrastructure/IApplicationSettings";
import { MovableItem } from "../../models/state/MovableItem";
import { DesignState } from "../../models/state/DesignState";
import { Display } from "../../models/common/Enums";
import { IGraphicsLoaded } from "./IGraphicsLoaded";
import { Material, MathUtils, Mesh, Object3D, OneMinusDstAlphaFactor, Vector3 } from "three";
import { CadFileGraphics } from "./CadFileGraphics";
import { IApplicationState } from "../../models/state/IApplicationState";
import { ISvkServerData } from "../../app-dialogs/controllers/SvkController";
import { ResourceTracker } from "../utils/ResourceTracker";

export class SuspendedCadFileGraphics extends CadFileGraphics {

    public static $inject = ["settings", "StateService"];
    private refresh = false;
    private highlightTracker = new ResourceTracker();
    private edgesTracker = new ResourceTracker();

    constructor(settings: IApplicationSettings, private state: IApplicationState<ISvkServerData>) {
        super(settings);
    }

    public createGraphics(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData, callback: IGraphicsLoaded): Object3D {
        const model = super.createGraphics(d, m, display, data, (object: Object3D)=>{
            // Update the cable length after it's created
            this.updateModel(object, m, data);
            callback(object);
        });

        this.updateModel(model, m, data);
    
        return model;
    }

    public createHighlight(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData, material: Material, partId?: string): Object3D {
        // Model is updated, recreate the highlight object
        if(this.refresh) {
            this.highlightTracker.dispose();
            this.cachedHighlight = null;
            this.refresh = false;
        }

        return this.highlightTracker.track(super.createHighlight(d, m, display, data, material, partId));
    }

    /**
     * Updates the cable length of the suspended 3D object
     * The object needs to have a mesh with name = 'Cable' in its hirearchy
     * Children with a name starting with 'Cable_' are considered as children of the cable mesh and moved accordingly
     */
    private updateModel(obj: Object3D, m: MovableItem, data: IGraphicsData){
        const modelData = this.state.applicationData?.models[m.type]; // Model config template

        if(!obj || !modelData || !(modelData as any).pendantLengthMinMaxDef) return;

        const cable = obj.getObjectByName('Cable') as Mesh;
        if(!cable || !cable.isMesh) return;

        let size = null;
        let localSize = null;
        // Measure the original world space and local space sizes of the cable and store them
        if(!cable.userData.worldSize) {
            size = new Vector3();
            localSize = new Vector3();
            cable.geometry.boundingBox.getSize(localSize);
            const bb = cable.geometry.boundingBox.clone()
            bb.applyMatrix4(cable.matrixWorld);
            bb.getSize(size);
            cable.userData.worldSize = size;
            cable.userData.localSize = localSize;
        }else{
            size = cable.userData.worldSize;
            localSize = cable.userData.localSize;
        }

        const [min, max, defaultVal] = (modelData as any).pendantLengthMinMaxDef as number[]; // In CM
        // size is in mm and needs to be converted to cm
        // Find a ratio between the model's cable original height and maximum cable length setting
        const sizeRatio = max / (size.y * 0.1); 

        let pLength = m.data.pendantLength; // User settings
        if(pLength == undefined){
            pLength = defaultVal;
        }

        pLength = MathUtils.clamp(pLength, min, max);
        // Convert the cable length settings to a value between 0 and 1
        // Use it to calculate the scale value of the cable object
        const lengthRatio = pLength / max;
        const yScale = sizeRatio * lengthRatio;
        // Since the yScale is a percentage, we can use it to move the attached items in the local space
        const childYOffset = (localSize.y - (localSize.y * yScale));

        cable.scale.setY(yScale);

        obj.traverse((o: Object3D)=>{
            if(o.name.startsWith('Cable_')){
                // The attached items are assumed to be in the origin of the local space
                o.position.y = childYOffset;
            }
        })

        this.refresh = true;
        this.updateEdges(obj, data);
    }

    /**
     * Recreates the edges geometry
     */
    private updateEdges(obj: Object3D, data: IGraphicsData){
        obj.traverse((o: Object3D) =>{
            if(o.name.startsWith('edge_')) {
                this.edgesTracker.track(o);
            }
        });

        this.edgesTracker.dispose();
        this.createEdges(obj, data);
    }
}