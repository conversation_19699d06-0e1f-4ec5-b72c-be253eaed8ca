import { Line2D, Vec2, Polygon } from "@immugio/three-math-extensions";
export function project3DPointsOnVerticalPlane(contour, holes) {
    // Assuming Y is the "height" vertical axis, then X and Z are the horizontal plane
    const points2d = contour.map(point => ({ x: point.x, y: point.z }));
    // Find two farthest points
    let longest;
    for (const p1 of points2d) {
        for (const p2 of points2d) {
            const line = Line2D.fromPoints(p1, p2);
            if (!longest || line.length > longest.length) {
                longest = line;
            }
        }
    }
    // Projected on a vertical plane, "y" will be height on UV map ("the distance from ground")
    // Then "x" is the distance from the start of the line.
    // GOTCHA: Potentially the "x" part will need to be improved to handle textures that are continuous between multiple objects
    function projectSet(points) {
        return points.map(point3d => {
            const x = longest.start.distanceTo(point3d.onPlan());
            const y = point3d.y;
            return new Vec2(x, y);
        });
    }
    return new Polygon(projectSet(contour), holes.map(hole => projectSet(hole)));
}
