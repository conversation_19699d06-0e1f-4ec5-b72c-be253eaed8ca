import { MessageHub } from "../../../infrastructure/MessageHub";
import { StateService } from "../../../state/StateService";
import { LabelsLogic } from "../../../logic/labels/LabelsLogic";
import { OverviewBaseController } from "./OverviewBaseController";
import { IOverviewItem } from "../../../models/dialogs/IOverviewItem";

export class OverviewController extends OverviewBaseController {

    public static $inject = ["MessageHub", "StateService", "LabelsLogic"];

    constructor(messageHub: MessageHub, state: StateService, private labelsLogic: LabelsLogic) {
        super(messageHub, state);
    }

    protected getListItems(): IOverviewItem[] {
        return this.labelsLogic.getList(this.state.design.movableItems);
    }

    protected setPowerStepLabels(): void {
        this.image.innerHTML = "IMAGE";
        this.qty.innerHTML = "WATT LEFT";
    }
}