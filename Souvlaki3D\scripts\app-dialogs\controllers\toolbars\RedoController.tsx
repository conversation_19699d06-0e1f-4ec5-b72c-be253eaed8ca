import { BaseActionController } from "./BaseActionController";
import { StateService } from "../../../state/StateService";
import { IToolbarActionOptions } from "./IToolbarActionOptions";
import { pushState } from "../../../state/actions/pushState";

export class RedoController extends BaseActionController {

    public static $inject = ["StateService"];

    constructor(private state: StateService, options: IToolbarActionOptions) {
        super(options);
        this.defaultInitialize();
    }

    public execute(): void {
        const redo = this.state.redo();
        if (redo) {
            this.update();
            pushState(redo.state);
        }
    }

    public update(): void {
        this.element.classList.toggle("disabled", !this.state.canRedo);
    }
}