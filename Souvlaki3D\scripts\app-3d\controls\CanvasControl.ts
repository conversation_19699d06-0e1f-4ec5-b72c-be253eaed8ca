﻿import { IInteractionOptions } from "../../models/positioning/IInteractionOptions";
import { IApplicationState } from "../../models/state/IApplicationState";
import { Interactive3D } from "../../models/scene/Interactive3D";
import { getMaterial } from "../utils/MaterialUtils";
import { Box3, Mesh, MeshStandardMaterial, Object3D, Vector3 } from "three";
import { Vec3 } from "@immugio/three-math-extensions";

export class CanvasControl {

    public interaction: IInteractionOptions;
    public overCursor: string;

    public object3D: Interactive3D = new Object3D();
    public vertices: Vector3[];
    public offset: Vector3;
    public box: Box3;

    protected highlightScale = 1.01;
    protected cachedHighlight: Interactive3D;
    protected activeHighlight: Object3D;
    protected highlightMaterial: MeshStandardMaterial;

    protected hovered: IInteractionOptions; // When hovered, in most cases will be the same as "this.interaction". In some cases it will be specific for a particular 3D element

    protected children: CanvasControl[] = [];

    constructor() {
        this.highlightMaterial = getMaterial("highlight");
    }

    public addChild(...child: CanvasControl[]): void {
        this.children.push(...child);
        this.object3D.add(...child.map(x => x.object3D));
    }

    public removeChild(child: CanvasControl): void {
        const index = this.children.indexOf(child);
        if (index !== -1) this.children.splice(index, 1);
        this.object3D.remove(child.object3D);
    }

    public update(s: IApplicationState): void {
        for (const child of this.children) {
            child.update(s);
        }
    }

    protected getHighlight(): Object3D {
        return this.cachedHighlight;
    }

    public mouseOver(interaction: IInteractionOptions): void {
        this.hovered = interaction;
        this.showHighlight();
    }

    public mouseOut(): void {
        this.hovered = null;
        this.hideHighlight();
    }

    public updateBoundingBox(adjustment?: number): void {
        this.box = new Box3().setFromObject(this.object3D);

        if (adjustment) {
            this.box.expandByVector(new Vector3(adjustment, adjustment, adjustment));
        }
    }

    public updateWordVertices(): void {
        if (!this.interaction?.movable?.behavior?.checkMeshCollision) {
            if (!this.box) {
                this.updateBoundingBox();
            }

            this.vertices = [
                new Vec3(this.box.min.x, this.box.min.y, this.box.min.z),
                new Vec3(this.box.min.x, this.box.min.y, this.box.max.z),
                new Vec3(this.box.min.x, this.box.max.y, this.box.min.z),
                new Vec3(this.box.min.x, this.box.max.y, this.box.max.z),
                new Vec3(this.box.max.x, this.box.min.y, this.box.min.z),
                new Vec3(this.box.max.x, this.box.min.y, this.box.max.z),
                new Vec3(this.box.max.x, this.box.max.y, this.box.min.z),
                new Vec3(this.box.max.x, this.box.max.y, this.box.max.z),
            ];

            return;
        }

        this.vertices = [];
        this.object3D.traverse(o => {
            if (o instanceof Mesh && o.name !== "highlight") {
                o.updateMatrixWorld(false);

                const vertices = o.geometry.getAttribute("position").array;
                for (let i = 0; i < vertices.length; i += 3) {
                    const v = new Vector3(vertices[i], vertices[i + 1], vertices[i + 2]);
                    this.vertices.push(o.localToWorld(v));
                }
            }
        });
    }

    protected showHighlight(): void {
        const highlight = this.getHighlight();

        if (highlight && !this.activeHighlight) {
            this.activeHighlight = highlight;
            this.object3D.add(this.activeHighlight);
        }
    }

    protected hideHighlight(): void {
        if (this.activeHighlight) {
            this.object3D.remove(this.activeHighlight);
            this.activeHighlight = undefined;
        }
    }

    protected clearChildren(): void {
        for (let i = this.object3D.children.length - 1; i >= 0; i--) {
            this.object3D.remove(this.object3D.children[i]);
        }

        this.children = [];
    }

    protected attachControlToObject3D(): void {
        if (!this.interaction) return;

        this.interaction.control = this;
        this.object3D.interaction = this.interaction;
    }
}