import { BaseActionController } from "./BaseActionController";
import { StateService } from "../../../state/StateService";
import { MovablesLogic } from "../../../logic/MovablesLogic";
import { IToolbarActionOptions } from "./IToolbarActionOptions";
import { pushState } from "../../../state/actions/pushState";
import { AvailableActions } from "../../../models/common/Enums";

export class RotateMovableController extends BaseActionController {

    public static $inject = ["StateService", "MovablesLogic"];

    constructor(private state: StateService, private movablesLogic: MovablesLogic, options: IToolbarActionOptions) {
        super(options);
        this.defaultInitialize();
    }

    public execute(): void {
        const clone = this.state.design.clone();
        const selected = clone.movableItems.filter(x => x.selected);
        this.movablesLogic.rotate(clone, selected, true);
        pushState(clone);
    }

    public update(): void {
        const selected = (this.state.design?.movableItems.filter(x => x.selected && !x.parentId) || []);
        const enabled = this.state.view.isActionAvailable(AvailableActions.Rotate) && selected.length;
        this.element.classList.toggle("disabled", !enabled);
    }
}