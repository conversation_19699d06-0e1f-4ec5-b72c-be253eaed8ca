﻿import { CanvasControl } from "./CanvasControl";
import { IApplicationState } from "../../models/state/IApplicationState";
import { Dimension } from "../../models/geometry/Dimension";
import { MeshUtils } from "../utils/MeshUtils";
import { TextUtils } from "../utils/TextUtils";
import { IPoint3D } from "../../models/geometry/IPoint3D";
import { Scene } from "../../models/scene/Scene";
import { PolygonUtils } from "../../logic/helpers/PolygonUtils";
import { BufferAttribute, BufferGeometry, DoubleSide, Line, LineBasicMaterial, Mesh, MeshBasicMaterial, Object3D, SphereGeometry } from "three";
import { settings } from "../../configurations/setup_global";

export class DimensionsControl extends CanvasControl {

    public static $inject = ["scene"];

    private readonly lineMaterial: LineBasicMaterial;
    private readonly symbolMaterial: MeshBasicMaterial;
    private readonly fontSize: number;
    private readonly dot: BufferGeometry;
    private readonly textMargin: number;

    private currentKeys: string[];
    private texts: { [key: string]: BufferGeometry } = {};

    private dims: Object3D[];

    constructor(private scene: Scene) {
        super();
        this.fontSize = settings.dimensions.fontSize;
        this.lineMaterial = new LineBasicMaterial({ color: settings.dimensions.color });
        this.symbolMaterial = new MeshBasicMaterial({ color: settings.dimensions.color, side: DoubleSide });
        this.dot = new SphereGeometry(20);
        this.textMargin = settings.dimensions.textMargin;
    }

    public update(s: IApplicationState): void {
        this.object3D.position.z = -s.view.offset.y;
        this.object3D.position.x = -s.view.offset.x;

        if (this.dims) {
            for (const dim of this.dims) {
               dim.quaternion.copy(this.scene.camera.quaternion);
            }
        }

        if (!s.diff.dimsChanged) return;

        this.currentKeys = []; // Reset keys used in this iteration
        this.dims = [];

        this.clearChildren(); // If no dims provided just clear old ones and retrun
        if (!s.view.dimensions || !s.view.dimensions.length) return;

        for (const dim of s.view.dimensions) {
            const p = this.getDim(dim);
            this.object3D.add(p);
        }

        for (const key in this.texts) { // Remove cached text geometries not used in this round
            if (this.texts.hasOwnProperty(key) && this.texts[key] && this.currentKeys.indexOf(key) === -1) {
                this.texts[key] = undefined;
            }
        }
    }

    private getDim(dim: Dimension): Object3D {
        const line1 = { x: dim.point1.x, y: dim.point1.y, z: dim.point1.z }; // Start of line the text lies on
        const line2 = { x: dim.point2.x, y: dim.point2.y, z: dim.point2.z }; // End of line the text lies on
        const mid = { x: (line1.x + line2.x) / 2, y: (line1.y + line2.y) / 2, z: (line1.z + line2.z) / 2 };

        const object3D = new Object3D();

        const textGeo = this.getTextGeo(dim.text);
        const textMesh = new Mesh(textGeo, this.symbolMaterial);
        const size = MeshUtils.getMeshSize(textGeo, true);

        const wrapper = new Object3D();
        wrapper.add(textMesh);
        object3D.add(wrapper);
        wrapper.position.set(mid.x, mid.y, mid.z);
        wrapper.quaternion.copy(this.scene.camera.quaternion);
        this.dims.push(wrapper);

        const mid1 = PolygonUtils.movePointOnLine(mid, line1, size.width / 2 + this.textMargin);
        const mid2 = PolygonUtils.movePointOnLine(mid, line2, size.width / 2 + this.textMargin);

        textMesh.position.x -= size.width / 2; // Center to the middle of line
        textMesh.position.y -= size.height / 2;

        this.addLine(object3D, line1, mid1);
        this.addLine(object3D, line2, mid2);

        if (dim.showArrows) this.addDots(object3D, line1, line2);

        return object3D;
    }

    private getTextGeo(text: string): BufferGeometry {
        if (this.currentKeys.indexOf(text) === -1) {
            this.currentKeys.push(text);
        }

        if (this.texts[text]) {
            return this.texts[text];
        }

        const textGeo = TextUtils.getTextGeo(text, this.fontSize, 1, false);
        this.texts[text] = textGeo;
        return textGeo;
    }

    private addLine(object3D: Object3D, line: IPoint3D, mid: IPoint3D): void {
        const geo = new BufferGeometry();
        const vertices = new Float32Array([
            line.x, line.y, line.z,
            mid.x, mid.y, mid.z,
        ]);
        geo.setAttribute("position", new BufferAttribute(vertices, 3));

        const mesh = new Line(geo, this.lineMaterial);
        object3D.add(mesh);
    }

    private addDots(object3D: Object3D, p1: IPoint3D, p2: IPoint3D): void {
        const dot1 = new Mesh(this.dot, this.symbolMaterial);
        dot1.position.set(p1.x, p1.y, p1.z);
        object3D.add(dot1);

        const dot2 = new Mesh(this.dot, this.symbolMaterial);
        dot2.position.set(p2.x, p2.y, p2.z);
        object3D.add(dot2);
    }
}