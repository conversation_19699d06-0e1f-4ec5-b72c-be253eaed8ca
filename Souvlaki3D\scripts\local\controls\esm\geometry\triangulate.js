import { ShapeUtils } from "three";
/**
 * Returns a set of triangles (faces) to be used for constructing walls
 * @param contour
 * @param openings holes in the main contour
 */
export function triangulate(contour, openings = []) {
    // If the points are too close to each other and overlap only due to floating point errors it can cause errors in triangulation
    // roundIfCloseToInteger is used to correct this
    const points = contour.map(p => p.clone().roundIfCloseToInteger());
    const holes = openings.map(o => o.map(p => p.clone().roundIfCloseToInteger()));
    // Triangulation breaks if there last and first points are the same
    if (points[0].equals(points.at(-1))) {
        points.pop();
    }
    return {
        points: [
            ...points,
            ...holes.flat()
        ],
        faces: ShapeUtils.triangulateShape(points, holes)
    };
}
