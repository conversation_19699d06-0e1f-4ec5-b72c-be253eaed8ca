import { MovableItem } from "../../models/state/MovableItem";
import { DesignState } from "../../models/state/DesignState";
import { Direction, Display } from "../../models/common/Enums";
import { IGraphicsData } from "../../models/infrastructure/IApplicationSettings";
import { MovableGraphics } from "./MovableGraphics";
import { BoxGeometry, Color, Material, Mesh, MeshBasicMaterial, Object3D } from "three";
import { Container3D } from "./Container3D";
import { StripFromPoints } from "./StripFromPoints";
import { LedUtils } from "../../logic/helpers/LedUtils";
import { MaterialUtils } from "../utils/MaterialUtils";
import { adjustColor } from "../utils/AdjustColor";
import { Highlight } from "../utils/Highlight";

export class LedAdapterGraphics extends MovableGraphics {

    private lastActive: boolean;
    private lastMaterialCode: string;
    private lastMovableItem: MovableItem;
    private lastStripLength: number;
    private lastStripSide: number;

    protected createMesh(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData, materials: Material[]): Object3D {
        const geometry = new BoxGeometry(m.width, m.height, m.depth);
        const box = new Mesh(geometry, materials[0]);
        return new Container3D(box, LedAdapterGraphics.getStrip(d, m, (materials[0] as MeshBasicMaterial).color));
    }

    public createHighlight(): Object3D {
        if (!this.cachedHighlight && this.cachedGraphics) {
            this.cachedHighlight = Highlight.create(this.cachedGraphics);
        }

        return this.cachedHighlight;
    }

    private static getStrip(d: DesignState, m: MovableItem, color: Color): Object3D {
        if (!m.active && m.data.stripLength && typeof m.data.stripSide === "number") {
            const points = LedUtils.getUntrimmedPathConnectedToMovableAtLength(m, d.movableItems, m.data.stripSide, m.data.stripLength);
            const lighterColor = adjustColor(color, 0.5);
            const strip = StripFromPoints(m, points, lighterColor, 20);
            strip.position.y -= (m.attachDirection === Direction.Top ? -15 : 15);
            return strip;
        }
        return new Object3D();
    }

    protected getMaterial(d: DesignState, m: MovableItem, data: IGraphicsData): Material[] {
        const materialCode = LedAdapterGraphics.getMaterialCode(m, data);
        return materialCode ? [MaterialUtils.getMaterial(materialCode)] : undefined;
    }

    protected shouldUpdate(d: DesignState, m: MovableItem, display: Display, data: IGraphicsData): boolean {
        const materialCode = LedAdapterGraphics.getMaterialCode(m, data);

        if (
            !m.equals(this.lastMovableItem) ||
            materialCode !== this.lastMaterialCode ||
            m.active !== this.lastActive ||
            m.data.stripLength !== this.lastStripLength ||
            m.data.stripSide !== this.lastStripSide
        ) {
            this.lastMaterialCode = materialCode;
            this.lastMovableItem = m;
            this.lastActive = m.active;
            this.lastStripLength = m.data.stripLength;
            this.lastStripSide = m.data.stripSide;
            return true;
        }

        return false;
    }

    private static getMaterialCode(m, data): string {
        return data?.currentFinishes?.[0] || m?.finishes[0] || data?.finishes[0];
    }
}