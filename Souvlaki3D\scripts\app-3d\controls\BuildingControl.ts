﻿import { CanvasControl } from "./CanvasControl";
import { IPoint2D } from "../../models/geometry/IPoint2D";
import { IApplicationState } from "../../models/state/IApplicationState";
import { Direction, Display, InteractionType } from "../../models/common/Enums";
import { BuildingUtils } from "../../logic/helpers/BuildingUtils";
import { DesignState } from "../../models/state/DesignState";
import { MeshUtils } from "../utils/MeshUtils";
import { getMaterial } from "../utils/MaterialUtils";
import { ViewState } from "../../models/state/ViewState";
import { DoubleSide, Material, Mesh } from "three";
import { SlopesSet } from "../../logic/helpers/geometry/SlopesSet";
import { getWallsBoundingPolygon } from "../../logic/helpers/building/getWallsBoundingPolygon";
import { <PERSON><PERSON><PERSON>per, Wall, WallSet, SizedMaterial } from "@immugio/controls";
import { Vec3 } from "@immugio/three-math-extensions";

export class BuildingControl extends CanvasControl {

    private wall: SizedMaterial;
    private transparent: SizedMaterial;
    private semiTransparent: SizedMaterial;

    constructor() {
        super();

        this.setBaseMaterials();

        this.interaction = { interactionType: InteractionType.Building };
        this.attachControlToObject3D();
    }

    public update(s: IApplicationState): void {
        if (!s.diff.structureChanged && !s.design.refresh) return;
        s.design.refresh = false;

        const d = s.design;

        this.clearChildren();

        if (!s.view.isEditingWalls) {
            this.object3D.add(this.createFloor(s));
            this.object3D.add(this.createRoof(s));
            this.object3D.add(...this.createWalls(d, s.view));
            this.object3D.position.set(-s.view.offset.x, 0, -s.view.offset.y);
        }
    }

    private createRoof(s: IApplicationState): Mesh {
        const visible = (!(s.view.closesElevations & Direction.Top) && s.view.displayMode !== Display.BottomView);
        const mesh = this.createLid(s.design, s.design.roofWidth, visible);
        mesh.position.y += s.design.height;
        return mesh;
    }

    private createFloor(s: IApplicationState): Mesh {
        const visible = (!(s.view.closesElevations & Direction.Bottom) && s.view.displayMode !== Display.TopView);
        const mesh = this.createLid(s.design, s.design.baseHeight, visible);
        mesh.position.y += s.design.baseHeight;
        return mesh;
    }

    private createLid(d: DesignState, height: number, visible: boolean): Mesh {
        const material = visible ? getMaterial(d.baseFinish,  "_main_floor") : this.transparent;
        material.side = DoubleSide;
        const contour = getWallsBoundingPolygon(d.walls).contour;
        return this.extrusion(contour, undefined, height, material);
    }

    private createWalls(d: DesignState, v: ViewState): Mesh[] {
        const transparent = v.displayMode & Display.PerspectiveView ? this.semiTransparent : this.transparent;
        const showIndexes = Math.abs(v.phi) === 90 ? null : BuildingUtils.getAllowedWalls(d.walls, v.closesElevations, v.displayMode);
        const slopes = new SlopesSet(d.height - d.roofWidth);

        const wallSet = new WallSet();
        for (let i = 0, l = d.walls.length; i < l; i++) { // Each elevation is created as a separate Mesh object
            const iWall = d.walls[i];
            const isTransparent = (showIndexes && !showIndexes.includes(i));
            const endpoints = [Vec3.fromPoint(iWall.start).onPlan(), Vec3.fromPoint(iWall.end).onPlan()];
            const external = getMaterial(d.extFinishes[i] || d.extFinishes[0]);
            const internal = getMaterial(d.intFinishes[i] || d.intFinishes[0]);
            const reveals = getMaterial(d.intFinishes[i] || d.intFinishes[0], "_reveals");
            reveals.side = DoubleSide;
            const wall = new Wall(endpoints, iWall.start.y, iWall.end.y, d.baseHeight, d.wallThickness, external, internal, this.wall, [], [], isTransparent);
            wallSet.addWall(wall);
        }

        return WallHelper.render(wallSet, slopes, d.baseHeight, this.wall, transparent);
    }

    private extrusion(outer: IPoint2D[], inners: Array<IPoint2D>[], height: number, materials: Material): Mesh {
        return MeshUtils.createExtrusion(outer, inners, height, materials);
    }

    private setBaseMaterials(): void {
        this.transparent = getMaterial("defaultWallFinishTransparent");
        this.semiTransparent = getMaterial("defaultWallFinishSemiTransparent");
        this.wall = getMaterial("wallsTopFinish");
        this.wall.side = DoubleSide;
    }
}