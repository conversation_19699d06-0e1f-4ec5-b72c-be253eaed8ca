﻿import { IApplicationSettings } from "../../models/infrastructure/IApplicationSettings";
import { IApplicationState } from "../../models/state/IApplicationState";
import { MovableItem } from "../../models/state/MovableItem";
import { CanvasControl } from "./CanvasControl";
import { MovableItemControl } from "./MovableItemControl";
import { MovablesUtils } from "../../logic/helpers/MovablesUtils";
import { Activator } from "../../infrastructure/Activator";
import { Object3D, Scene as ThreeScene } from "three";

export class SceneControl extends CanvasControl {

    public zoomObject: Object3D;
    public movableControls: MovableItemControl[] = [];

    public object3D: ThreeScene;

    constructor(private settings: IApplicationSettings) {
        super();
        this.object3D = this.zoomObject = new ThreeScene();
        Activator.register("movableControls", this.movableControls);

        settings.app.controls?.forEach(x => this.addChild(
            (typeof x === "string" ? Activator.getUnique<CanvasControl>(x) : Activator.getUnique<CanvasControl>(x.type, x.options))
        ));
    }

    public update(s: IApplicationState): void {
        if (s.diff.isNewDesign) this.clearControls();

        this.syncMovableControls(s);
        this.updateMovableItems(s);

        super.update(s);
    }

    private clearControls(): void {
        let i: number;

        for (i = this.movableControls.length - 1; i >= 0; i--) {
            this.removeObject(this.movableControls[i]);
            this.movableControls.pop();
        }
    }

    public removeObject(control: CanvasControl): void {
        this.object3D.remove(control.object3D);
        control.object3D.remove(...control.object3D.children); // Remove also children of the control because when dragging a "Group" objects would be returned to scene in Dragger - SceneUtils.detach
    }

    private updateMovableItems(s: IApplicationState): void {
        for (const mov of this.movableControls) {
            mov.update(s);
        }
    }

    private syncMovableControls(s: IApplicationState): void {
        const d = s.design;
        let i: number, l: number, m: MovableItem, c: MovableItemControl, found: boolean;

        // Quick check if there is anything to sync (most cases there is not ... )
        if (s.design.movableItems.length === this.movableControls.length) {
            let needsSync = false;
            for (i = 0, l = this.movableControls.length; i < l; i++) {
                if (this.movableControls[i].interaction.movable !== d.movableItems[i]) {
                    needsSync = true;
                    break;
                }
            }
            if (!needsSync) return;
        }

        // 1) Cases where the MovableItem has been added but the MovableItemControl is missing
        const movablesToAdd: MovableItem[] = [];
        for (m of d.movableItems) {

            found = false;

            for (c of this.movableControls) {
                if (m.id === c.interaction.movable.id) {
                    found = true;
                    break;
                }
            }

            if (!found) {
                movablesToAdd.push(m);
            }
        }

        // 2) Cases where MovableItem has been removed but the MovableItemControl is still in canvas
        for (i = this.movableControls.length - 1; i >= 0; i--) {

            c = this.movableControls[i];
            found = false;

            // If several controls were removed in one step due to a parent-child relationship and the parent was after the child in the array
            // "i" will get out of index - e.g. "i" is still 50 but movables.length is 45. Skip iterations as necessary
            if (!c) continue;

            for (m of d.movableItems) {
                if (c.interaction.movable.id === m.id) {
                    found = true;
                    break;
                }
            }

            if (!found) {
                this.removeMovable(this.movableControls[i].interaction.movable);
            }
        }
        this.addMovable(s, movablesToAdd);

        // 3) Cases where the MovableItems in DesignState and in MovableItemControls match by property values but are different instances
        // e.g. due tu an UNDO where the whole DesignState is replaced
        for (m of d.movableItems) {

            for (c of this.movableControls) {
                if (c.interaction.movable.id === m.id)
                    c.interaction.movable = m;
            }
        }

        // Order the movable controls array so that it matches the d.movableItems array order
        for (i = 0, l = d.movableItems.length; i < l; i++) {
            d.movableItems[i].index = i;
        }
        this.movableControls.sort((a, b) => a.interaction.movable.index - b.interaction.movable.index);
    }

    private removeMovable(m: MovableItem): void {
        const control = this.movableControls.filter(c => c.interaction.movable.id === m.id)[0];

        if (control) {
            const index = this.movableControls.indexOf(control);
            if (index !== -1) this.movableControls.splice(index, 1);
            this.removeObject(control);
        }
    }

    private addMovable(s: IApplicationState, movables: MovableItem[]): MovableItemControl[] {
        const controls: MovableItemControl[] = [];

        for (const m of movables) {

            let skin = this.settings.movables.find(v => v.type === MovablesUtils.designTimeType(m.type)).skin;
            skin = skin ? JSON.parse(JSON.stringify(skin)) : undefined;

            const control = new MovableItemControl(s.design, m, skin, s.view.displayMode, this.settings);
            this.object3D.add(control.object3D);
            controls.push(control);

            const index = s.design.movableItems.indexOf(m);
            if (index !== -1) this.movableControls.splice(index, 0, control);
        }

        return controls;
    }
}