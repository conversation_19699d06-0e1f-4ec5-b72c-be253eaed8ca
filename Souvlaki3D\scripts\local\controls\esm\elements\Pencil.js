import { CylinderGeometry, Mesh, Group, MeshBasicMaterial } from "three";
export function Pencil() {
    const total = 400, bodyLength = total * .8, tipLength = total - bodyLength, r = 20;
    const tube = new Mesh(new CylinderGeometry(r, r, bodyLength, 64), new MeshBasicMaterial({ color: "#04AA6D" }));
    tube.position.y = total / 2 + tipLength / 2;
    const tip = new Mesh(new CylinderGeometry(r, 1, tipLength), new MeshBasicMaterial({ color: "#BB8FCE" }));
    tip.position.y = tipLength / 2;
    const o = new Group();
    o.add(tube, tip);
    o.rotateY(-Math.PI / 4);
    o.rotateX(-Math.PI / 2);
    return o;
}
