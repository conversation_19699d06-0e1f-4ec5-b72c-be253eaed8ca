# Architecture Overview

## System Architecture

The Souvlaki 3D Configuration Engine is built on a modular, event-driven architecture that separates concerns between 3D rendering, state management, business logic, and UI components.

```mermaid
graph TD
    A[Core] --> B[Scene Management]
    A --> C[State Service]
    A --> D[Message Hub]
    
    B --> E[Three.js Renderer]
    B --> F[Camera Controls]
    B --> G[Scene Controls]
    
    C --> H[Design State]
    C --> I[View State]
    C --> J[Undo/Redo]
    
    D --> K[UI Events]
    D --> L[3D Events]
    D --> M[State Events]
```

## Core Components

### Core (Core.ts)
The central orchestrator that:
- Initializes the 3D environment
- Manages user interactions
- Coordinates between different subsystems
- Handles event routing
- Manages the render loop

Key responsibilities:
```typescript
class Core {
    constructor(
        private movablesLogic: MovablesLogic,
        private buildingLogic: BuildingLogic,
        private dimensionsLogic: DimensionsLogic,
        private zonesLogic: ZonesLogic,
        private labelsLogic: LabelsLogic,
        private alerts: AlertService,
        private state: StateService,
        private controllers: IController[],
        private messageHub: MessageHub,
        private scene: Scene
    )
```

### State Management (StateService.ts)
Maintains application state through:
- Design State: Current configuration
- View State: Camera and UI settings
- Undo/Redo History
- State Comparison for Updates

```typescript
class StateService {
    public design: DesignState;     // Current building
    public view: ViewState;         // Current view
    public diff: StateDiff;         // Changes between states
    private undoStack: UndoStep[];  // History management
    private redoStack: UndoStep[];
}
```

### Event System (MessageHub)
Facilitates communication between components through:
- Event broadcasting
- Event subscription
- Asynchronous message passing

## Key Subsystems

### 1. Movables System
Handles all movable items in the 3D space:
- Placement and positioning
- Rotation and transformation
- Connection management
- Collision detection

```typescript
class MovablesLogic {
    // Core operations
    public insert(d: DesignState, dragged: MovableItem, insertPoints: ISnapPoint[]): void
    public swap(d: DesignState, current: MovableItem, replacement: MovableItem): MovableItem
    public rotate(d: DesignState, movables: MovableItem[], clockwise: boolean): void
}
```

### 2. Scene Management
Controls the 3D environment:
- Camera positioning
- View modes (perspective/orthographic)
- Scene updates
- Rendering optimization

### 3. Positioning System
Manages spatial relationships:
- Snap points
- Connection validation
- Distance constraints
- Wall attachments

## Data Flow

1. **User Interaction**
   ```mermaid
   sequenceDiagram
       User->>Core: Interaction Event
       Core->>State: Update State
       State->>Scene: Trigger Update
       Scene->>Renderer: Render Changes
       Core->>MessageHub: Broadcast Event
       MessageHub->>UI: Update Interface
   ```

2. **State Updates**
   ```mermaid
   sequenceDiagram
       State->>Comparer: Compare States
       Comparer->>Diff: Generate Diff
       Diff->>Controls: Update Controls
       Controls->>Scene: Update Scene
       Scene->>Renderer: Render Frame
   ```

## Component Communication

The system uses a combination of:
1. Direct method calls for synchronous operations
2. Event broadcasting for asynchronous updates
3. State diffing for optimized renders

Example event flow:
```typescript
// Event subscription
this.messageHub.subscribe(Events.zoomIn, () => this.zoom(1));
this.messageHub.subscribe(Events.startInserting, (menuItem) => this.startInserting(menuItem));
this.messageHub.subscribe<DesignState>(Events.setState, (d) => this.setState(d));
```

## Performance Considerations

1. **State Management**
   - Efficient state diffing
   - Optimized undo/redo stacks
   - Selective updates

2. **Rendering**
   - Request animation frame optimization
   - Selective scene updates
   - Camera optimization

3. **Event System**
   - Event debouncing
   - Optimized event routing
   - Minimal event payload

## Error Handling

The system implements robust error handling:
- State validation
- Operation constraints
- User feedback
- Recovery mechanisms

## Future Considerations

1. **Scalability**
   - Component lazy loading
   - State persistence
   - Performance optimization

2. **Extensibility**
   - Plugin architecture
   - Custom component support
   - API expansion

3. **Maintenance**
   - Code modularization
   - Testing coverage
   - Documentation updates
