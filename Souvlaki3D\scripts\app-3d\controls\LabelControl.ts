import { CanvasControl } from "./CanvasControl";
import { Scene } from "../../models/scene/Scene";
import { IApplicationState } from "../../models/state/IApplicationState";
import { MovableItem } from "../../models/state/MovableItem";
import { MeshBasicMaterial, Object3D, ShapeGeometry, Vector3 } from "three";
import { TextUtils } from "../utils/TextUtils";
import { PositioningUtils } from "../../logic/helpers/PositioningUtils";
import { Display, ElementShape, PlacementMode } from "../../models/common/Enums";
import { Label, LabelPosition } from "../../models/state/Label";
import { IMovableSnap } from "../../models/positioning/IMovableSnap";
import { SnapVector } from "../../models/positioning/ISnapPoint";
import { Mesh3D } from "../graphics/Mesh3D";
import { RoundedRectangleShape } from "../graphics/RoundedRectangleShape";
import { Vec3 } from "@immugio/three-math-extensions";

export class LabelControl extends CanvasControl {

    public static $inject = ["scene"];

    private checksum: string;
    private readonly material = new MeshBasicMaterial({ color: "black", depthTest: false });
    private readonly symbolBackgroundMaterial = new MeshBasicMaterial({ color: "#ffffff", opacity: 0.2, transparent: true, depthTest: false });
    private readonly backgroundMargin = 40;

    constructor(private scene: Scene) {
        super();
    }

    public update(s: IApplicationState): void {
        if (!s.view.showLabels) {
            if (this.object3D.children.length) {
                this.clearChildren();
                this.checksum = null;
            }
            return;
        }

        for (const child of this.object3D.children) {
            child.quaternion.copy(this.scene.camera.quaternion);
        }

        const currentLabels = this.filterLabelsByView(s.design.movableItems, s.view.labels, s.view.displayMode);
        const checksum = currentLabels.map(x => x.id).join();
        if (checksum === this.checksum) {
            return;
        }

        this.checksum = checksum;
        this.clearChildren();
        const allSnaps = PositioningUtils.getMovableSnaps(s.design.movableItems);

        for (const label of currentLabels) {
            const movable = s.design.movableItems.find(x => x.id === label.movableIds[0]);
            const parent = movable?.parentId ? s.design.movableItems.find(x => x.id === movable.parentId) : undefined;

            const mesh = this.getTextMesh(label.text);
            this.object3D.add(mesh);

            mesh.position.copy(this.getPosition(label, movable, parent, allSnaps));

            mesh.quaternion.copy(this.scene.camera.quaternion);
        }

        this.object3D.position.set(-s.view.offset.x, 0, -s.view.offset.y);
    }

    private filterLabelsByView(allMovables: MovableItem[], allLabels: Label[], display: Display) {
        const movables = this.filterMovablesByView(allMovables, display);
        const ids = movables.map(x => x.id);
        return allLabels.filter(x => x.movableIds.some(y => ids.includes(y)));
    }

    private filterMovablesByView(allMovables: MovableItem[], display: Display): MovableItem[] {
        const parentOrItem = (it: MovableItem) => it.parentId ? allMovables.find(x => x.id === it.parentId) : it;

        switch (display) {
            case Display.BottomView:
            case Display.TopView:
                return allMovables.filter(x => parentOrItem(x).placementMode & (PlacementMode.Ceiling | PlacementMode.Corners | PlacementMode.ExternalWalls | PlacementMode.Horizontal));

            case Display.FrontView:
                return allMovables.filter(x => parentOrItem(x).elevation === 2);

            case Display.SideView:
                return allMovables.filter(x => parentOrItem(x).elevation === 1);

            default:
                return allMovables;
        }
    }

    private getPosition(label: Label, movable: MovableItem, parent: MovableItem, allSnaps: IMovableSnap[]): Vector3 {
        const move = 100;
        const directionItem = parent || movable;
        const pos = label.getLabelOrigin(allSnaps);

        if (label.position === LabelPosition.ItemCenter && movable.behavior.itemCenterLabelPosition) {
            return SnapVector.fromSources(directionItem, [movable.behavior.itemCenterLabelPosition], movable)[0];
        }

        else if (label.position === LabelPosition.PowerSupply && movable.behavior.powerSupplyLabelPosition) {
            return SnapVector.fromSources(movable, [movable.behavior.powerSupplyLabelPosition], movable)[0];
        }

        // For angle elements it can stay like that, for linear move out of center
        else if (directionItem.shape === ElementShape.Linear) {
            const size = PositioningUtils.getRotatedSize(movable);

            if (directionItem.placementMode === PlacementMode.Ceiling || directionItem.placementMode === PlacementMode.Horizontal) {
                if (size.width < size.depth) {
                    pos.x -= move;
                } else {
                    pos.z -= move;
                }

            } else {
                const horizontal = directionItem.elevation % 2 === 0;
                if (horizontal) {
                    if (size.width < size.height) {
                        pos.x -= move;

                    } else {
                        pos.y -= move;
                    }

                } else {
                    if (size.depth > size.height) {
                        pos.y -= move;

                    } else {
                        pos.z -= move;
                    }
                }
            }
        }

        return pos;
    }

    private getTextMesh(key: string): Object3D {
        const letter = TextUtils.getText(key, 70, this.material, 10, true);
        letter.geometry.computeBoundingBox();
        letter.geometry.center();
        letter.renderOrder = 999;

        const size = letter.geometry.boundingBox.getSize(new Vec3());
        const height = size.y + this.backgroundMargin;
        const width = size.x + this.backgroundMargin * 2;
        const shape = new RoundedRectangleShape(-width / 2, -height / 2, size.x / 4,  height, width);
        const background = new Mesh3D(new ShapeGeometry(shape), this.symbolBackgroundMaterial).setRenderOrder(998);

        return new Object3D().add(letter, background);
    }
}