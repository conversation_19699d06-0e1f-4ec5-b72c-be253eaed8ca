﻿import { CanvasControl } from "./CanvasControl";
import { MovableGraphics } from "../graphics/MovableGraphics";
import { DesignState } from "../../models/state/DesignState";
import { AvailableActions, Direction, Display, InteractionType } from "../../models/common/Enums";
import { MovableItem } from "../../models/state/MovableItem";
import { IApplicationSettings, ISkin } from "../../models/infrastructure/IApplicationSettings";
import { IApplicationState } from "../../models/state/IApplicationState";
import { Activator } from "../../infrastructure/Activator";
import { CanvasUtils } from "../utils/CanvasUtils";
import { MaterialUtils } from "../utils/MaterialUtils";
import { MovablesUtils } from "../../logic/helpers/MovablesUtils";
import { Interactive3D } from "../../models/scene/Interactive3D";
import { BoxGeometry, Mesh, Object3D } from "three";
import { getMovableInteractionType } from "../../logic/helpers/movables/getMovableInteractionType";

export class MovableItemControl extends CanvasControl {

    private graphics: MovableGraphics;
    private designState: DesignState;

    private lastDisplayMode: Display;
    private lastInvalid: boolean;
    private lastDetached: boolean;
    private lastSelected: boolean;
    private lastColor: string[];
    private lastHighlightPart: string;

    constructor(d: DesignState, m: MovableItem, private skin: ISkin, displayMode: Display, private settings: IApplicationSettings) {
        super();

        this.interaction = {
            interactionType: getMovableInteractionType(m),
            movable: m
        };

        this.overCursor = "pointer";
        this.redraw(d, displayMode);
        this.attachControlToObject3D();
    }

    private redraw(d: DesignState, displayMode: Display): void {
        this.designState = d;

        this.clearChildren(); // Always create a box representing the base shape
        const m = this.interaction.movable;
        const geo = new BoxGeometry(m.width, m.height, m.depth);
        const material = MaterialUtils.getMaterial("initial");
        this.object3D.add(new Mesh(geo, material));
        this.object3D.name = `${m.type}-${m.id}`;

        this.cachedHighlight = new Mesh(geo, this.highlightMaterial); // Highlight box representing the base shape
        this.cachedHighlight.scale.set(this.highlightScale, this.highlightScale, this.highlightScale);
        this.cachedHighlight.interaction = { interactionType: InteractionType.Highlight };
        this.cachedHighlight.name = "highlight";
        this.cachedHighlight.renderOrder = -1;

        if (this.skin && this.skin.graphics3D) { // Replace by algorithm generated graphics if available
            let currentFinishes: string[];
            if (m.detached) {
                currentFinishes = [this.settings.textures.detached.color];
            } else if (m.selected) {
                currentFinishes = [this.settings.textures.selected.color];
            } else if (m.data.invalid) {
                currentFinishes = [this.settings.textures.invalid.color];
            } else if (m.data.color) {
                currentFinishes = m.data.color;
            }

            const data = { ...this.skin.data, currentFinishes };
            if (!this.graphics) this.graphics = Activator.getUnique(this.skin.graphics3D);
            const object3D = this.graphics.createGraphics(d, m, displayMode, data, o => this.updateGraphics(o));
            if (object3D) this.updateGraphics(object3D);
        }

        this.lastDisplayMode = displayMode;
        this.lastInvalid = m.data.invalid;
        this.lastDetached = m.detached;
        this.lastSelected = m.selected;
        this.lastColor = m.data.color;
    }

    public update(s: IApplicationState): void {
        const m = this.interaction.movable;

        if (this.interaction.movable.data.lightingControl && s.view.hasAction(AvailableActions.Power)) {
            this.overCursor = null;
        } else {
            this.overCursor = "pointer";
        }

        if (((!this.hovered && !m.highlight) || this.lastHighlightPart !== m.highlightPart) && this.activeHighlight) {
            this.lastHighlightPart = m.highlightPart;
            this.hideHighlight();
        }

        if ((this.hovered || m.highlight) && !this.activeHighlight) {
            this.showHighlight();
        }

        if (m.refresh || m.data.invalid !== this.lastInvalid || m.detached !== this.lastDetached || m.selected !== this.lastSelected || m.data.color !== this.lastColor) {
            this.redraw(s.design, s.view.displayMode);
            m.refresh = false;
        }

        if (s.view.activeItem?.interactionType & (InteractionType.DraggingItem | InteractionType.InsertingItem)) {
            return;
        }

        this.object3D.position.set(m.x - s.view.offset.x, m.y, m.z - s.view.offset.y);
        this.object3D.rotation.set(0, 0, 0);
        CanvasUtils.applyRotation(this.object3D, m);
    }

    public getHighlight(): Object3D {
        if (this.graphics) { // If the movable has a generator that supports highlight provide it
            const partId = MovablesUtils.isMultiPart(this.interaction.movable) ? this.hovered && this.hovered.partId || this.interaction.movable.highlightPart : null;
            const hoverHighlight = this.graphics.createHighlight(this.designState, this.interaction.movable, this.lastDisplayMode, this.skin.data, this.highlightMaterial, partId);
            if (hoverHighlight) {
                return this.wrapAndSetInteraction(hoverHighlight);
            }
        }

        return this.cachedHighlight; // Otherwise, return the basic box
    }

    private wrapAndSetInteraction(o: Object3D): Object3D {
        const object3D = new Object3D().add(o);
        this.setInteraction(object3D);
        if (this.skin?.data?.rotateXWhenAttachedToTop) {
            object3D.rotation.x = this.interaction.movable.attachDirection === Direction.Top ? Math.PI : 0;
        }
        return object3D;
    }

    private updateGraphics(o: Object3D): void {
        this.clearChildren();
        this.object3D.add(this.wrapAndSetInteraction(o));
    }

    private setInteraction(object3D: Object3D): void {
        object3D.traverse(x => {
            if (x.name.startsWith("Part_")) {
                (x as Interactive3D).interaction = {
                    interactionType: getMovableInteractionType(this.interaction.movable),
                    movable: this.interaction.movable,
                    partId: x.name,
                    control: this
                };
            }
        });
    }
}