import { IApplicationState } from "../../../models/state/IApplicationState";
import { IOverviewItem } from "../../../models/dialogs/IOverviewItem";
import { Events } from "../../../models/infrastructure/Events";
import { ItemPart } from "../../../models/state/MovableItem";
import { Steps } from "../../../models/common/Enums";
import { MessageHub } from "../../../infrastructure/MessageHub";
import { StateService } from "../../../state/StateService";
import { BaseController } from "../BaseController";
import { getElementAsync } from "@immugio/jsx-and-html-utils";
import { currentStep } from "../../utils/currentStep";

export abstract class OverviewBaseController extends BaseController {

    public static $inject = ["MessageHub", "StateService", "LabelsLogic"];

    private hoverCls = "overview-active";

    private container: HTMLElement;
    protected qty: HTMLElement;
    protected image: HTMLElement;

    protected items: IOverviewItem[] = [];

    constructor(protected messageHub: MessageHub, protected state: StateService) {
        super();
        this.initialize();
    }

    public update(s: IApplicationState): void {
        if (s.view.showOverview && s.diff.overviewChanged) { // Live overview updates as user edits drawing
            this.toggle(true);
        }
    }

    private async initialize(): Promise<void> {
        this.image = document.querySelector(".overview-mode > table > thead > tr > td:nth-child(1)");
        this.container = await getElementAsync("overview-body");
        this.qty = await getElementAsync("overview-qty");

        this.messageHub.subscribe(Events.wizard, () => this.toggle(false));
        this.messageHub.subscribe(Events.overview, () => this.toggle(true));
        this.messageHub.subscribe(Events.hoverItem, () => this.hoverItem());
    }

    private hoverItem(): void {
        const id = this.state.view.hoveredItem?.movable?.id;
        const partId = this.state.view.hoveredItem?.movable && this.state.view.hoveredItem?.partId;

        this.items.forEach(row => {
            const hovered = row.movables.some(x => x.item.id === id && x.partId === partId);
            if (hovered) {
                row.rowElement.classList.add(this.hoverCls);

            } else {
                row.rowElement.classList.remove(this.hoverCls);
            }
        });
    }

    protected toggle(showOverview: boolean): void {
        this.state.view.showOverview = showOverview;
        const step = currentStep();

        if (step !== Steps.stepControl) {
            this.state.design.movableItems.forEach(x => x.data.color = null);
        }

        if (showOverview) {
            this.items = this.getListItems();
            this.showList(this.items, step);

            document.querySelectorAll<HTMLElement>(".overview-mode").forEach(x => x.style.display = "");
            document.querySelectorAll<HTMLElement>(".wizard-mode").forEach(x => x.style.display = "none");

        } else {
            document.querySelectorAll<HTMLElement>(".overview-mode").forEach(x => x.style.display = "none");
            document.querySelectorAll<HTMLElement>(".wizard-mode").forEach(x => x.style.display = "");
        }
    }

    protected abstract getListItems(): IOverviewItem[];

    protected showList(items: IOverviewItem[], step: string): void {
        this.container.querySelectorAll("tr").forEach(element => {
            this.container.removeChild(element);
        });

        for (const item of items) {
            let labelHtml = "";
            for (const label of item.labels) {
                labelHtml += `<span class="prop-label" title="${label.title}" style="background-color: ${label.color || "#FFF"}">${label.text}</span>`;
            }

            const row =
                `<td>
                    <img src="${item.image}" alt="">
                </td>
                <td>
                    <div>
                        <p>${item.description}</p>
                        <p>${labelHtml}</p>
                    </div>
                </td>
                <td>${item.qty != null ? item.qty : ""}</td>
                `;

            const r = document.createElement("tr");
            r.innerHTML = row;
            r.addEventListener("mouseenter", () => this.enter(item.movables));
            r.addEventListener("mouseleave", () => this.leave(item.movables));

            this.container.appendChild(r);
            item.rowElement = r;

            if (step !== Steps.stepControl) {
                item.movables.forEach(x => x.item.data.color = [item.highlightColor]);
            }
        }

        this.image.innerHTML = "IMAGE";
        this.qty.innerHTML = "QUANTITY";

        if (step === Steps.stepControl) {
            this.setPowerStepLabels();
        }
    }

    protected abstract setPowerStepLabels(): void;

    private enter(ids: ItemPart[]): void {
        // If an item is included twice in ids (Part_01 and Part_02) convert the two entries into a sigle one withou partId
        const grouped = ids.reduce((acc, cur) => {
            const ex = acc.find(x => x.item.id === cur.item.id);
            if (!ex) {
                acc.push({ ...cur });
            } else {
                ex.partId = undefined;
            }
            return acc;
        }, new Array<ItemPart>());

        grouped.forEach(ip => {
            ip.item.highlight = true;
            ip.item.highlightPart = ip.partId;
        });
    }

    private leave(ids: ItemPart[]): void {
        ids.forEach(ip => {
            ip.item.highlight = false;
            ip.item.highlightPart = undefined;
        });
    }
}