import { BaseActionController } from "./BaseActionController";
import { StateService } from "../../../state/StateService";
import { IToolbarActionOptions } from "./IToolbarActionOptions";
import { AvailableActions } from "../../../models/common/Enums";
import { deleteMovables } from "../../../state/actions/deleteMovables";

export class DeleteController extends BaseActionController {

    public static $inject = ["StateService"];

    constructor(private state: StateService, options: IToolbarActionOptions) {
        super(options);
        this.defaultInitialize();
    }

    public execute(): void {
        const selected = this.state.design.movableItems.filter(x => x.selected).map(x => x.id);
        deleteMovables(selected, true);
    }

    public update(): void {
        const enabled = this.state.view.isActionAvailable(AvailableActions.Delete) && this.state.design?.movableItems.some(x => x.selected);
        this.element.classList.toggle("disabled", !enabled);
    }
}