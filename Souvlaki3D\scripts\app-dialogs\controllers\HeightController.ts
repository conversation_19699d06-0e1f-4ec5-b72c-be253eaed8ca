import { IApplicationSettings } from "../../models/infrastructure/IApplicationSettings";
import { MovableItem } from "../../models/state/MovableItem";
import { Events } from "../../models/infrastructure/Events";
import { MessageHub } from "../../infrastructure/MessageHub";
import { StateService } from "../../state/StateService";
import { PlacementMode } from "../../models/common/Enums";
import { MovablesLogic } from "../../logic/MovablesLogic";
import { BaseController } from "./BaseController";
import { NumericRangeController } from "./NumericRangeController";

export class HeightController extends BaseController {

    public static $inject = ["MovablesLogic", "StateService", "MessageHub", "settings"];

    private m: MovableItem;
    private numericRangeController: NumericRangeController;

    constructor(private movablesLogic: MovablesLogic, private state: StateService, messageHub: MessageHub, private settings: IApplicationSettings) {
        super();
        messageHub.subscribe<MovableItem>(Events.requestHeight, m => this.requestHeight(m));
    }

    private requestHeight(m: MovableItem): void {
        const d = this.state.design;
        const isInDrawing = this.state.design.movableItems.includes(m);
        const horizontal = (m.behavior.placementOptions || []).includes(PlacementMode.Horizontal);
        if (isInDrawing && horizontal && Math.round(m.ceilingDistance) !== 0 && (m.placementMode & (PlacementMode.Ceiling | PlacementMode.Horizontal | PlacementMode.ExternalWalls))) {
            const length = this.movablesLogic.getConnectedItems(d.movableItems, m)?.length;
            if (length === 1) {
                this.state.saveHistory();

                const min = m.behavior.minimumCeilingDistance || 0;
                const max = Math.min(Math.round(d.height - d.roofWidth - d.baseHeight - m.height - 1), ...[this.settings.view.maxSuspension, m.behavior.maximumCeilingDistance].filter(x => x != null));
                const h = Math.min(Math.round(m.ceilingDistance), max);

                this.showDialog(m, min, max, h);
            }
        }
    }

    private showDialog(m: MovableItem, min: number, max: number, h: number): void {
        if (!this.numericRangeController) {
            this.initialize();
        }

        this.m = m;
        this.numericRangeController.setValues(min, max, h);
        this.setMovableHeight(h);

        $("#height-modal").modal("show");
    }

    private initialize() {
        const title = document.querySelector<HTMLElement>("#height-title");
        const input = document.querySelector<HTMLInputElement>("#heightInput");
        const range = document.querySelector<HTMLInputElement>("#heightRange");
        this.numericRangeController = new NumericRangeController(range, input, title, 0, 0, 0, this.state.view.unitType, heightMm => this.setMovableHeight(heightMm));

        const $heightModal = $("#height-modal");
        $heightModal.modal({ show: false });
        document.getElementById("heightSubmit").addEventListener("click", () => $heightModal.modal("hide"));
        document.querySelector("#height-modal .close").addEventListener("click", () => $heightModal.modal("hide"));
    }

    private setMovableHeight(heightMm: number) {
        this.m.y = this.state.design.height - this.state.design.roofWidth - heightMm - this.m.height / 2;
        this.m.ceilingDistance = heightMm;
    }
}