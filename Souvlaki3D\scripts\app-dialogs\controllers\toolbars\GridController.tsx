import { BaseActionController } from "./BaseActionController";
import { StateService } from "../../../state/StateService";
import { IToolbarActionOptions } from "./IToolbarActionOptions";
import { pushView } from "../../../state/actions/pushView";
import { Display } from "../../../models/common/Enums";

export class GridController extends BaseActionController {

    public static $inject = ["StateService"];

    constructor(private state: StateService, options: IToolbarActionOptions) {
        super(options);
        this.defaultInitialize();
    }

    public execute(): void {
        const showPlane = !this.state.view.showPlane;
        pushView({showPlane});
        this.update();
    }

    public update(): void {
        this.element.classList.toggle("disabled", this.state.view.displayMode === Display.PerspectiveView);
    }
}