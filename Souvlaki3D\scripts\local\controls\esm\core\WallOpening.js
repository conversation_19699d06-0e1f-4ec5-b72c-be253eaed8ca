import { Line3D, Vec3 } from "@immugio/three-math-extensions";
import { getWallPointsWithRidgesIntersections } from "../building/getWallPointsWithRidgesIntersections";
/**
 * A collection of 3d lines representing a wall opening
 */
export class WallOpening {
    topLines;
    bottomLine;
    wallLineAtBottomOfOpening;
    constructor(topLines, bottomLine, wallLineAtBottomOfOpening) {
        this.topLines = topLines;
        this.bottomLine = bottomLine;
        this.wallLineAtBottomOfOpening = wallLineAtBottomOfOpening;
    }
    #maxMergeDistance = 0.1;
    #maxMergeParallelDifference = 0.001;
    static fromMovables(wallStart, wallEnd, movables, slopes) {
        const wallLine = new Line3D(wallStart, wallEnd);
        const openings = movables.map(m => WallOpening.create(wallLine, m, slopes)).filter(w => w);
        return WallOpening.merge(openings);
    }
    static create(wallLine, movable, slopes) {
        const tolerance = 1; // Chosen arbitrarily, in reality the tolerance could be much smaller
        const wallLineAtBottomOfOpening = wallLine.clone();
        wallLineAtBottomOfOpening.start.y = movable.bottom;
        wallLineAtBottomOfOpening.end.y = movable.bottom;
        // Item center projected on wall line. The line distance from ground is different that movable distance from ground.
        const itemCenter = wallLineAtBottomOfOpening.closestPointToPoint(new Vec3(movable.calculatedX, wallLineAtBottomOfOpening.start.y, movable.calculatedY), false, new Vec3());
        const bottomLine = wallLineAtBottomOfOpening.clone().setCenter(itemCenter).setLength(movable.width);
        const closesPointToStart = wallLineAtBottomOfOpening.closestPointToPoint(bottomLine.start, true, new Vec3());
        if (closesPointToStart.distanceTo(bottomLine.start) > tolerance) {
            bottomLine.start.copy(closesPointToStart);
        }
        const closesPointToEnd = wallLineAtBottomOfOpening.closestPointToPoint(bottomLine.end, true, new Vec3());
        if (closesPointToEnd.distanceTo(bottomLine.end) > tolerance) {
            bottomLine.end.copy(closesPointToEnd);
        }
        if (bottomLine.length < tolerance) {
            return null;
        }
        const ridgesProjectedOnPlan = slopes.getRidges().filter(x => x).map(x => x.onPlan());
        const topPointsWithRidgesIntersections = getWallPointsWithRidgesIntersections([bottomLine.start.onPlan(), bottomLine.end.onPlan()], ridgesProjectedOnPlan);
        const topPoints = slopes.projectPointsOnPlane(topPointsWithRidgesIntersections[0]);
        topPoints.forEach(p => p.y = Math.min(movable.top, p.y - 1));
        const topLines = Line3D.fromPolygon(topPoints);
        return new WallOpening(topLines, bottomLine, wallLineAtBottomOfOpening);
    }
    static merge(openings) {
        if (openings.length < 2) {
            return openings;
        }
        const toProcess = openings.slice();
        const result = [];
        while (toProcess.length > 0) {
            const current = toProcess.pop();
            let joinedLine;
            for (let i = 0; i < result.length; i++) {
                const other = result[i];
                joinedLine = current.join(other);
                if (joinedLine) {
                    result.splice(i, 1);
                    toProcess.push(joinedLine);
                    break;
                }
            }
            if (!joinedLine) {
                result.push(current);
            }
        }
        return result;
    }
    join(other) {
        if (this.topLines.length === 1 && other.topLines.length === 1) {
            const mergedBottom = this.bottomLine.joinLine(other.bottomLine, this.#maxMergeDistance, this.#maxMergeParallelDifference);
            if (mergedBottom) {
                const mergedTop = this.topLines[0].joinLine(other.topLines[0], this.#maxMergeDistance, this.#maxMergeParallelDifference);
                if (mergedTop) {
                    return new WallOpening([mergedTop], mergedBottom, this.wallLineAtBottomOfOpening);
                }
            }
        }
        return null;
    }
    get outline() {
        return [
            this.bottomLine.start,
            this.topLines[0].start,
            ...this.topLines.map(x => x.end),
            this.bottomLine.end,
        ];
    }
    getVerticalLines(minimumDistanceToCorner) {
        return [new Line3D(this.topLines[0].start, this.bottomLine.start), new Line3D(this.topLines.at(-1).end, this.bottomLine.end)]
            .filter(x => x.end.distanceTo(this.wallLineAtBottomOfOpening.start) > minimumDistanceToCorner &&
            x.end.distanceTo(this.wallLineAtBottomOfOpening.end) > minimumDistanceToCorner);
    }
}
