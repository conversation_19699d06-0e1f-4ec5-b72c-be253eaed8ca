import { BaseActionController } from "./BaseActionController";
import { StateService } from "../../../state/StateService";
import { ViewService } from "../../services/ViewService";
import { IToolbarActionOptions } from "./IToolbarActionOptions";
import { Display } from "../../../models/common/Enums";

export class FrontViewController extends BaseActionController {

    public static $inject = ["StateService", "ViewService"];

    constructor(private state: StateService, private viewService: ViewService, options: IToolbarActionOptions) {
        super(options);
        this.defaultInitialize();
    }

    public execute(): void {
        this.viewService.rotateView(0, 0, Display.FrontView);
    }

    public update(): void {
        const isInPosition = this.state.view.phi === 0 && this.state.view.theta === 0 && this.state.view.displayMode === Display.FrontView;
        this.element.classList.toggle("disabled", isInPosition);
    }
}