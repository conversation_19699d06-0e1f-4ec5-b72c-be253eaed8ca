export class DraggableIcon {

    private icon: HTMLImageElement;

    constructor(template: HTMLImageElement) {
        this.icon = document.body.appendChild(template.cloneNode(true) as HTMLImageElement);
        this.icon.style.opacity = "0.5";
        this.icon.style.pointerEvents = "none";
        this.icon.style.position = "absolute";
    }

    public setPosition(e: MouseEvent): this {
        this.icon.style.top = `${e.clientY - this.icon.height / 2}px`;
        this.icon.style.left = `${e.clientX - this.icon.width / 2}px`;
        return this;
    }

    public static fromEvent(e: MouseEvent): DraggableIcon {
        return e.target instanceof HTMLImageElement ? new DraggableIcon(e.target).setPosition(e) : null;
    }

    public remove(): void {
        this.icon.remove();
    }
}