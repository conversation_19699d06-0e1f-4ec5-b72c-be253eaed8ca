import { MessageHub } from "../../infrastructure/MessageHub";
import { Events } from "../../models/infrastructure/Events";
import { DesignState } from "../../models/state/DesignState";
import { setCeilingDistance } from "../../state/actions/setCeilingDistance";

export class CeilingDistanceController {

    private rodSize: HTMLSelectElement;

    constructor() {
        this.rodSize = document.querySelector("#rodSize");
        this.rodSize.addEventListener("change", () => {
            const value = parseInt(this.rodSize.value);
            setCeilingDistance(value, true);
        });
        MessageHub.subscribe([Events.pushState, Events.setState, Events.dragResize], (d: DesignState) => this.onStateChanged(d));
    }

    private onStateChanged(d: DesignState): void {
        const currentCeilingDistance = d.movableItems.find(x => !x.parentId)?.ceilingDistance?.toString();
        if (currentCeilingDistance && Array.from(this.rodSize.options).some(x => x.value === currentCeilingDistance)) {
            this.rodSize.value = currentCeilingDistance;
        }
    }
}