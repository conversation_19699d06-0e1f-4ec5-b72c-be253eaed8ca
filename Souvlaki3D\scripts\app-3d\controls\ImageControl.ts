import { CanvasControl } from "./CanvasControl";
import { IApplicationState } from "../../models/state/IApplicationState";
import { Direction } from "../../models/common/Enums";
import { DoubleSide, Mesh, MeshBasicMaterial, PlaneGeometry, Texture, TextureLoader } from "three";
import { getWallsBoundingPolygon } from "../../logic/helpers/building/getWallsBoundingPolygon";

export class ImageControl extends CanvasControl {

    private texture: Texture;
    private plane: Mesh;

    public update(s: IApplicationState): void {
        if (!s.diff.structureChanged && !s.diff.imageOptionsChanged && !s.diff.offsetChanged && !s.diff.closestElevationsChanged) {
            return;
        }

        this.object3D.remove(this.plane);
        if (!s.view.image) {
            return;
        }

        if (s.diff.imageOptionsChanged) {
            this.texture = new TextureLoader().load(s.view.image.url, () => this.createPlane(s), null, (e) => ImageControl.error(e));

        } else {
            this.createPlane(s);
        }
    }

    private createPlane(s: IApplicationState): void {
        if (!this.texture || !this.texture.image) {
            return;
        }

        let width = s.design.extWidth() - s.design.wallThickness * 2;
        let height = s.design.extDepth() - s.design.wallThickness * 2;

        const planeRatio = width / height;
        const imageRatio = this.texture.image.width / this.texture.image.height;

        if (imageRatio < planeRatio) { // Reduce width
            width = height * imageRatio;

        } else { // Reduce height
            height = width / imageRatio;
        }

        const material = new MeshBasicMaterial({ map: this.texture, side: DoubleSide });
        this.plane = new Mesh(
            new PlaneGeometry(width, height),
            material
        );

        this.plane.rotation.x = -Math.PI / 2;

        this.position(s);
        this.object3D.add(this.plane);
    }

    private position(s: IApplicationState): void {
        const outline = getWallsBoundingPolygon(s.design.walls).contour;

        const xPoints = outline.map(p => p.x);
        const minX = Math.min(...xPoints);
        const maxX = Math.max(...xPoints);
        const absLeft = minX - s.view.offset.x;
        const absRight = maxX - s.view.offset.x;
        this.plane.position.x = (absRight + absLeft) / 2;

        const yPoints = outline.map(p => p.y);
        const minZ = Math.min(...yPoints);
        const maxZ = Math.max(...yPoints);
        const absBack = minZ - s.view.offset.y;
        const absFront = maxZ - s.view.offset.y;
        this.plane.position.z = (absFront + absBack) / 2;

        this.plane.position.y = (s.view.closesElevations & Direction.Top)
            ? s.design.baseHeight + 1
            : s.design.height - s.design.roofWidth - 1;
    }

    private static error(e: ErrorEvent): void {
        console.log("Error while loading background image", e);
    }
}