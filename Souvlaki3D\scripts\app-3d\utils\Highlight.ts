import { EdgesGeometry, LineBasicMaterial, LineSegments, Mesh, MeshBasicMaterial, Object3D } from "three";
import { settings } from "../../configurations/setup_global";

export class Highlight {

    private static line = new LineBasicMaterial({ color: settings.textures.highlight.color, opacity: settings.textures.highlight.opacity + .1, transparent: true, linewidth: 1, depthTest: false });
    private static trans = new MeshBasicMaterial({ color: settings.textures.highlight.color, opacity: settings.textures.highlight.opacity, transparent: true, depthTest: false });

    public static create(o: Object3D): Object3D {
        const copy = o.clone(true);
        copy.traverse(x => {

            if (x instanceof Mesh && x.parent) {
                x.material = this.trans;

                const geo = new EdgesGeometry((x as Mesh).geometry, 20);
                const edges = new LineSegments(geo, this.line);
                edges.position.copy(x.position);
                edges.scale.copy(x.scale);
                edges.rotation.copy(x.rotation);
                edges.name = x.name;

                x.parent.add(edges);
            }
        });

        return copy;
    }
}